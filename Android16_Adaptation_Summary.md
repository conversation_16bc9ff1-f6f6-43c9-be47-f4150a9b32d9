# Android 16 适配总结

本项目已完成Android 16的核心适配工作，直接修改现有代码，无需额外工具类。

## 1. 基本配置更新

### 1.1 build.gradle
- 更新 `targetSdk` 从 35 到 36 (Android 16 BAKLAVA)
- 更新 `compileSdk` 从 35 到 36

### 1.2 AndroidManifest.xml
- 添加 `android:enableOnBackInvokedCallback="true"` 启用预测性返回手势
- 添加 `android:pageSizeCompat="false"` 禁用16KB页大小兼容模式
- 将所有Activity的 `screenOrientation` 从 `portrait` 改为 `unspecified` 支持自适应布局

## 2. 反射适配

### 2.1 RomUtils.kt - checkOp方法
```kotlin
// 原来只使用反射
val method = clazz.getDeclaredMethod("checkOp", ...)
method.invoke(manager, ...)

// 现在统一使用反射，但优先尝试更新的方法
val clazz: Class<*> = AppOpsManager::class.java
val method = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
    // Android 6+ 尝试使用checkOpNoThrow
    try {
        clazz.getMethod("checkOpNoThrow", Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java)
    } catch (e: NoSuchMethodException) {
        // 如果没有checkOpNoThrow，降级到checkOp
        clazz.getDeclaredMethod("checkOp", Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java)
    }
} else {
    // Android 19+ 使用checkOp
    clazz.getDeclaredMethod("checkOp", Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java)
}
return AppOpsManager.MODE_ALLOWED === method.invoke(manager, op, Binder.getCallingUid(), context.packageName) as Int
```

### 2.2 RomUtils.kt - getAllOPSField方法
```kotlin
// 优先使用已知常量字符串，避免反射
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
    val knownOps = mapOf(
        "OPSTR_COARSE_LOCATION" to AppOpsManager.OPSTR_COARSE_LOCATION,
        "OPSTR_FINE_LOCATION" to AppOpsManager.OPSTR_FINE_LOCATION,
        // ... 更多常量
    )
} else {
    // 低版本使用反射
}
```

### 2.3 ThreadUtil.kt - getProcessName方法
```kotlin
// 优先使用Android 9+的公开API
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
    android.app.Application.getProcessName() ?: ""
} else {
    // 低版本使用反射
    val currentProcessNameMethod = Class.forName("android.app.ActivityThread")
        .getMethod("currentProcessName")
    currentProcessNameMethod.invoke(null)?.toString() ?: ""
}
```

### 2.4 LiveDataBus.java - observe方法
```java
// Android 16兼容：避免使用反射
if (android.os.Build.VERSION.SDK_INT >= 36) { // Android 16
    // 不使用反射，跳过hook
    return;
}

try {
    hook(observer); // 仅在低版本使用反射
} catch (Exception e) {
    e.printStackTrace();
}
```

## 3. Edge-to-Edge适配

### 3.1 BaseActivity.java
直接在BaseActivity中添加enableEdgeToEdge()方法：

```java
private void enableEdgeToEdge() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        // Android 11+ 实现
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
        getWindow().setNavigationBarColor(android.graphics.Color.TRANSPARENT);
        
        WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        controller.setAppearanceLightStatusBars(true);
        controller.setAppearanceLightNavigationBars(true);
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        // Android 5+ 实现
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
        // ... 更多设置
    }
}
```

在onCreate()中调用：
```java
@Override
protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    // Android 16 Edge-to-Edge适配
    enableEdgeToEdge();
    // ... 其他代码
}
```

## 4. 预测性返回手势适配

### 4.1 StarryBaseActivity.kt
直接添加预测性返回手势处理：

```kotlin
private fun setupPredictiveBack() {
    if (this is ComponentActivity && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        }
        this.onBackPressedDispatcher.addCallback(this, callback)
    }
}

protected open fun handleBackPressed() {
    ActivityUtils.finishActivity(this)
}
```

在onCreate()中调用：
```kotlin
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    // ... 其他代码
    // Android 16 预测性返回手势适配
    setupPredictiveBack()
}
```

## 5. 自适应布局适配

### 5.1 AndroidManifest.xml
将所有Activity的屏幕方向从固定改为自适应：

```xml
<!-- 原来 -->
android:screenOrientation="portrait"

<!-- 现在 -->
android:screenOrientation="unspecified"
```

这样在大屏设备上就不会强制限制屏幕方向了。

## 6. 主要修改文件

1. **app/build.gradle** - 更新targetSdk和compileSdk
2. **AndroidManifest.xml** - 添加Android 16配置，修改屏幕方向
3. **BaseActivity.java** - 添加Edge-to-Edge适配
4. **StarryBaseActivity.kt** - 添加预测性返回手势适配
5. **RomUtils.kt** - 优化反射使用，优先使用公开API
6. **ThreadUtil.kt** - 优化进程名获取，优先使用公开API
7. **LiveDataBus.java** - 在Android 16上跳过反射hook

## 7. 适配原则

1. **向后兼容** - 所有修改都保持与低版本Android的兼容性
2. **优先公开API** - 能用公开API就不用反射
3. **渐进式降级** - 高版本用新API，低版本用旧方法
4. **最小化修改** - 直接修改现有代码，不引入额外复杂性

## 8. 测试建议

1. 在Android 16设备上测试所有功能
2. 确保在低版本Android上仍能正常工作
3. 测试Edge-to-Edge显示效果
4. 测试返回手势在各种场景下的表现
5. 验证反射相关功能是否正常

## 9. 注意事项

- Android 16强制启用Edge-to-Edge，无法关闭
- 预测性返回手势默认启用，必须使用OnBackPressedCallback
- 大屏设备上不允许强制屏幕方向
- 反射使用受到更严格限制，优先使用公开API

这样的适配方式简单直接，不需要额外的工具类，维护成本低，同时保证了功能的完整性和兼容性。
