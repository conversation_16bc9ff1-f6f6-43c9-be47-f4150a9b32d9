# Android 16 适配指南

本项目已完成Android 16的适配工作，主要包括以下几个方面：

## 1. 基本配置更新

### 1.1 targetSdk更新
- 将`targetSdk`从35更新到36 (Android 16 BAKLAVA)
- 将`compileSdk`从35更新到36

### 1.2 AndroidManifest.xml配置
- 添加`android:enableOnBackInvokedCallback="true"`启用预测性返回手势
- 添加`android:pageSizeCompat="false"`禁用16KB页大小兼容模式
- 将所有Activity的`screenOrientation`从`portrait`改为`unspecified`以支持自适应布局

## 2. 反射适配

### 2.1 问题描述
Android 16对反射使用进行了更严格的限制，原有的反射代码可能无法正常工作。

### 2.2 解决方案
创建了`Android16CompatUtils`工具类来处理反射相关的兼容性问题：

- **RomUtils.kt**: 使用兼容的方式检查AppOps权限
- **ThreadUtil.kt**: 使用Android 9+的公开API获取进程名
- **LiveDataBus**: 创建了`Android16LiveDataBus`避免反射访问LiveData内部字段

### 2.3 主要变更
```kotlin
// 原来的反射调用
val method = clazz.getDeclaredMethod("checkOp", ...)
method.invoke(manager, ...)

// 现在的兼容调用
Android16CompatUtils.checkOp(context, op)
```

## 3. Edge-to-Edge适配

### 3.1 强制启用
Android 16强制启用Edge-to-Edge，无法再通过`windowOptOutEdgeToEdgeEnforcement`关闭。

### 3.2 适配工具
创建了`EdgeToEdgeUtils`工具类：
- 自动启用Edge-to-Edge
- 处理系统窗口插入
- 适配状态栏和导航栏

### 3.3 使用方法
```kotlin
// 在Activity的onCreate中调用
EdgeToEdgeUtils.enableEdgeToEdge(this)

// 为View设置系统窗口插入处理
EdgeToEdgeUtils.applySystemWindowInsets(view)
```

## 4. 预测性返回手势适配

### 4.1 默认启用
Android 16默认启用预测性返回手势，必须使用`OnBackPressedCallback`处理返回事件。

### 4.2 适配工具
创建了`PredictiveBackUtils`工具类：
- 统一的返回手势处理
- 支持条件性返回
- 支持多级返回处理

### 4.3 使用方法
```kotlin
// 设置预测性返回手势
PredictiveBackUtils.setupPredictiveBack(this) {
    // 处理返回事件
    finish()
}
```

## 5. 自适应布局适配

### 5.1 强制要求
Android 16在大屏设备上强制自适应布局，不允许限制横竖屏朝向。

### 5.2 适配工具
创建了`AdaptiveLayoutUtils`工具类：
- 检测屏幕尺寸分类
- 提供布局建议
- 支持响应式设计

### 5.3 使用方法
```kotlin
// 检查是否为大屏设备
if (AdaptiveLayoutUtils.isLargeScreen(this)) {
    // 使用大屏布局
}

// 获取推荐的列数
val columnCount = AdaptiveLayoutUtils.getRecommendedColumnCount(this)
```

## 6. Intent安全适配

### 6.1 新限制
Android 16对Intent使用增加了限制：
- 显式Intent必须与目标组件的Intent Filter相匹配
- 所有Intent都必须指定action

### 6.2 适配工具
创建了`IntentSecurityUtils`工具类：
- 安全的Intent创建和启动
- Intent验证功能
- 常用Intent模板

### 6.3 使用方法
```kotlin
// 安全地启动Activity
IntentSecurityUtils.startActivitySafely(context, intent)

// 创建安全的显式Intent
val intent = IntentSecurityUtils.createSafeExplicitIntent(
    context, 
    TargetActivity::class.java
)
```

## 7. 综合适配管理

### 7.1 适配管理器
创建了`Android16AdaptationManager`统一管理所有适配工作：
- 一键完成所有适配
- 适配状态检查
- 生成适配报告

### 7.2 使用方法
```kotlin
// 在Activity中执行完整适配
Android16AdaptationManager.adaptActivity(this)

// 检查适配状态
val status = Android16AdaptationManager.checkAdaptationStatus(this)

// 生成适配报告
val report = Android16AdaptationManager.generateAdaptationReport(this)
```

## 8. 测试建议

### 8.1 功能测试
- 测试所有反射相关功能是否正常工作
- 验证Edge-to-Edge显示效果
- 测试返回手势在各种场景下的表现
- 在不同屏幕尺寸设备上测试布局适配

### 8.2 兼容性测试
- 在Android 16设备上进行完整测试
- 确保在低版本Android上仍能正常工作
- 测试Intent启动的各种场景

### 8.3 性能测试
- 验证适配后的性能表现
- 检查内存使用情况
- 测试启动速度

## 9. 注意事项

### 9.1 向后兼容
所有适配代码都保持了向后兼容性，在低版本Android上会自动降级到原有实现。

### 9.2 渐进式适配
可以根据需要逐步启用各项适配功能，不需要一次性全部启用。

### 9.3 持续更新
随着Android 16的正式发布，可能还会有新的适配要求，需要持续关注和更新。

## 10. 相关文件

### 10.1 新增文件
- `Android16CompatUtils.kt` - 反射兼容工具
- `Android16LiveDataBus.kt` - LiveDataBus兼容版本
- `EdgeToEdgeUtils.kt` - Edge-to-Edge适配工具
- `PredictiveBackUtils.kt` - 预测性返回手势适配工具
- `AdaptiveLayoutUtils.kt` - 自适应布局适配工具
- `IntentSecurityUtils.kt` - Intent安全适配工具
- `Android16AdaptationManager.kt` - 综合适配管理器

### 10.2 修改文件
- `app/build.gradle` - 更新targetSdk和compileSdk
- `AndroidManifest.xml` - 添加Android 16配置
- `BaseActivity.java` - 添加适配调用
- `StarryBaseActivity.kt` - 添加预测性返回手势
- `RomUtils.kt` - 使用兼容工具类
- `ThreadUtil.kt` - 使用兼容工具类

## 11. 总结

本次Android 16适配工作全面覆盖了官方文档中提到的主要变更点，通过创建专门的工具类和管理器，确保了适配的完整性和可维护性。所有适配代码都经过了仔细设计，既满足了Android 16的新要求，又保持了与低版本的兼容性。
