#!/bin/bash

# Android 16适配测试脚本

echo "=== Android 16 适配测试脚本 ==="
echo ""

# 检查ADB连接
echo "1. 检查ADB连接..."
adb devices
if [ $? -ne 0 ]; then
    echo "错误: ADB未连接或设备未找到"
    exit 1
fi

# 构建项目
echo ""
echo "2. 构建项目..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

# 安装APK
echo ""
echo "3. 安装APK..."
adb install -r app/build/outputs/apk/debug/app-debug.apk
if [ $? -ne 0 ]; then
    echo "错误: APK安装失败"
    exit 1
fi

# 启动测试Activity
echo ""
echo "4. 启动Android 16测试Activity..."
adb shell am start -n com.czur.czurwma/.Android16TestActivity

# 获取设备信息
echo ""
echo "5. 设备信息:"
echo "   Android版本: $(adb shell getprop ro.build.version.release)"
echo "   API级别: $(adb shell getprop ro.build.version.sdk)"
echo "   设备型号: $(adb shell getprop ro.product.model)"
echo "   屏幕密度: $(adb shell wm density)"
echo "   屏幕尺寸: $(adb shell wm size)"

# 检查日志
echo ""
echo "6. 查看相关日志 (按Ctrl+C停止):"
echo "   正在监听Android 16适配相关日志..."
adb logcat -s "Android16*" "EdgeToEdgeUtils" "PredictiveBackUtils" "AdaptiveLayoutUtils" "IntentSecurityUtils"

echo ""
echo "=== 测试完成 ==="
