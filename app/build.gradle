plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.czur.czurwma'
    compileSdk 35

    defaultConfig {
        applicationId "com.czur.czurwma"
        minSdk 26
        targetSdk 35
//        versionCode 3
//        versionName "1.0.3"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    signingConfigs {
        debug {
            //TODO OVERSEAS 海外签名
//            keyAlias 'czurrsa'
//            keyPassword 'changer'
//            storeFile file('keystore/czuroversea.jks')
//            storePassword 'changer'

            //TODO OVERSEAS 国内签名
            keyAlias 'czurwma'
            keyPassword 'changer'
            storeFile file('keystore/czuroversea.jks')
            storePassword 'changer'

            // 添加V1V2签名
            v1SigningEnabled true
            v2SigningEnabled true

        }

        release {
            //TODO OVERSEAS 海外签名
//            keyAlias 'czurrsa'
//            keyPassword 'changer'
//            storeFile file('keystore/czuroversea.jks')
//            storePassword 'changer'

            //TODO OVERSEAS 国内签名
            keyAlias 'czurwma'
            keyPassword 'changer'
            storeFile file('keystore/czuroversea.jks')
            storePassword 'changer'

            // 添加V1V2签名
            v1SigningEnabled true
            v2SigningEnabled true

        }


    }
//    room {
//        schemaDirectory "$projectDir/schemas"
//    }

    buildTypes {
        release {
            debuggable false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //是否清理无用资源
            shrinkResources false
            //是否启用zipAlign压缩
            zipAlignEnabled true
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            //true：启用混淆,false:不启用
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            shrinkResources false
            zipAlignEnabled false
//            signingConfig signingConfigs.release // TODO 先注释,不删除

            // 对调试 build 停用 Crashlytics
            ext.enableCrashlytics = false
            // 禁止自动生成 build ID
            ext.alwaysUpdateBuildId = false
            // 禁用PNG压缩。
            crunchPngs false
        }
    }
//    buildTypes {
//        release {
//            minifyEnabled false
//            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//        }
//    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        buildConfig true
        compose false
    }


    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java']
            resources.srcDirs = ['src/main/resources']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
        czur.res.srcDirs = ['src/domestic/res']
        czur.java.srcDirs = ['src/domestic/java']
        czur.jniLibs.srcDirs = ['src/domestic/jniLibs']
        czur.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        overseas.res.srcDirs = ['src/overseas/res']
        overseas.java.srcDirs = ['src/overseas/java']
        overseas.manifest.srcFile 'src/overseas/AndroidManifest.xml'


    }


    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    flavorDimensions "default"

    productFlavors {
        czur {
            applicationId "com.czur.czurwma"
            manifestPlaceholders = [appName            : "com.czur.czurwma",
                                    UMENG_CHANNEL_VALUE: "czur"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        overseas {
            applicationId "com.czur.global.czurwma"
            manifestPlaceholders = [appName            : "com.czur.global.czurwma",
                                    UMENG_CHANNEL_VALUE: "overseas"]
            versionCode rootProject.ext.android.versionCodeOverSea
            versionName rootProject.ext.android.versionNameOverSea
            dimension "default"
        }
    }
    lint {
        abortOnError false
        checkDependencies true
        checkReleaseBuilds false
    }


    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def newName
            newName = "${applicationId}-" + variant.buildType.name + "-${variant.versionName}" + ".apk"
            outputFileName = new File(newName)
        }
        def buildType = variant.buildType.name
        def productFlavor = variant.productFlavors[0].name
        def isOverseas = productFlavor.contains('overseas')
        if (!isOverseas) {
            productFlavor = 'domestic'
            buildConfigField('long', 'CLIENT_TIMEOUT', "8000")//修改接口连接超时时间
        } else {
            buildConfigField('long', 'CLIENT_TIMEOUT', "15000")
        }

        buildConfigField('boolean', 'IS_OVERSEAS', isOverseas.toString())

        def appName = rootProject.ext.get("config").get(productFlavor).get("appName")
        def checkUpdateUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("checkUpdateUrl")
        def privacyUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("privacyUrl")
        def termsUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("termsUrl")
        def feedBackUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("feedBackUrl")
        resValue("string", "app_name", appName)
        buildConfigField('String', 'CHECK_UPDATE_URL', checkUpdateUrl)
        buildConfigField('String', 'PRIVACY_AGREEMENT', privacyUrl)
        buildConfigField('String', 'TERMS_URL', termsUrl)
        buildConfigField('String', 'FEEDBACK_URL', feedBackUrl)// 意见反馈
    }


    kotlinOptions {
        jvmTarget = '1.8'
    }


}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.1'
    implementation 'androidx.activity:activity-compose:1.5.1'
    implementation platform('androidx.compose:compose-bom:2022.10.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation platform('androidx.compose:compose-bom:2022.10.00')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
    //工具类
    implementation 'com.blankj:utilcodex:1.31.1'

    implementation "androidx.appcompat:appcompat:1.6.1"

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    //Gson
    implementation 'com.google.code.gson:gson:2.10'
    //EventBus
    implementation 'org.greenrobot:eventbus:3.3.1'

    // pinyin4j
    implementation 'com.github.open-android:pinyin4j:2.5.0'
    //下拉刷新
    implementation 'com.baoyz.pullrefreshlayout:library:1.2.0'

    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'

    implementation  'io.github.scwang90:refresh-layout-kernel:2.1.0'      //核心必须依赖
    implementation  'io.github.scwang90:refresh-header-classics:2.1.0'    //经典刷新头

    //刷新控件
//    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.2'

    // CZURLog日志插件
    implementation 'com.gitee.czur_dl:czurutils:v1.7.3'

    //二维码扫描
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zxing:1.3.8'
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zbar:1.3.8'


    //Weak Handler
    implementation 'com.badoo.mobile:android-weak-handler:1.1'

    //OkHttp3
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'

    // recyclerview
    implementation 'androidx.recyclerview:recyclerview:1.2.1'

    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.12.5'

    def work_version = "2.8.0"
    // optional - RxJava2 support
    implementation "androidx.work:work-rxjava2:$work_version"

    // optional - GCMNetworkManager support
    implementation "androidx.work:work-gcm:$work_version"

    // optional - Test helpers
    androidTestImplementation "androidx.work:work-testing:$work_version"

    // optional - Multiprocess support
    implementation "androidx.work:work-multiprocess:$work_version"

    //通过标签直接生成shape
    implementation 'com.noober.background:core:1.6.0'

    implementation "org.xerial:sqlite-jdbc:********"

    def room_version = "2.5.0"

    implementation "androidx.room:room-runtime:$room_version"
    annotationProcessor "androidx.room:room-compiler:$room_version"

    // To use Kotlin annotation processing tool (kapt)
    kapt "androidx.room:room-compiler:$room_version"

    //aliyun OSS
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.12'

    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

//    implementation 'com.jakewharton.disklrucache:disklrucache:2.0.2'
    implementation 'com.jakewharton:disklrucache:2.0.2'

}