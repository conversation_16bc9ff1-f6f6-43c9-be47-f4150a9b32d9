<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.VIBRATE" />


    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--允许获取网络信息状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--允许访问Wi-Fi网络状态信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--允许改变Wi-Fi连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--允许改变网络连接状态-->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!--相机 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <!--8.0安装 TODO OVERSEAS 海外需要注掉-->
<!--    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />-->

    <!-- Optional for location -->
    <!-- 在屏幕最顶部显示addview-->
    <uses-permission android:name="android.permissionmission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 用于开启 debug 版本的应用在6.0 系统上 层叠窗口权限 -->
    <!--    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />&lt;!&ndash; Android Q后台定位权限&ndash;&gt;-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />

    <!--  发送全屏通知-->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <!--  Android 13 通知权限的调整  -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"
        android:minSdkVersion="33"/>

<!--    <permission-group android:name="com.czur.global.czurwma.andpermission" />-->

    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <uses-permission android:name="android.permission.BLUETOOTH"/>

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <application
        android:name=".CzurWMAApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/app_logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/logo_round"
        android:supportsRtl="true"
        android:theme="@style/CZURAppTheme"
        tools:targetApi="31"
        android:networkSecurityConfig="@xml/network_security_config"
        >
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".eshare.EShareActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".eshare.EShareEmptyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            android:launchMode="standard"
            android:theme="@style/TranslucentTheme"/>
        <activity
            android:name=".eshare.EShareOpenPainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".eshare.EShareScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            /><activity
            android:name=".eshare.EShareMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".WebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />

        <activity
            android:name=".eshare.FindDeviceActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            android:launchMode="singleTop"
            />
        <activity android:name="com.czur.czurwma.AboutActivity" />
        <activity
            android:name=".eshare.transmitfile.TransmitFileUpLoadActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />

        <activity
            android:name=".eshare.UserFeedbackActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />

        <activity
            android:name=".eshare.transmitfile.TransmitFileDownloadActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".eshare.transmitfile.TransmitFileRootBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".eshare.transmitfile.TransmitChildFileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />

        <service
            android:name=".utils.downloadapk.DownloadApkService"
            android:enabled="true"
            android:exported="false" />

        <service android:name=".service.EShareHeartBeatService"
            android:foregroundServiceType="dataSync"
            android:priority="1000"
            />

        <!-- EShare Widget-->
        <receiver
            android:name=".eshare.EShareWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/eshare_widget_info" />
        </receiver>
    </application>

</manifest>