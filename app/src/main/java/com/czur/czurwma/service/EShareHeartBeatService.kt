package com.czur.czurwma.service

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.text.TextUtils
import android.widget.AutoCompleteTextView
import androidx.core.app.NotificationCompat
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.ServiceUtils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.eshare.engine.Constants
import com.czur.czurwma.eshare.engine.Constants.OFFLINE_WAIT_TIME
import com.czur.starry.device.file.server.TransmitFileHttpClient
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import com.google.gson.JsonParser
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.UnsupportedEncodingException
import java.nio.charset.StandardCharsets


class EShareHeartBeatService : Service() {
    private val TAG = "EShareHeartBeatService"
    private val applicationViewModel by lazy {
        (application as CzurWMAApplication).getEshareViewModel1()
    }

    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(this).device()
    }


    var hasRetriedFirst = false
    var hasRetriedSecond = false


    override fun onBind(intent: Intent?): IBinder? {
        logI("KeepAliveService onBind")
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        CoroutineScope(Dispatchers.IO).launch {
            startHearBeatThread()
        }
        logI("KeepAliveService onStartCommand")
        hasRetriedFirst = false
        hasRetriedSecond = false
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        isRunning = false
        logStackTrace()
        logI("KeepAliveService onDestroy")
//        stopHeatBeatThread()
        stopForeground(STOP_FOREGROUND_REMOVE)
    }

    override fun onCreate() {
        super.onCreate()
    }

    companion object {
        private var mDeviceManager: IDevice? = null
        private var mContext: Context? = null
        private var hostHeartbeatCount = 0
        private var lastCastStatus = -2
        val castStatusChanged = MutableSharedFlow<Int>()
        var LastcastStatusChanged = 0
        val replyCastState = MutableSharedFlow<Int>()
        var isRunning = false

        fun startService(activity: Activity) {
            val intent = Intent(activity, EShareHeartBeatService::class.java)
            activity.startService(intent)
        }

        fun stopService(activity: Activity) {
            val intent = Intent(activity, EShareHeartBeatService::class.java)
            activity.stopService(intent)
        }
    }

    private suspend fun startHearBeatThread() = withContext(Dispatchers.IO) {
        if (isRunning) return@withContext
        isRunning = true
        start()
    }

    private fun stopHeatBeatThread() {
        isRunning = false
        ServiceUtils.stopService(EShareHeartBeatService::class.java)
    }

    var lastCheckTime: Long = 0
    var timeoutCount = 0
    var timeoutTime = 0L
    private suspend fun start() = withContext(Dispatchers.IO) {
        lastCheckTime = 0
        timeoutCount = 0
        timeoutTime = 0
        hostHeartbeatCount = 0
        lastCastStatus = -2
        LastcastStatusChanged = -1
        check()
    }

    suspend fun check() {
        if (!isRunning) {
            return
        }

        if (System.currentTimeMillis() - lastCheckTime >= Constants.HEART_BEAT_PERIOD) {
            lastCheckTime = System.currentTimeMillis()
            if (hostHeartbeatCount < Int.MAX_VALUE) {
                hostHeartbeatCount++
            } else {
                hostHeartbeatCount = 0
            }
            val result = mDeviceManager.hostHeartBeat(hostHeartbeatCount)

            if (result == null) {
                logTagD(TAG, "hostHeartBeat=====result====null")
            }

            if (!TextUtils.isEmpty(result)) {
                timeoutCount = 0
                timeoutTime = 0
                try {
                    dealResult(result)
                    logTagD(TAG, "hostHeartBeat=====result--->$result")
                } catch (e: UnsupportedEncodingException) {
                    e.printStackTrace()
                } catch (e1: JsonSyntaxException) {
                    e1.printStackTrace()
                }
                check()
            } else {
                // 前10次就只继续check心跳,10次之后断开
                timeoutCount++
                if (timeoutTime == 0L) {
                    timeoutTime = System.currentTimeMillis()
                }
                val hasOutTime = System.currentTimeMillis() - timeoutTime
                logTagD(
                    TAG,
                    "hostHeartBeat=====timeoutCount--->$timeoutCount hasOutTime->$hasOutTime"
                )
                if (timeoutCount < Constants.OFFLINE_WAIT_COUNT && hasOutTime < OFFLINE_WAIT_TIME) {
                    //重连设备连接
                    logTagD(TAG, "hostHeartBeat=====result--->等待心跳连接")
                    applicationViewModel.connectDeviceByAddress(applicationViewModel.currentIP) { result ->
                        if (result) {
                            hasRetriedFirst = false
                            timeoutCount = 0
                            logTagD(TAG, "hostHeartBeat=====result--->重新连接成功")
                        } else {
                            logTagD(
                                TAG,
                                "hostHeartBeat=====result--->重新连接失败 ${applicationViewModel.currentDevice?.name}"
                            )
//                                mDeviceManager.disconnectDevice(applicationViewModel.currentDevice?.name)
                            logTagD(TAG, "hostHeartBeat=====result--->重新连接失败")
                        }
                        CoroutineScope(Dispatchers.IO).launch {
                            check()
                        }
                    }
                    logTagD(
                        TAG,
                        "hostHeartBeat=====result--->重新连接isDeviceConnect ${mDeviceManager.isDeviceConnect}"
                    )
                } else {
                    logTagD(TAG, "hostHeartBeat=====result--->彻底断开连接")
                    setCastState(5)
                    isRunning = false
                    ServiceUtils.stopService(EShareHeartBeatService::class.java)
                }
            }
        } else {
            try {
                delay(100)
                check()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }
    }

    @SuppressLint("StaticFieldLeak")
    private fun dealResult(str: String) {
        val results = String(str.toByteArray(StandardCharsets.UTF_8), StandardCharsets.UTF_8)
        val allRet = results.split(System.lineSeparator()).toTypedArray()
        allRet.forEach {
            val result = it.trim()
            if (result.isNotEmpty()) {
                val obj = JsonParser().parse(result).asJsonObject
                if (obj.has(Constants.KEY_CAST_STATE)) {
                    val castState = obj[Constants.KEY_CAST_STATE].asInt
                    val replyHeartbeat = obj[Constants.REPLY_HEART_BEAT].asInt
                    if (lastCastStatus != castState) {
                        lastCastStatus = castState
                        if (replyHeartbeat == 1 && castState ==0){
                            // 刚连接时候的0 不响应
                        }else{
                            setCastState(castState)
                        }
                    }
                }
                if (obj.has(Constants.REPLYCASTREQUEST)) {
                    val replyCastRequest = obj[Constants.REPLYCASTREQUEST].asInt
                    setreplayCastState(replyCastRequest)
                }
                if (obj.has(Constants.MULTISCREEN)) {
                    val screencount = obj[Constants.MULTISCREEN].asInt
                    if (screencount > 1) {
                        if (LastcastStatusChanged != screencount) {
                            setCastState(4)
                            LastcastStatusChanged = screencount
                        }
                    } else {
                        if (LastcastStatusChanged != screencount) {
                            setCastState(3)
                            LastcastStatusChanged = screencount
                        }
                    }
                }
            }
        }
    }

    private fun setCastState(state: Int) {
        MainScope().launch {
            castStatusChanged.emit(state)
        }
    }

    private fun setreplayCastState(state: Int) {
        MainScope().launch {
            replyCastState.emit(state)
        }
    }
}