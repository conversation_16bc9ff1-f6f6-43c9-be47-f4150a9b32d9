package com.czur.czurwma.common

import com.czur.czurwma.BuildConfig
import com.czur.czurwma.preferences.ESharePreferences

object NetUrls {

    val CLIENT_API_VERSION: String
        get() = ESharePreferences.getInstance().starryApiVersion
    //根据名字获取软件系列
    /**
     * name 产品名字
     */
    const val CHECK_SERIES = BuildConfig.CHECK_UPDATE_URL + "/api/official/app/version/support/series"


    //根据系列id及版本号进行版本检测
    /**
     * seriesId	软件系列id
     * version 软件版本
     */
    const val CHECK_UPDATE = BuildConfig.CHECK_UPDATE_URL + "/api/official/app/version/support/check"


    /**
     * http://yapi.czur.com/project/72/interface/api/7046
     * 获取验证码
     * 端口：8652，备用：8687
     * code = 200 //正常
     * code = 202 //校验码
     * code = 205 //md5校验失败
     * code = 210 //空间不足
     * code = 300 //起始字节错误
     * code= 400 //未知错误
     */
    const val GET_VERIFY_CODE = "/api/starry/fileShare/getVerifyCode"

    /**
     * http://yapi.czur.com/project/72/interface/api/7053
     * 获取验证码
     * 端口：8652，备用：8687
     * code = 200 //正常
     * code = 202 //校验码
     * code = 205 //md5校验失败
     * code = 210 //空间不足
     * code = 300 //起始字节错误
     * code= 400 //未知错误
     */
    const val GET_GET_START_BYTES = "/api/starry/fileShare/getStartBytes"


    /**
     * http://yapi.czur.com/project/72/interface/api/7060
     * 上传文件
     * code = 200 //正常
     * code = 202 //校验码
     * code = 205 //md5校验失败
     * code = 210 //空间不足
     * code = 300 //起始字节错误
     * code= 400 //未知错误
     */
    const val POST_UPLOAD_FILE = "/api/starry/fileShare/uploadFile"


    /**
     * http://yapi.czur.com/mock/72/api/starry/fileShare/deleteTempFile
     */
    const val GET_DELETE_TEMP_FILE = "/api/starry/fileShare/deleteTempFile"


    /**
     * http://yapi.czur.com/project/72/interface/api/7748
     * 获取文件列表
     *
     * Root-Local"（本地文件） ,
     * "Root-Download"（下载文件） ,
     * "Root-Share（成者妙传）",
     * "Root-Meeting"（会议录制）,
     * "Root-Picture（拍照/截图）
     *
     */
    const val GET_FILE_LIST = "/api/starry/fileShare/getFileList"


    /**
     * http://yapi.czur.com/mock/72/api/starry/fileShare/download
     * 下载文件
     */
    const val DOWNLOAD_FILE = "/api/starry/fileShare/download"


    /**
     * 检查支持什么功能,下载还是上传
     */
    const val CHECK_FUNCTIONS = "/api/starry/fileShare/functions"


    /**
     * 每次下载一段之前都要检查文件是否存在
     */
    const val CHECK_FILE = "/api/starry/fileShare/checkFile"

    /**
     * http://yapi.czur.com/project/72/interface/api/11050
     *  获取根目录列表
     */
    const val GET_ROOT_LIST = "/api/starry/fileShare/getRootList"

    /**
     * http://yapi.czur.com/project/72/interface/api/11055
     * 检查密码是否正确
     */
    const val GET_FILE_CHECK_PWD = "/api/starry/fileShare/checkPwd"

    /**
     * http://yapi.czur.com/project/72/interface/api/9633
     * 获取文件缩略图
     */
    const val GET_FILE_GET_THUMB = "/api/starry/fileShare/getThumb"

    /**
     * 获取starry的状态,查看是否关闭了文件传输开关
     */
    const val GET_STARRY_STATUS = "/api/starry/fileShare/getStarryStatus"
}

