package com.czur.czurwma.common

import android.net.Uri


const val DOWNLOAD = "download"
const val DONE = "done"
const val FAIL = "fail"
const val PAUSE = "pause"
enum class  UploadFileEnum{
    UPLOADING,
    DONE_UPLOAD,
    FAIL,
    PAUSE,
    LARGE_SIZE_ERROR,
    NONE,
    DOWNLOADING,
    DONE_DOWNLOAD,
    FILE_NOT_FOUND,
    CAN_NOT_DOWNLOAD_EMPTY // 目前是因为文件是0kb,报错
}

data class TransmitFileEntity(
    var md5: String? = "",
    var fileName: String = ""
    , var fileSize: String = ""
    , var filePath: String = ""//因为可以选择同一个文件,所以前面在makeData的时候加了一个时间戳来区分
    , var progress: Float =0f
    ,var uri : Uri = Uri.EMPTY
    , var status:UploadFileEnum = UploadFileEnum.NONE // download 下载   done 完成  fail 失败 pause 暂停
)