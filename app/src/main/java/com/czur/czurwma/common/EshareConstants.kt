package com.czur.czurwma.common

object EshareConstants {
    /***** 时间单位 *****/
    const val ONE_SECOND = 1000L
    const val ONE_MINUTE = 60 * ONE_SECOND
    const val ONE_HOUR = 60 * ONE_MINUTE

    const val STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND_ID = 1230

    const val RESULT_SUCCESS_CODE = 666

    const val RESULT_CHECK_OVERLAYS_CODE = 777
    const val RESULT_TRANSFER_FILE_CODE = 778
    const val RESULT_FIND_DEVICES_CODE = 779

    const val STARRY_CALL_PAGE_NUM = 0
    const val STARRY_CALL_PAGE_SIZE = 20

    const val MAX_DEVICE_NAME_LENGTH = 14 // 名称规则：7个汉字或14个字符。
    const val MAX_ESHARE_CODE_LENGTH = 15 // 名称规则：15个字符。

    // 会议的状态  1：未开始 NOT_STARTED；2：进行中 PCOCESSING；3：结束 END
    const val MEETING_STATUS_NOT_STARTED    = 1    //未开始
    const val MEETING_STATUS_PCOCESSING     = 2    //进行中
    const val MEETING_STATUS_END            = 3    //结束

    //人的 status 状态
    //0：呼叫中 CALLING 1：未接听超时 TIMEOUT 2：加入 JOINED 3：拒接 REJECTED
    //4：被移除 REMOVED 5：离开 LEAVE 6：掉线 OFFLINE 7：暂时离开 HOLD_ON
    const val CALL_STATUS_CALLING   = 0    //呼叫中
    const val CALL_STATUS_TIMEOUT   = 1 //未接听超时
    const val CALL_STATUS_JOINED    = 2    //加入
    const val CALL_STATUS_REJECTED  = 3  //拒接
    const val CALL_STATUS_REMOVED   = 4      //被移除
    const val CALL_STATUS_LEAVE     = 5      //离开
    const val CALL_STATUS_OFFLINE   = 6   //掉线
    const val CALL_STATUS_HOLD_ON   = 7   //暂时离开

    const val BAR_MAX_COUNT = 7 // barchart的X轴标签数量
    const val RUN_DELAY_TIMES1000 = 1000 // 延迟1秒
    const val RUN_DELAY_TIMES_BASE = RUN_DELAY_TIMES1000 // 延迟1分钟

    // 会议号长度判断
    const val MEETING_NO_LEN_6 = 6
    const val MEETING_NO_LEN_8 = 8
    const val MEETING_NO_LEN_11 = 11

    const val INTENT_FROM_TYPE_ADDRESS = 1 // 1-通讯录
    const val INTENT_FROM_TYPE_COMPANY = 2 // 2-企业成员
    const val INTENT_FROM_TYPE_MEETING = 3 // 3-会议详情

    // intent传值的参数名
    const val STARRY_USER_TYPE = "userType"
    const val STARRY_USER_TITLE = "userTitle"
    const val STARRY_USER_MODEL = "userModel"
    const val STARRY_PRE_ACTIVITY = "userPreActivity"
    const val STARRY_MEETING_FROM = "MEETING_FROM"
    const val STARRY_MEETING_FROM_DETAIL = "MEEETING_DETAIL"

    const val STARRY_PRE_PAGE_NAME = "StarryCompanyListActivity"

    const val STARRY_MEMBER_LIST_MODEL = "memberListModel"
    const val STARRY_MEETING_MODEL = "meetingModel"
    const val STARRY_MEETING_TITLE = "meetingTitle"
    const val STARRY_MEETING_ID = "meetingId"
    const val STARRY_MEETING_ISPCENTER = "meetingIsPCEnter"
    const val STARRY_MEETING_AUTOPLAY = "meetingAutoPlay"
    const val STARRY_CONTACT_TITLE = "contactTitle"
    const val STARRY_CONTACT_ID = "contactId"
    const val STARRY_CONTACT_VALUE = "contactValue"
    const val STARRY_CONTACT_EDIT_TYPE = "contactEditType"
    const val STARRY_CONTACT_EDIT_TYPE_NAME = 0
    const val STARRY_CONTACT_EDIT_TYPE_MOBILE = 1
    const val STARRY_CONTACT_LIST = "contactList"
    const val STARRY_COMPANY_LIST_SEARCH = "companyListSearch"

    const val STARRY_BLANK_TYPE = "blankActivityType"
    const val STARRY_BLANK_TYPE_REMOVED = "ACTIVITY_TYPE_REMOVED"
    const val STARRY_BLANK_TYPE_STOP = "ACTIVITY_TYPE_STOP"
    const val STARRY_BLANK_TYPE_RECALL = "ACTIVITY_TYPE_RECALL"
    const val STARRY_BLANK_TYPE_QUITSHARE = "ACTIVITY_TYPE_QUITSHARE"
    const val STARRY_BLANK_TYPE_OTHER_JOIN = "ACTIVITY_TYPE_OTHER_JOIN"
    const val STARRY_BLANK_PARAM = "ACTIVITY_STARRY_BLANK_PARAM"
    const val STARRY_BLANK_PARAM2 = "ACTIVITY_STARRY_BLANK_PARAM2"
    const val STARRY_BLANK_TYPE_STOP_FORCE = "ACTIVITY_TYPE_STOP_FORCE"

    const val UPDATE_FORCE = "UPDATE_FORCE"
    const val UPDATE_NORMAL = "UPDATE_NORMAL"

    const val STARRY_BLANK_TYPE_CLOSE = "STARRY_BLANK_CLOSE"


    const val STARRY_BLANK_CLIPBOARD_TYPE = "STARRY_BLANK_CLIPBOARD_TYPE"
    const val STARRY_BLANK_TYPE_WEB = "STARRY_BLANK_TYPE_WEB"
    const val STARRY_BLANK_TYPE_COPY = "STARRY_BLANK_TYPE_COPY"
    const val STARRY_BLANK_CLIPBOARD_CONTENT = "STARRY_BLANK_CLIPBOARD_CONTENT"
    const val STARRY_BLANK_CLIPBOARD_UUID = "STARRY_BLANK_CLIPBOARD_UUID"


    // 0 未读; 1已读;2已处理
    const val STARRY_MESSAGE_STATUS_UNREAD = 0
    const val STARRY_MESSAGE_STATUS_READ = 1
    const val STARRY_MESSAGE_STATUS_DEAL = 2

    // 返回的true string
    const val RET_DATA_TRUE = true

    // 用户的类型
    const val STARRY_USERTYPE_COMP = "COMPANY"
    const val STARRY_USERTYPE_PERSION = "PERSION"

    // 通讯录类型
    const val STARRY_USER_TYPE_COMPANY = "COMPANY"
    const val STARRY_USER_TYPE_COMP_NO = "COMPANY_NO"   //未加入
    const val STARRY_USER_TYPE_COMP_EXPRIED = "COMPANY_EXPRIED"   //已过期
    const val STARRY_USER_TYPE_COMP_ADD = "COMPANY_ADD"   //添加会议成员
    const val STARRY_USER_TYPE_CONTACT = "CONTACT" //通讯录
    const val STARRY_USER_TYPE_CC = "COMPANY_CONTACT"
    const val STARRY_USER_TYPE_ADDRESS_BOOK = "ADRESS_BOOK"
    const val STARRY_USER_TYPE_ENTERPRISE_MEMBER = 1

    // 选择的类型
    const val STARRY_SELECT_TYPE_START = "MEETING_SELECT_START" // 发起新的会议
    const val STARRY_SELECT_TYPE_ADD = "MEETING_SELECT_ADD"   //添加会议成员

    const val STARRY_COMPANY_STATUS_JOINED = "2"   //已加入企业
    // 加入状态，同意加入传2，拒绝传3 退出5
    const val STARRY_COMPANY_STATUS_JOIN = 2
    const val STARRY_COMPANY_STATUS_REJECT = 3
    const val STARRY_COMPANY_STATUS_EXIT = 5

    // LiveDataBus key
    // 被移出会议
    const val MEETING_REMOVED = "MEETING_REMOVED"
    // 清空最近会议
    const val MEETING_CLEAR = "MEETING_CLEAR"
    // 会议中来电
    const val MEETING_RECALL = "MEETING_RECALL"

    // 投屏中来电
    const val ESHARE_MEETING_CALLIN = "ESHARE_MEETING_CALLIN"

    // 会议锁定画面
    const val MEETING_CLOCK = "MEETING_CLOCK"
    // 会议中编辑昵称
    const val STARRY_CONTACT_EDIT_MEETING = "STARRY_CONTACT_EDIT_MEETING"

    // 会议中添加联系人
    const val STARRY_CONTACT_ADDTO_MEETING = "STARRY_CONTACT_ADDTO_MEETING"

    // 未接会议刷新，小红点
    const val MEETING_NOJOIN_RED = "MEETING_NOJOIN_RED"

    // 退出企业
    const val STARRY_COMPANY_EXIT = "STARRY_COMPANY_EXIT"

    // 删除联系人的事件通知
    const val STARRY_CONTACT_DELETE = "STARRY_CONTACT_DELETE"
    const val STARRY_CONTACT_EDIT = "STARRY_CONTACT_EDIT"
    const val STARRY_CONTACT_ADD = "STARRY_CONTACT_ADD"
    const val STARRY_CONTACT_ADDTO = "STARRY_CONTACT_ADDTO"

    // STARRY_NOTICE_OTHER_DEVICE_LOGIN, //在其他设备登录
    const val STARRY_OTHER_DEVICE_LOGIN = "STARRY_OTHER_DEVICE_LOGIN"

    const val MEETING_LONG_CONNECTION_CHECK = "MEETING_LONG_CONNECTION_CHECK"
    // 声网反馈网络连接失败
    const val MEETING_AGORA_CONNECT_FAILED = "MEETING_AGORA_CONNECT_FAILED"
    // 长连接反馈断开连接
    const val MEETING_NETTY_CONNECT_FAILED = "MEETING_NETTY_CONNECT_FAILED"

    const val INTENT_FROM_TYPE = "INTENT_FROM_TYPE" //针对获取联系人详情,定义从哪里跳转 1-通讯录，2-企业成员，3-会议详情
    const val INTENT_FROM_ID = "INTENT_FROM_ID" //针对获取联系人详情,id

    // 本地视频启动失败
    const val STARRY_LOCAL_VIDEO_STREAM_STATE_FAILED = "STARRY_LOCAL_VIDEO_STREAM_STATE_FAILED"

    const val STARRY_LOCAL_AUDIO_STREAM_STATE_FAILED = "STARRY_LOCAL_AUDIO_STREAM_STATE_FAILED"

    // 剪贴板中的内容
    const val MEETING_CLIPBOARD = "MEETING_CLIPBOARD"
    const val MEETING_CLIPBOARD_STARRY = "MEETING_CLIPBOARD_STARRY"
    const val MEETING_CLIPBOARD_INDEX = "MEETING_CLIPBOARD_INDEX"

    // starry_room_user_list的密码有变化
    const val MEETING_USER_LIST_CHANGE_PWD = "MEETING_USER_LIST_CHANGE_PWD"

    const val MEETING_USER_LIST_CHANGE_CODE = "MEETING_USER_LIST_CHANGE_CODE"

    // 会议中跳转入会
    const val MEETING_REJOIN_FROM_WEB = "MEETING_REJOIN_FROM_WEB"
    const val MEETING_REJOIN_FROM_COPY = "MEETING_REJOIN_FROM_COPY"
    const val MEETING_REJOIN_MEETING_FROM_WEB = "MEETING_REJOIN_MEETING_FROM_WEB"
    const val MEETING_REJOIN_MEETING_FROM_WEB_INDEX = "MEETING_REJOIN_MEETING_FROM_WEB_INDEX"

    // 会议中，跳转入会，先停止当前会议
    const val MEETING_REJOIN_STOP_FROM_WEB = "MEETING_REJOIN_STOP_FROM_WEB"

    // 取消权限弹窗，告知meetfragment
    const val MEETING_PERMISSIONS_CANCEL = "MEETING_PERMISSIONS_CANCEL"

    const val SYNC_USER_INFO_ID = "SYNC_USER_INFO_ID"

    // 成为管理员，通知收起退出弹窗
    const val MEETING_BE_ADMIN = "MEETING_BE_ADMIN"

    // 移除starry模块
    const val STARRY_REMOVE = "STARRY_REMOVE"

    // 删除消息后需要刷新首页消息的小红点
    const val MEETING_DELETE_MESSAGE_REDPOINT = "MEETING_DELETE_MESSAGE_REDPOINT"

    // 强制更新
    const val DOWNLOAD_FORCE_RUNNING = "DOWNLOAD_FORCE_RUNNING"
    const val DOWNLOAD_FORCE_DONE = "DOWNLOAD_FORCE_DONE"

    // 起始页点击login
    const val LOGIN_SHOW_LOGIN_BTN = "LOGIN_SHOW_LOGIN_BTN"

    // CONNECTION_CHANGED_INVALID_TOKEN(8): 生成的 Token 无效
    const val MEETING_AGORA_CONNECT_INVALID_TOKEN = "MEETING_AGORA_CONNECT_INVALID_TOKEN"

    // EshareWidgetProvider  Type
    const val ESHARE_EMPTY_TYPE = "eshareEmptyType"
    const val ESHARE_EMPTY_TYPE_NORMAL = "ESHARE_EMPTY_TYPE_NORMAL"
    const val ESHARE_EMPTY_TYPE_FIND = "ESHARE_EMPTY_TYPE_FIND"
    const val ESHARE_EMPTY_TYPE_INPUT = "ESHARE_EMPTY_TYPE_INPUT"
    const val ESHARE_EMPTY_TYPE_SCAN = "ESHARE_EMPTY_TYPE_SCAN"

    const val APP_UPDATE_NO_UPDATE_INFO = "APP_UPDATE_NO_UPDATE_INFO"//没有更新信息
    const val APP_UPDATE_HAS_UPDATE_INFO = "APP_UPDATE_HAS_UPDATE_INFO"//有更新信息
    const val APP_UPDATE_RETRY = "APP_UPDATE_RETRY" //重试下载
    const val APP_UPDATE_DONE = "APP_UPDATE_DONE" //下载完成

    const val TRANSMIT_RECONNECT = "TRANSMIT_RECONNECT" //重新连接服务器
    const val TRANSMIT_PWD_ALLOW_WRONG_TIMES = 3 //加密文件密码最多输入错误次数
    const val TRANSMIT_PWD_LOCKED_TIME = 12* ONE_HOUR //密码错误后的锁定时长

    const val PWD_ERROR_406 = "PWD_ERROR_406" //加密文件密码错误

}