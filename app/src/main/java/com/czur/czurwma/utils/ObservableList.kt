package com.czur.czurwma.utils

class ObservableList<E>() : ArrayList<E>() {

    private val observer: MutableList<ListObserver<E>> = mutableListOf()

    override fun clear() {
        super.clear()
        for (o in observer) {
            o.onChange(null)
        }
    }

    override fun add(element: E): <PERSON><PERSON>an {
        val isAdded = super.add(element)
        for (o in observer) {
            if (isAdded) {
                o.onAdd(element)
                o.onChange(element)
            }
        }

        return isAdded
    }

    override fun remove(element: E): <PERSON><PERSON>an {
        val isRemoved = super.remove(element)
        for (o in observer) {
            if (isRemoved) {
                o.onRemove(element)
                o.onChange(element)
            }
        }
        return isRemoved
    }


    fun setListener(obs: ListObserver<E>) {
        observer.add(obs)
    }

}

interface ListObserver<E> {
    fun onAdd(element: E)
    fun onRemove(element: E)
    fun onChange(element: E?)
}

