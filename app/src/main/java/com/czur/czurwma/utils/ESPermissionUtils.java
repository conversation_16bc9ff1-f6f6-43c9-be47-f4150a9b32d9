package com.czur.czurwma.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.PermissionUtils;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;


public final class ESPermissionUtils {

    public static final int REQUEST_CAMERA_PERMISSIONS = 1001;
    public static final int REQUEST_MEDIA_PERMISSIONS = 1002;
    public static final int REQUEST_PHOTO_PERMISSIONS = 1003;
    public static final int REQUEST_DOCUMENT_PERMISSIONS = 1004;
    public static final int REQUEST_PACKAGE_PERMISSIONS = 1005;
    public static final int REQUEST_PROGRAM_PERMISSIONS = 1006;
    public static final int REQUEST_LOCATION_PERMISSIONS = 1007;
    public static final int REQUEST_LOCATION_SETTINGS = 1008;
    public static final int REQUEST_SHARE_PERMISSIONS=1009;
    public static final int REQUEST_SPEAKER_PERMISSIONS = 1010;
    public static final int REQUEST_CAMERA_LOCATION_PERMISSIONS = 1011;
    public static final int REQUEST_GPS_PERMISSIONS = 1012;

    public static final int REQUEST_NFC_PERMISSIONS = 1013;
    public static final int REQUEST_COARSE_LOCATION_PERMISSIONS = 1014;

    public static final int REQUEST_STARRY_PERMISSIONS = 1015;


    public static boolean requestSpeakerPermissions(@NonNull Activity activity) {
        List<String> permissions = new CopyOnWriteArrayList<>();
        permissions.add(Manifest.permission.RECORD_AUDIO);

        for (String permission : permissions) {
            int result = ActivityCompat.checkSelfPermission(activity.getApplicationContext(), permission);
            if (result == PackageManager.PERMISSION_GRANTED) {
                permissions.remove(permission);
            }
        }

        if (!permissions.isEmpty()) {
            String[] array = new String[permissions.size()];
            permissions.toArray(array);
            ActivityCompat.requestPermissions(activity, array, REQUEST_SPEAKER_PERMISSIONS);
            return false;
        }
        return true;
    }

    public static boolean requestCameraPermissions(@NonNull Activity activity) {
        List<String> permissions = new CopyOnWriteArrayList<>();
        permissions.add(Manifest.permission.CAMERA);

        for (String permission : permissions) {
            int result = ActivityCompat.checkSelfPermission(activity.getApplicationContext(), permission);
            if (result == PackageManager.PERMISSION_GRANTED) {
                permissions.remove(permission);
            }
        }

        if (!permissions.isEmpty()) {
            String[] array = new String[permissions.size()];
            permissions.toArray(array);
            ActivityCompat.requestPermissions(activity, array, REQUEST_CAMERA_PERMISSIONS);
            return false;
        }
        return true;
    }

    public static boolean requestStoragePermissions(@NonNull Activity activity, int requestCode) {
        List<String> permissions = new CopyOnWriteArrayList<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);

        for (String permission : permissions) {
            int result = ActivityCompat.checkSelfPermission(activity.getApplicationContext(), permission);
            if (result == PackageManager.PERMISSION_GRANTED) {
                permissions.remove(permission);
            }
        }

        if (!permissions.isEmpty()) {
            String[] array = new String[permissions.size()];
            permissions.toArray(array);
            ActivityCompat.requestPermissions(activity, array, requestCode);
            return false;
        }
        return true;
    }
    public static boolean requestLocationPermissions(@NonNull Activity activity, int requestCode) {
        List<String> permissions = new CopyOnWriteArrayList<>();

        permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION);

        for (String permission : permissions) {
            int result = ActivityCompat.checkSelfPermission(activity.getApplicationContext(), permission);
            if (result == PackageManager.PERMISSION_GRANTED) {
                permissions.remove(permission);
            }
        }

        if (!permissions.isEmpty()) {
            String[] array = new String[permissions.size()];
            permissions.toArray(array);
            ActivityCompat.requestPermissions(activity, array, requestCode);
            return false;
        }
        return true;
    }

    public static void openLocationSettings(@NonNull Activity activity) {
        Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        activity.startActivityForResult(intent, REQUEST_LOCATION_SETTINGS);
    }

    @SuppressLint("WrongConstant")
    public static boolean checkDrawOverlays(@NonNull Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {

            if (!Settings.canDrawOverlays(activity)) {
                try {
                activity.startActivityForResult(new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + activity.getPackageName())),2000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return false;
            }
        }
        return true;
    }

    public static boolean RequestGpsGranted(Activity activity){

              Boolean gps=checkGpsGranted(activity);
        if(!gps){
            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
//PermissionConstants.RC_GPS是请求码，可以在onActivityResult中根据该请求码判断用户是否已经在设置页面打开位置服务
            activity.startActivityForResult(intent, REQUEST_GPS_PERMISSIONS);
            return false;
        }

      return true;
    }

    public static Boolean checkGpsGranted(Activity activity){
        LocationManager locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        // 通过GPS卫星定位，定位级别可以精确到街（通过24颗卫星定位，在室外和空旷的地方定位准确、速度快）  
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        return gps;
    }

    public static boolean requestStarryPermissions(@NonNull Activity activity) {
        List<String> permissions = new CopyOnWriteArrayList<>();
        permissions.add(Manifest.permission.RECORD_AUDIO);
        permissions.add(Manifest.permission.CAMERA);

        for (String permission : permissions) {
            int result = ActivityCompat.checkSelfPermission(activity.getApplicationContext(), permission);
            if (result == PackageManager.PERMISSION_GRANTED) {
                permissions.remove(permission);
            }
        }

        if (!permissions.isEmpty()) {
            String[] array = new String[permissions.size()];
            permissions.toArray(array);
            ActivityCompat.requestPermissions(activity, array, REQUEST_STARRY_PERMISSIONS);
            return false;
        }
        return true;
    }

    //检查视频和音频权限是否都开启
    public static boolean checkVideoAndAudioPermission(){
        boolean isMic = PermissionUtils.isGranted(PermissionConstants.MICROPHONE);
        boolean isCamera = PermissionUtils.isGranted(PermissionConstants.CAMERA);
        return isMic && isCamera;
    }

}
