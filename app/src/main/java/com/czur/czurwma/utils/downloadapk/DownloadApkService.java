package com.czur.czurwma.utils.downloadapk;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.czur.czurwma.R;


public class DownloadApkService extends Service {

    private static final String KEY_TASK_TYPE = "taskType";
    private static final int TASK_TYPE_DEFAULT = -1;  // 兼容之前逻辑
    private static final int TASK_TYPE_CHECK_HISTORY = 1;

    private static final String KEY_CLEAR_PAGE = "clearPage";

    private AppDownloadManager downloadManager;
    private String updateUrl;
    private String notes;
    private String apkName;

    /**
     * 检查是否有之前未下载完的记录
     *
     * @param context
     */
    public static void checkHistoryDownloadTask(Context context, Boolean clearPage) {
        Intent intent = new Intent(context, DownloadApkService.class);
        intent.putExtra(KEY_TASK_TYPE, TASK_TYPE_CHECK_HISTORY);
        intent.putExtra(KEY_CLEAR_PAGE, clearPage);
        context.startService(intent);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int taskType = TASK_TYPE_DEFAULT;
        boolean needClearPage = true;
        if (intent != null) {
            taskType = intent.getIntExtra(KEY_TASK_TYPE, TASK_TYPE_DEFAULT);
            // 按照之前逻辑, 每一次启动这个Service, 都会产生一个新的AppDownloadManager
            // 然后一个AppDownloadManager只负责下载1个Apk, 没太明白为什么这样设计.
            needClearPage = intent.getBooleanExtra(KEY_CLEAR_PAGE, true);
        }
        downloadManager = new AppDownloadManager(this);
        downloadManager.setInstallNeedClearPage(needClearPage);
        switch (taskType) {
            case TASK_TYPE_CHECK_HISTORY:
                processTaskCheckHistory();
                break;
            default:
                processTaskDef(intent);
        }
        downloadManager.resume();

        return super.onStartCommand(intent, flags, startId);
    }

    private void processTaskCheckHistory() {
        downloadManager.checkHistoryDownloadTask();
    }

    private void processTaskDef(Intent intent) {
        updateUrl = intent.getStringExtra("updateUrl");
        notes = intent.getStringExtra("notes");
        apkName = intent.getStringExtra("apkName");
        logI("DownloadApkService.updateUrl=" + updateUrl,
                "notes=" + notes,
                "apkName=" + apkName);
        downloadManager.setApkName(apkName);
        check();
    }

    private void check() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                logI("DownloadApkService.updateUrl=" + updateUrl);
                try {
                    downloadManager.downloadApk(updateUrl, getString(R.string.app_update_title), notes);
                    downloadManager.setUpdateListener(new AppDownloadManager.OnUpdateListener() {
                        @Override
                        public void update(int currentByte, int totalByte) {
                            if (currentByte > 0 && totalByte > 0 && currentByte >= totalByte) {
                                processTaskCheckHistory();
                            }
                        }
                    });
                } catch (Exception e) {
                    logE("DownloadApkService.check.e=" + e.toString());
                }
            }
        }).start();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        downloadManager.onPause();
    }
}
