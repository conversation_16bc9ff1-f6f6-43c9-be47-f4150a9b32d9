package com.czur.czurwma.utils

import android.app.Activity
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logW

/**
 * 预测性返回手势适配工具类
 * Android 16默认启用预测性返回手势
 */
object PredictiveBackUtils {

    /**
     * 为Activity设置预测性返回手势处理
     */
    fun setupPredictiveBack(activity: ComponentActivity, onBackPressed: () -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            setupPredictiveBackApi33(activity, onBackPressed)
        } else {
            setupLegacyBack(activity, onBackPressed)
        }
        logI("PredictiveBackUtils", "Predictive back setup for ${activity.javaClass.simpleName}")
    }

    /**
     * Android 13+ (API 33+) 的预测性返回实现
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun setupPredictiveBackApi33(activity: ComponentActivity, onBackPressed: () -> Unit) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                logI("PredictiveBackUtils", "Predictive back triggered")
                onBackPressed()
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 低版本的返回处理
     */
    private fun setupLegacyBack(activity: ComponentActivity, onBackPressed: () -> Unit) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                logI("PredictiveBackUtils", "Legacy back triggered")
                onBackPressed()
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 创建可控制的返回回调
     */
    fun createBackCallback(
        enabled: Boolean = true,
        onBackPressed: () -> Unit
    ): OnBackPressedCallback {
        return object : OnBackPressedCallback(enabled) {
            override fun handleOnBackPressed() {
                onBackPressed()
            }
        }
    }

    /**
     * 为Fragment设置预测性返回处理
     */
    fun setupPredictiveBackForFragment(
        activity: ComponentActivity,
        enabled: Boolean = true,
        onBackPressed: () -> Unit
    ): OnBackPressedCallback {
        val callback = createBackCallback(enabled, onBackPressed)
        activity.onBackPressedDispatcher.addCallback(callback)
        return callback
    }

    /**
     * 设置条件性返回处理
     */
    fun setupConditionalBack(
        activity: ComponentActivity,
        condition: () -> Boolean,
        onBackPressed: () -> Unit,
        onBackNotHandled: (() -> Unit)? = null
    ) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (condition()) {
                    logI("PredictiveBackUtils", "Conditional back handled")
                    onBackPressed()
                } else {
                    logI("PredictiveBackUtils", "Conditional back not handled")
                    onBackNotHandled?.invoke() ?: run {
                        // 如果没有提供未处理的回调，则禁用此回调并重新触发返回
                        isEnabled = false
                        activity.onBackPressedDispatcher.onBackPressed()
                        isEnabled = true
                    }
                }
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 设置多级返回处理（如：关闭弹窗 -> 返回上一页 -> 退出应用）
     */
    fun setupMultiLevelBack(
        activity: ComponentActivity,
        handlers: List<() -> Boolean> // 返回true表示已处理，false表示继续下一个处理器
    ) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                for ((index, handler) in handlers.withIndex()) {
                    if (handler()) {
                        logI("PredictiveBackUtils", "Multi-level back handled at level $index")
                        return
                    }
                }
                
                // 所有处理器都返回false，执行默认返回行为
                logI("PredictiveBackUtils", "Multi-level back not handled, using default")
                isEnabled = false
                activity.onBackPressedDispatcher.onBackPressed()
                isEnabled = true
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 设置确认退出的返回处理
     */
    fun setupConfirmExitBack(
        activity: ComponentActivity,
        showConfirmDialog: () -> Unit
    ) {
        var lastBackTime = 0L
        val backInterval = 2000L // 2秒内连续按返回键才退出
        
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastBackTime < backInterval) {
                    // 连续按返回键，执行退出
                    logI("PredictiveBackUtils", "Confirm exit - double back detected")
                    isEnabled = false
                    activity.onBackPressedDispatcher.onBackPressed()
                    isEnabled = true
                } else {
                    // 第一次按返回键，显示确认提示
                    logI("PredictiveBackUtils", "Confirm exit - first back, showing dialog")
                    lastBackTime = currentTime
                    showConfirmDialog()
                }
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 检查是否支持预测性返回手势
     */
    fun isPredictiveBackSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
    }

    /**
     * 检查是否为Android 16+（预测性返回手势默认启用）
     */
    fun isPredictiveBackEnabledByDefault(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }

    /**
     * 获取返回手势相关信息
     */
    fun getBackGestureInfo(): String {
        return when {
            isPredictiveBackEnabledByDefault() -> "Android 16+ - Predictive back enabled by default"
            isPredictiveBackSupported() -> "Android 13+ - Predictive back supported"
            else -> "Legacy back handling"
        }
    }

    /**
     * 为WebView设置返回处理
     */
    fun setupWebViewBack(
        activity: ComponentActivity,
        canGoBack: () -> Boolean,
        goBack: () -> Unit,
        onExitWebView: () -> Unit
    ) {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (canGoBack()) {
                    logI("PredictiveBackUtils", "WebView back - going back in history")
                    goBack()
                } else {
                    logI("PredictiveBackUtils", "WebView back - exiting WebView")
                    onExitWebView()
                }
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
    }

    /**
     * 临时禁用返回手势（如在加载过程中）
     */
    fun createTemporaryDisableCallback(activity: ComponentActivity): OnBackPressedCallback {
        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                logW("PredictiveBackUtils", "Back gesture temporarily disabled")
                // 不执行任何操作，阻止返回
            }
        }
        
        activity.onBackPressedDispatcher.addCallback(activity, callback)
        return callback
    }
}
