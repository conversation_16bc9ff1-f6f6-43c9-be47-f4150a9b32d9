package com.czur.czurwma.utils

import android.app.Activity
import android.app.AppOpsManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.os.Binder
import android.os.Build
import android.os.Process
import android.provider.Settings
import android.util.Log
import com.blankj.utilcode.util.PermissionUtils
import java.lang.reflect.Method
import java.lang.reflect.Modifier

const val MIUI_OP_END = 10028
const val MIUI_OP_START = 10000
const val MODE_ALLOWED = 0
const val MODE_ASK = 5
const val MODE_DEFAULT = 3
const val MODE_ERRORED = 2
const val MODE_FOREGROUND = 4
const val MODE_IGNORED = 1
const val MODE_UNKNOWN = -1
const val OP_ACCEPT_HANDOVER = 74
const val OP_ACCESS_NOTIFICATIONS = 25
const val OP_ACCESS_XIAOMI_ACCOUNT = 10015
const val OP_ACTIVATE_VPN = 47
const val OP_ADD_VOICEMAIL = 52
const val OP_ANSWER_PHONE_CALLS = 69
const val OP_ASSIST_SCREENSHOT = 50
const val OP_ASSIST_STRUCTURE = 49
const val OP_AUDIO_ACCESSIBILITY_VOLUME = 64
const val OP_AUDIO_ALARM_VOLUME = 37
const val OP_AUDIO_BLUETOOTH_VOLUME = 39
const val OP_AUDIO_MASTER_VOLUME = 33
const val OP_AUDIO_MEDIA_VOLUME = 36
const val OP_AUDIO_NOTIFICATION_VOLUME = 38
const val OP_AUDIO_RING_VOLUME = 35
const val OP_AUDIO_VOICE_VOLUME = 34
const val OP_AUTO_START = 10008 //是否允许自启动权限
const val OP_BACKGROUND_LOCATION = 10027  //后台定位权限
const val OP_BACKGROUND_START_ACTIVITY = 10021 //后台弹出界面
const val OP_BIND_ACCESSIBILITY_SERVICE = 73
const val OP_BLUETOOTH_CHANGE = 10002
const val OP_BLUETOOTH_SCAN = 77
const val OP_BODY_SENSORS = 56
const val OP_BOOT_COMPLETED = 10007
const val OP_CALL_PHONE = 13
const val OP_CAMERA = 26
const val OP_CHANGE_WIFI_STATE = 71
const val OP_COARSE_LOCATION = 0
const val OP_DATA_CONNECT_CHANGE = 10003
const val OP_DELETE_CALL_LOG = 10013
const val OP_DELETE_CONTACTS = 10012
const val OP_DELETE_MMS = 10011
const val OP_DELETE_SMS = 10010
const val OP_EXACT_ALARM = 10014
const val OP_FINE_LOCATION = 1
const val OP_GET_ACCOUNTS = 62
const val OP_GET_ANONYMOUS_ID = 10024
const val OP_GET_INSTALLED_APPS = 10022
const val OP_GET_TASKS = 10019
const val OP_GET_UDEVICE_ID = 10025
const val OP_GET_USAGE_STATS = 43
const val OP_GPS = 2
const val OP_INSTALL_SHORTCUT = 10017 //安装快捷方式
const val OP_INSTANT_APP_START_FOREGROUND = 68
const val OP_MANAGE_IPSEC_TUNNELS = 75
const val OP_MOCK_LOCATION = 58
const val OP_MONITOR_HIGH_POWER_LOCATION = 42
const val OP_MONITOR_LOCATION = 41
const val OP_MUTE_MICROPHONE = 44
const val OP_NEIGHBORING_CELLS = 12
const val OP_NFC = 10016
const val OP_NFC_CHANGE = 10009
const val OP_NONE = -1
const val OP_PICTURE_IN_PICTURE = 67
const val OP_PLAY_AUDIO = 28
const val OP_POST_NOTIFICATION = 11
const val OP_PROCESS_OUTGOING_CALLS = 54
const val OP_PROJECT_MEDIA = 46
const val OP_READ_CALENDAR = 8
const val OP_READ_CALL_LOG = 6
const val OP_READ_CELL_BROADCASTS = 57
const val OP_READ_CLIPBOARD = 29
const val OP_READ_CONTACTS = 4
const val OP_READ_EXTERNAL_STORAGE = 59
const val OP_READ_ICC_SMS = 21
const val OP_READ_MMS = 10005
const val OP_READ_NOTIFICATION_SMS = 10018
const val OP_READ_PHONE_NUMBERS = 65
const val OP_READ_PHONE_STATE = 51
const val OP_READ_SMS = 14
const val OP_RECEIVE_EMERGECY_SMS = 17
const val OP_RECEIVE_MMS = 18
const val OP_RECEIVE_SMS = 16
const val OP_RECEIVE_WAP_PUSH = 19
const val OP_RECORD_AUDIO = 27
const val OP_REQUEST_DELETE_PACKAGES = 72
const val OP_REQUEST_INSTALL_PACKAGES = 66
const val OP_RUN_ANY_IN_BACKGROUND = 70
const val OP_RUN_IN_BACKGROUND = 63
const val OP_SEND_MMS = 10004
const val OP_SEND_SMS = 20
const val OP_SERVICE_FOREGROUND = 10023
const val OP_SHOW_DEAMON_NOTIFICATION = 10026
const val OP_SHOW_WHEN_LOCKED = 10020
const val OP_START_FOREGROUND = 76
const val OP_SYSTEM_ALERT_WINDOW = 24
const val OP_TAKE_AUDIO_FOCUS = 32
const val OP_TAKE_MEDIA_BUTTONS = 31
const val OP_TOAST_WINDOW = 45
const val OP_TURN_SCREEN_ON = 61
const val OP_USE_FINGERPRINT = 55
const val OP_USE_SIP = 53
const val OP_VIBRATE = 3
const val OP_WAKE_LOCK = 40
const val OP_WIFI_CHANGE = 10001
const val OP_WIFI_SCAN = 10
const val OP_WRITE_CALENDAR = 9
const val OP_WRITE_CALL_LOG = 7
const val OP_WRITE_CLIPBOARD = 30
const val OP_WRITE_CONTACTS = 5
const val OP_WRITE_EXTERNAL_STORAGE = 60
const val OP_WRITE_ICC_SMS = 22
const val OP_WRITE_MMS = 10006
const val OP_WRITE_SETTINGS = 23
const val OP_WRITE_SMS = 15
const val OP_WRITE_WALLPAPER = 48

//KEY_OP_CODES值设置为：AppOpsManagerEx.HW_OP_CODE_POPUP_BACKGROUND_WINDOW = 100000
const val HW_OP_CODE_POPUP_BACKGROUND_WINDOW = 100000


object RomUtils {
    public val SETTING_PERMISSION = 9221

    @JvmStatic
    val XIAOMI_MANAGER_ERROR = 1

    @JvmStatic
    val REQUEST_CODE_PERMISSION_TAKE_PHOTO = 2

    @JvmStatic
    val REQUEST_CODE_CHOOSE_PHOTO = 1

    @JvmStatic
    val REQUEST_CODE_TAKE_PHOTO = 2

    @JvmStatic
    val REQUEST_CODE_CROP = 3

    @JvmStatic
    val REQUEST_NICK = 0x123

    private val TAG = RomUtils::class.java.simpleName

    fun isXiaomiVivo(): Boolean {
        return isXiaoMi() || isVivo()
    }

    @JvmStatic
    public fun isXiaoMi(): Boolean {
        return checkManufacturer("xiaomi")
    }


    fun isOppo(): Boolean {
        return checkManufacturer("oppo")
    }

    fun isVivo(): Boolean {
        return checkManufacturer("vivo")
    }

    private fun checkManufacturer(manufacturer: String): Boolean {
        return manufacturer.equals(Build.MANUFACTURER, true)
    }

    //    fun checkSpecialXiaomi(context: Context){
//        if (isXiaoMi()) {
//            val xiaomi1 = isXiaomiBgStartPermissionAllowed(context)
//            val xiaomi2 = canXiaoMiShowLockView(context)
//            if (xiaomi1 && xiaomi2 &&
//                !StarryPreferences.getInstance().hasShowedPermissionsDialog
//            ) {
//                StarryPreferences.getInstance().isSpecialXiaomi = true
//                // 只针对小米第一次进入页面,并且返回都是true的时候,说明获取权限有问题,因为应该都是默认的关闭状态
//            }else{
//                StarryPreferences.getInstance().isSpecialXiaomi = false
//            }
//        }else{
//            StarryPreferences.getInstance().isSpecialXiaomi = false
//        }
//
//    }
    // 获取后台弹出
    fun isBackgroundStartPermissionAllowed(context: Context): Boolean {
        if (isXiaoMi()) {
            val xiaomi1 = isXiaomiBgStartPermissionAllowed(context)
            return xiaomi1
        } else if (isVivo()) {
            val vivo1 = isVivoBgStartPermissionAllowed(context)
            return vivo1
        }
        return false
    }

    //获取锁屏显示权限
    fun isBackgroundLockPermissionAllowed(context: Context): Boolean {
        if (isXiaoMi()) {
            val xiaomi2 = canXiaoMiShowLockView(context)
            return xiaomi2
        } else if (isVivo()) {
            val vivo2 = isVivoLockPermissionAllowed(context)
            return vivo2
        }
        return false
    }

    fun isOverlayPermissionAllowed(context: Context): Boolean {
        if (isXiaoMi()) {
            return checkFloatWindowPermission(context);
        } else {
            return PermissionUtils.isGrantedDrawOverlays()
        }
    }

    fun checkFloatWindowPermission(context: Context): Boolean {
        val version = Build.VERSION.SDK_INT
        return if (version >= 19) {
            checkOp(context, 24)
        } else true
    }

    /******* aiXcoder分隔符（请勿修改）  */ // ApplozicInternal: private
    fun checkOp(context: Context, op: Int): Boolean {
        val version = Build.VERSION.SDK_INT
        if (version >= 19) {
            val manager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            try {
                return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // Android 6+ 使用noteOp
                    val mode = manager.noteOpNoThrow(op, Binder.getCallingUid(), context.packageName)
                    mode == AppOpsManager.MODE_ALLOWED
                } else {
                    // Android 19+ 使用反射（仅在低版本）
                    val clazz: Class<*> = AppOpsManager::class.java
                    val method = clazz.getDeclaredMethod(
                        "checkOp",
                        Int::class.javaPrimitiveType,
                        Int::class.javaPrimitiveType,
                        String::class.java
                    )
                    AppOpsManager.MODE_ALLOWED === method.invoke(
                        manager,
                        op,
                        Binder.getCallingUid(),
                        context.packageName
                    ) as Int
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
        return false
    }

    /**
     *
     */
//    fun isBackgroundStartAllowed(context: Context): Boolean {
//
//        if (isXiaoMi()) {
//            //应该改成 已开启 去设置
//            val xiaomi1 = isXiaomiBgStartPermissionAllowed(context)
//            val xiaomi2 = canXiaoMiShowLockView(context)
//            if (xiaomi1 && xiaomi2 &&
//                !StarryPreferences.getInstance().hasShowedPermissionsDialog
//            ) {
//                StarryPreferences.getInstance().isSpecialXiaomi = true
//                // 只针对小米第一次进入页面,并且返回都是true的时候,说明获取权限有问题,因为应该都是默认的关闭状态
//            }
//
//            return xiaomi1 && xiaomi2
//
//        }
//
//        if (isVivo()) {
//            val vivo1 = isVivoBgStartPermissionAllowed(context)
//            val vivo2 = isVivoLockPermissionAllowed(context)
//            return vivo1 && vivo2
//        }
//
//        return true
//    }

    public fun isXiaomiBgStartPermissionAllowed(context: Context): Boolean {


        val ops = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        try {
            val op = 10021
            val method: Method = ops.javaClass.getMethod(
                "checkOpNoThrow",
                Int::class.javaPrimitiveType,
                Int::class.javaPrimitiveType,
                String::class.java
            )
            val result =
                method.invoke(ops, op, android.os.Process.myUid(), context.packageName) as Int
            return result == AppOpsManager.MODE_ALLOWED
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 小米后台锁屏检测方法
     * @param context
     * @return
     */
    public fun canXiaoMiShowLockView(context: Context): Boolean {
        var ops: AppOpsManager? = null
        ops = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        try {
            val op = 10020 // >= 23
            // ops.checkOpNoThrow(op, uid, packageName)
            val method = ops!!.javaClass.getMethod(
                "checkOpNoThrow", *arrayOf<Class<*>?>(
                    Int::class.javaPrimitiveType,
                    Int::class.javaPrimitiveType,
                    String::class.java
                )
            )
            val result = method.invoke(ops, op, Process.myUid(), context.packageName) as Int
            return result == AppOpsManager.MODE_ALLOWED
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return false
    }


    private fun isVivoBgStartPermissionAllowed(context: Context): Boolean {
        return getVivoBgStartPermissionStatus(context) == 0
    }

    private fun isVivoLockPermissionAllowed(context: Context): Boolean {
        return getVivoLockStatus(context) == 0
    }

    /**
     * 判断Vivo后台弹出界面状态， 1无权限，0有权限
     * @param context context
     */
    private fun getVivoBgStartPermissionStatus(context: Context): Int {
//        val uri: Uri =
//            Uri.parse("content://com.vivo.permissionmanager.provider.permission/start_bg_activity")
//        val selection = "pkgname = ?"
//        val selectionArgs = arrayOf(context.packageName)
//        var state = 1
//        try {
//            context.contentResolver.query(uri, null, selection, selectionArgs, null)?.use {
//                if (it.moveToFirst()) {
//                    state = it.getInt(it.getColumnIndex("currentstate"))
//                }
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//        return state
        return 1
    }

    /**
     * 判断vivo锁屏显示 1未开启 0开启
     * @param context
     * @return
     */
    private fun getVivoLockStatus(context: Context): Int {
//        val packageName = context.packageName
//        val uri2 =
//            Uri.parse("content://com.vivo.permissionmanager.provider.permission/control_locked_screen_action")
//        val selection = "pkgname = ?"
//        val selectionArgs = arrayOf(packageName)
//        try {
//            val cursor: Cursor? = context
//                .contentResolver
//                .query(uri2, null, selection, selectionArgs, null)
//            if (cursor != null) {
//                return if (cursor.moveToFirst()) {
//                    val currentmode: Int = cursor.getInt(cursor.getColumnIndex("currentstate"))
//                    cursor.close()
//                    currentmode
//                } else {
//                    cursor.close()
//                    1
//                }
//            }
//        } catch (throwable: Throwable) {
//            throwable.printStackTrace()
//        }
//        return 1
        return 1
    }


    // 小米手机上，打开自启动管理，打开本app应用权限管理页面
    fun openXiaoMiPermissionEdit(context: Context, extra_pkgname: String) {
        try {
            val intent2 = Intent()
            intent2.action = "miui.intent.action.APP_PERM_EDITOR"
            intent2.addCategory("android.intent.category.DEFAULT")
            intent2.putExtra("extra_pkgname", extra_pkgname)
            context.startActivity(intent2)
        } catch (e: Exception) {
            goAppDetailSetting(context, extra_pkgname)
        }
    }

    // oppo打开权限设置界面
    fun openOppoPermissionEdit(context: Context, extra_pkgname: String) {
        try {
            val intent = Intent()
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            intent.putExtra("packageName", extra_pkgname)
            val comp = ComponentName(
                "com.color.safecenter",
                "com.color.safecenter.permission.PermissionManagerActivity"
            )
            intent.component = comp
            context.startActivity(intent)
        } catch (e: Exception) {
            goAppDetailSetting(context, extra_pkgname)
        }
    }

    // 权限设置界面
    fun goAppDetailSetting(context: Context, extra_pkgname: String) {
        val localIntent = Intent()
        localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        localIntent.action = "android.settings.APPLICATION_DETAILS_SETTINGS"
        localIntent.data = Uri.fromParts("package", extra_pkgname, null)
        try {
            context.startActivity(localIntent)
        } catch (e: Exception) {
            e.printStackTrace()
//            AppUtils.topActivity?.showTip(context.resources.getString(R.string.permission_setting_error))
        }
    }


    object PermissionPageManagement {
        private const val TAG = "JumpPermissionManagement"

        /**
         * Build.MANUFACTURER
         */
        private const val MANUFACTURER_HUAWEI = "HUAWEI" //华为
        private const val MANUFACTURER_MEIZU = "Meizu" //魅族
        private const val MANUFACTURER_XIAOMI = "Xiaomi" //小米
        private const val MANUFACTURER_SONY = "Sony" //索尼
        private const val MANUFACTURER_OPPO = "OPPO" //oppo
        private const val MANUFACTURER_LG = "LG"
        private const val MANUFACTURER_VIVO = "vivo" //vivo
        private const val MANUFACTURER_SAMSUNG = "samsung" //三星
        private const val MANUFACTURER_ZTE = "ZTE" //中兴
        private const val MANUFACTURER_YULONG = "YuLong" //酷派
        private const val MANUFACTURER_LENOVO = "LENOVO" //联想

        /**
         * 此函数可以自己定义
         * @param activity
         */
        @JvmStatic
        public fun goToSetting(activity: Activity) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ApplicationInfo(activity)
            } else {
                when (Build.MANUFACTURER) {
                    MANUFACTURER_HUAWEI -> Huawei(activity)
                    MANUFACTURER_MEIZU -> Meizu(activity)
                    MANUFACTURER_XIAOMI -> Xiaomi(activity)
                    MANUFACTURER_SONY -> Sony(activity)
                    MANUFACTURER_OPPO -> OPPO(activity)
                    MANUFACTURER_VIVO -> VIVO(activity)
                    MANUFACTURER_LG -> LG(activity)
                    else -> {
                        ApplicationInfo(activity)
                        Log.e("goToSetting", "目前暂不支持此系统")
                    }
                }
            }

        }

        fun Huawei(activity: Activity) {
            try {
                val intent = Intent()
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.putExtra("packageName", activity.getApplicationInfo().packageName)
                val comp = ComponentName(
                    "com.huawei.systemmanager",
                    "com.huawei.permissionmanager.ui.MainActivity"
                )
                intent.setComponent(comp)

                activity.startActivityForResult(intent, SETTING_PERMISSION)

            } catch (e: Exception) {
                e.printStackTrace()
                goIntentSetting(activity)
            }
        }

        fun Meizu(activity: Activity) {
            try {
                val intent = Intent("com.meizu.safe.security.SHOW_APPSEC")
                intent.addCategory(Intent.CATEGORY_DEFAULT)
                intent.putExtra("packageName", activity.getPackageName())
                activity.startActivityForResult(intent, SETTING_PERMISSION)

            } catch (e: Exception) {
                e.printStackTrace()
                goIntentSetting(activity)
            }
        }

        fun Xiaomi(activity: Activity) {
            var intent = Intent();
            intent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, SETTING_PERMISSION)
        }

        fun Sony(activity: Activity) {
            try {
                val intent = Intent()
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.putExtra("packageName", activity.getPackageName())

                val comp =
                    ComponentName("com.sonymobile.cta", "com.sonymobile.cta.SomcCTAMainActivity")
                intent.setComponent(comp)
                activity.startActivityForResult(intent, SETTING_PERMISSION)

            } catch (e: Exception) {
                e.printStackTrace()
                goIntentSetting(activity)
            }
        }

        fun OPPO(activity: Activity) {
            try {
                val intent = Intent()
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.putExtra("packageName", activity.getPackageName())
                //        ComponentName comp = new ComponentName("com.color.safecenter", "com.color.safecenter.permission.PermissionManagerActivity");
                val comp = ComponentName(
                    "com.coloros.securitypermission",
                    "com.coloros.securitypermission.permission.PermissionAppAllPermissionActivity"
                ) //R11t 7.1.1 os-v3.2
                intent.setComponent(comp)
                activity.startActivityForResult(intent, SETTING_PERMISSION)

            } catch (e: Exception) {
                e.printStackTrace()
                goIntentSetting(activity)
            }
        }

        fun VIVO(activity: Activity) {
            val localIntent: Intent
            if (Build.MODEL.contains("Y85") && !Build.MODEL.contains("Y85A") || Build.MODEL.contains(
                    "vivo Y53L"
                )
            ) {

                localIntent = Intent()
                localIntent.setClassName(
                    "com.vivo.permissionmanager",
                    "com.vivo.permissionmanager.activity.PurviewTabActivity"
                )
                localIntent.putExtra("packagename", activity.getPackageName())
                localIntent.putExtra("tabId", "1")
                activity.startActivityForResult(localIntent, SETTING_PERMISSION)

            } else {
                localIntent = Intent()
                localIntent.setClassName(
                    "com.vivo.permissionmanager",
                    "com.vivo.permissionmanager.activity.SoftPermissionDetailActivity"
                )
                localIntent.setAction("secure.intent.action.softPermissionDetail")
                localIntent.putExtra("packagename", activity.getPackageName())
                activity.startActivityForResult(localIntent, SETTING_PERMISSION)

            }
        }

        fun LG(activity: Activity) {
            try {
                val intent = Intent("android.intent.action.MAIN")
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.putExtra("packageName", activity.getPackageName())
                val comp = ComponentName(
                    "com.android.settings",
                    "com.android.settings.Settings\$AccessLockSummaryActivity"
                )
                intent.setComponent(comp)
                activity.startActivityForResult(intent, SETTING_PERMISSION)

            } catch (e: Exception) {
                e.printStackTrace()
                goIntentSetting(activity)
            }
        }

        /**
         * 只能打开到自带安全软件
         * @param activity
         */
        fun _360(activity: Activity) {
            val intent = Intent("android.intent.action.MAIN")
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra("packageName", activity.getPackageName())
            val comp = ComponentName(
                "com.qihoo360.mobilesafe",
                "com.qihoo360.mobilesafe.ui.index.AppEnterActivity"
            )
            intent.setComponent(comp)
            activity.startActivityForResult(intent, SETTING_PERMISSION)

        }

        /**
         * 应用信息界面
         * @param activity
         */
        fun ApplicationInfo(activity: Activity) {
            val localIntent = Intent()
            localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            if (Build.VERSION.SDK_INT >= 9) {
                localIntent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS")
                localIntent.setData(Uri.fromParts("package", activity.getPackageName(), null))
            } else if (Build.VERSION.SDK_INT <= 8) {
                localIntent.setAction(Intent.ACTION_VIEW)
                localIntent.setClassName(
                    "com.android.settings",
                    "com.android.settings.InstalledAppDetails"
                )
                localIntent.putExtra(
                    "com.android.settings.ApplicationPkgName",
                    activity.getPackageName()
                )
            }
            activity.startActivityForResult(localIntent, SETTING_PERMISSION)

        }

        /**
         * 系统设置界面
         * @param activity
         */
        fun SystemConfig(activity: Activity) {
            val intent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(intent)
        }

        /**
         * 默认打开应用详细页
         */

        public fun goIntentSetting(pActivity: Activity) {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri: Uri = Uri.fromParts("package", pActivity.getPackageName(), null)
            intent.setData(uri)
            try {
                pActivity.startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 通过这个获取所有的OPS Field, 理论上第三方系统厂商都是修改这个来实现新增权限的, 这个可以解决比如miui的自启动和后台弹出界面等
     * Android 16兼容版本
     */
    fun getAllOPSField(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用已知的常量，避免反射
                val knownOps = mapOf(
                    "OP_COARSE_LOCATION" to AppOpsManager.OPSTR_COARSE_LOCATION.hashCode(),
                    "OP_FINE_LOCATION" to AppOpsManager.OPSTR_FINE_LOCATION.hashCode(),
                    "OP_CAMERA" to AppOpsManager.OPSTR_CAMERA.hashCode(),
                    "OP_RECORD_AUDIO" to AppOpsManager.OPSTR_RECORD_AUDIO.hashCode(),
                    "OP_READ_PHONE_STATE" to AppOpsManager.OPSTR_READ_PHONE_STATE.hashCode(),
                    "OP_WRITE_EXTERNAL_STORAGE" to AppOpsManager.OPSTR_WRITE_EXTERNAL_STORAGE.hashCode(),
                    "OP_READ_EXTERNAL_STORAGE" to AppOpsManager.OPSTR_READ_EXTERNAL_STORAGE.hashCode()
                )
                for ((name, value) in knownOps) {
                    println("$name = $value")
                }
            } else {
                // 低版本使用反射
                val manager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
                val f = manager.javaClass.declaredFields

                for (field in f) {
                    if (field.type == Int::class.java && Modifier.isStatic(field.modifiers)) {
                        field.isAccessible = true
                        println("${field.name} = ${field.get(null)}")
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}