package com.czur.czurwma.utils

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.activity.ComponentActivity
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logW

/**
 * Android 16适配管理器
 * 统一管理所有Android 16相关的适配工作
 */
object Android16AdaptationManager {

    /**
     * 为Activity执行完整的Android 16适配
     */
    fun adaptActivity(activity: ComponentActivity) {
        if (!isAndroid16OrHigher()) {
            logI("Android16AdaptationManager", "Not Android 16+, skipping adaptation")
            return
        }

        logI("Android16AdaptationManager", "Starting Android 16 adaptation for ${activity.javaClass.simpleName}")

        try {
            // 1. Edge-to-Edge适配
            adaptEdgeToEdge(activity)

            // 2. 预测性返回手势适配
            adaptPredictiveBack(activity)

            // 3. 自适应布局适配
            adaptAdaptiveLayout(activity)

            // 4. 记录适配信息
            logAdaptationInfo(activity)

            logI("Android16AdaptationManager", "Android 16 adaptation completed successfully")
        } catch (e: Exception) {
            logW("Android16AdaptationManager", "Android 16 adaptation failed: ${e.message}")
        }
    }

    /**
     * Edge-to-Edge适配
     */
    private fun adaptEdgeToEdge(activity: Activity) {
        try {
            EdgeToEdgeUtils.enableEdgeToEdge(activity)
            logI("Android16AdaptationManager", "Edge-to-Edge adaptation completed")
        } catch (e: Exception) {
            logW("Android16AdaptationManager", "Edge-to-Edge adaptation failed: ${e.message}")
        }
    }

    /**
     * 预测性返回手势适配
     */
    private fun adaptPredictiveBack(activity: ComponentActivity) {
        try {
            PredictiveBackUtils.setupPredictiveBack(activity) {
                // 默认返回行为
                activity.finish()
            }
            logI("Android16AdaptationManager", "Predictive back adaptation completed")
        } catch (e: Exception) {
            logW("Android16AdaptationManager", "Predictive back adaptation failed: ${e.message}")
        }
    }

    /**
     * 自适应布局适配
     */
    private fun adaptAdaptiveLayout(activity: Activity) {
        try {
            AdaptiveLayoutUtils.setupAdaptiveLayout(activity)
            AdaptiveLayoutUtils.logScreenInfo(activity)
            logI("Android16AdaptationManager", "Adaptive layout adaptation completed")
        } catch (e: Exception) {
            logW("Android16AdaptationManager", "Adaptive layout adaptation failed: ${e.message}")
        }
    }

    /**
     * 记录适配信息
     */
    private fun logAdaptationInfo(activity: Activity) {
        val info = buildString {
            appendLine("=== Android 16 Adaptation Info ===")
            appendLine("Activity: ${activity.javaClass.simpleName}")
            appendLine("Android Version: ${getVersionInfo()}")
            appendLine("Edge-to-Edge: ${EdgeToEdgeUtils.isEdgeToEdgeEnabled(activity)}")
            appendLine("Predictive Back: ${PredictiveBackUtils.getBackGestureInfo()}")
            appendLine("Screen Info: ${AdaptiveLayoutUtils.getScreenInfo(activity)}")
            appendLine("Intent Security: ${IntentSecurityUtils.getIntentSecurityInfo()}")
            appendLine("Reflection Compat: ${Android16CompatUtils.needsCompatMode()}")
            appendLine("===================================")
        }
        logI("Android16AdaptationManager", info)
    }

    /**
     * 检查适配状态
     */
    fun checkAdaptationStatus(activity: Activity): AdaptationStatus {
        val issues = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        if (!isAndroid16OrHigher()) {
            return AdaptationStatus(
                isFullyAdapted = true,
                issues = emptyList(),
                warnings = listOf("Not running on Android 16+")
            )
        }

        // 检查Edge-to-Edge
        if (!EdgeToEdgeUtils.isEdgeToEdgeEnabled(activity)) {
            issues.add("Edge-to-Edge not enabled")
        }

        // 检查预测性返回手势
        if (!PredictiveBackUtils.isPredictiveBackSupported()) {
            warnings.add("Predictive back not supported on this device")
        }

        // 检查自适应布局
        if (AdaptiveLayoutUtils.needsAdaptiveLayout(activity)) {
            val screenInfo = AdaptiveLayoutUtils.getScreenInfo(activity)
            if (screenInfo.category == AdaptiveLayoutUtils.ScreenSizeCategory.SMALL) {
                warnings.add("Small screen detected, adaptive layout may not be necessary")
            }
        }

        return AdaptationStatus(
            isFullyAdapted = issues.isEmpty(),
            issues = issues,
            warnings = warnings
        )
    }

    /**
     * 适配状态数据类
     */
    data class AdaptationStatus(
        val isFullyAdapted: Boolean,
        val issues: List<String>,
        val warnings: List<String>
    ) {
        fun hasIssues(): Boolean = issues.isNotEmpty()
        fun hasWarnings(): Boolean = warnings.isNotEmpty()

        override fun toString(): String {
            return buildString {
                appendLine("Adaptation Status: ${if (isFullyAdapted) "FULLY_ADAPTED" else "NEEDS_ATTENTION"}")
                if (issues.isNotEmpty()) {
                    appendLine("Issues:")
                    issues.forEach { appendLine("  - $it") }
                }
                if (warnings.isNotEmpty()) {
                    appendLine("Warnings:")
                    warnings.forEach { appendLine("  - $it") }
                }
            }
        }
    }

    /**
     * 获取适配建议
     */
    fun getAdaptationRecommendations(activity: Activity): List<String> {
        val recommendations = mutableListOf<String>()

        if (!isAndroid16OrHigher()) {
            recommendations.add("Consider testing on Android 16+ devices")
            return recommendations
        }

        val status = checkAdaptationStatus(activity)

        if (!EdgeToEdgeUtils.isEdgeToEdgeEnabled(activity)) {
            recommendations.add("Enable Edge-to-Edge for better user experience")
        }

        val screenInfo = AdaptiveLayoutUtils.getScreenInfo(activity)
        when (screenInfo.category) {
            AdaptiveLayoutUtils.ScreenSizeCategory.LARGE,
            AdaptiveLayoutUtils.ScreenSizeCategory.XLARGE -> {
                if (!AdaptiveLayoutUtils.shouldUseTwoPaneLayout(activity)) {
                    recommendations.add("Consider implementing two-pane layout for large screens")
                }
            }
            else -> {
                // 小屏设备的建议
                recommendations.add("Optimize layout for small screens")
            }
        }

        if (status.hasIssues()) {
            recommendations.add("Fix adaptation issues: ${status.issues.joinToString(", ")}")
        }

        return recommendations
    }

    /**
     * 执行运行时适配检查
     */
    fun performRuntimeCheck(context: Context): RuntimeCheckResult {
        val results = mutableMapOf<String, Boolean>()
        val details = mutableMapOf<String, String>()

        // 检查反射兼容性
        try {
            val processName = Android16CompatUtils.getProcessName()
            results["reflection_compat"] = processName.isNotEmpty()
            details["reflection_compat"] = "Process name: $processName"
        } catch (e: Exception) {
            results["reflection_compat"] = false
            details["reflection_compat"] = "Error: ${e.message}"
        }

        // 检查AppOps权限
        try {
            val canCheckOps = Android16CompatUtils.checkOp(context, 0)
            results["appops_compat"] = true
            details["appops_compat"] = "Check result: $canCheckOps"
        } catch (e: Exception) {
            results["appops_compat"] = false
            details["appops_compat"] = "Error: ${e.message}"
        }

        // 检查Intent安全性
        try {
            val securityInfo = IntentSecurityUtils.getIntentSecurityInfo()
            results["intent_security"] = true
            details["intent_security"] = securityInfo
        } catch (e: Exception) {
            results["intent_security"] = false
            details["intent_security"] = "Error: ${e.message}"
        }

        return RuntimeCheckResult(results, details)
    }

    /**
     * 运行时检查结果
     */
    data class RuntimeCheckResult(
        val results: Map<String, Boolean>,
        val details: Map<String, String>
    ) {
        fun isAllPassed(): Boolean = results.values.all { it }
        
        fun getFailedChecks(): List<String> = results.filter { !it.value }.keys.toList()

        override fun toString(): String {
            return buildString {
                appendLine("Runtime Check Results:")
                results.forEach { (check, passed) ->
                    val status = if (passed) "PASS" else "FAIL"
                    appendLine("  $check: $status - ${details[check]}")
                }
            }
        }
    }

    /**
     * 检查是否为Android 16+
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }

    /**
     * 获取版本信息
     */
    fun getVersionInfo(): String {
        return Android16CompatUtils.getVersionInfo()
    }

    /**
     * 获取完整的适配报告
     */
    fun generateAdaptationReport(activity: Activity): String {
        return buildString {
            appendLine("=== Android 16 Adaptation Report ===")
            appendLine("Generated at: ${System.currentTimeMillis()}")
            appendLine("Activity: ${activity.javaClass.simpleName}")
            appendLine()
            
            appendLine("System Information:")
            appendLine("  ${getVersionInfo()}")
            appendLine("  Android 16+: ${isAndroid16OrHigher()}")
            appendLine()
            
            val status = checkAdaptationStatus(activity)
            appendLine("Adaptation Status:")
            appendLine("  ${status}")
            appendLine()
            
            val recommendations = getAdaptationRecommendations(activity)
            if (recommendations.isNotEmpty()) {
                appendLine("Recommendations:")
                recommendations.forEach { appendLine("  - $it") }
                appendLine()
            }
            
            val runtimeCheck = performRuntimeCheck(activity)
            appendLine("Runtime Check:")
            appendLine("  ${runtimeCheck}")
            
            appendLine("=====================================")
        }
    }
}
