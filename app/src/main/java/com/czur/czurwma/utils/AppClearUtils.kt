package com.czur.czurwma.utils

import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.ServiceUtils
import com.czur.czurutils.log.logI
import com.czur.czurwma.R
import com.czur.czurwma.common.EshareConstants
import com.czur.czurwma.eshare.TAG
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.service.EShareHeartBeatService

object AppClearUtils {

    private var context: Application? = null

    private fun checkPermissions(context : Context): <PERSON>olean {
        // 如果Android版本大于31
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Settings.canDrawOverlays(context)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
                    && PermissionUtils.isGranted(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            Settings.canDrawOverlays(context)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
        }
    }
    // EShare Permission
    @JvmStatic
    @JvmOverloads
    fun checkESharePermission(
        context: Context,
        activity: Activity,
        isMoreBtn: Boolean = false,
        allowPermission: (Boolean) -> Unit
    ) {
        if (checkPermissions(context)) {
            logI("${TAG}.checkPermission.权限已开启")
            //权限开启状态
            ESharePreferences.getInstance().moreBtnVisible = isMoreBtn
            ESharePreferences.getInstance().backBtnVisible = true
            allowPermission(true)
        } else { //权限关闭状态
            val explainStr = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.getString(R.string.eshare_mic_overlay_bt_permission_explain)
            }else{
                context.getString(R.string.eshare_mic_overlay_permission_explain)
            }
            logI("${TAG}.checkPermission.权限未开启")
            PermissionUtil.checkPermissionWithDialog(
                context,
                context.getString(R.string.starry_popupwindow_title),
                explainStr,
                context.getString(R.string.go_setting),
                context.getString(R.string.starry_background_start_msg_cancel)
            ) {
                if (it != null) { //点击去设置
                    val permissionList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                        arrayOf(Manifest.permission.RECORD_AUDIO,
                            Manifest.permission.BLUETOOTH_CONNECT)
                    }else{
                        arrayOf(Manifest.permission.RECORD_AUDIO)
                    }

                    PermissionUtil.useToolsRequestPermission(
                        permissionList,
                        object : PermissionCallBack {
                            override fun execute() {
                                if (!Settings.canDrawOverlays(context)) {
                                    LogUtils.i("${TAG}.checkPermission.当前无权限，请授权")
                                    ActivityUtils.startActivityForResult(
                                        activity,
                                        Intent(
                                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                            Uri.parse("package:" + context!!.packageName)
                                        ),
                                        EshareConstants.RESULT_CHECK_OVERLAYS_CODE
                                    )
                                }
                            }
                        }
                    )
                } else {//点击取消
                    allowPermission(false)
                }
            }
        }
    }



    // EShare Permission
    @JvmStatic
    @JvmOverloads
    fun checkCameraPermission(
        context: Context,
        activity: Activity,
        isMoreBtn: Boolean = false,
        allow: () -> Unit
    ) {
        if (PermissionUtils.isGranted(Manifest.permission.CAMERA,
                Manifest.permission.VIBRATE)) {
            logI( "${TAG}.checkPermission.权限已开启")
            //权限开启
            allow()
        } else { //权限关闭状态
            logI( "${TAG}.checkPermission.权限未开启")
            PermissionUtil.checkPermissionWithDialog(
                context,
                context.getString(R.string.starry_popupwindow_title),
                context.getString(R.string.eshare_camera_permission_explain),
                context.getString(R.string.go_setting),
                context.getString(R.string.starry_background_start_msg_cancel)
            ) {
                if (it != null) { //点击去设置
                    PermissionUtil.useToolsRequestPermission(
                        arrayOf(Manifest.permission.CAMERA,
                            Manifest.permission.VIBRATE),
                        object : PermissionCallBack {
                            override fun execute() {

                            }
                        }
                    )
                } else {//点击取消
                }
            }
        }
    }


    // 开启/关闭前台服务（长连接）
    @JvmStatic
    fun startScreenNotify() {
        logI("${TAG}.startScreenNotify()")
        if (!ServiceUtils.isServiceRunning(EShareHeartBeatService::class.java)) {
            EShareHeartBeatService.startService(ActivityUtils.getTopActivity())
        }
    }


}