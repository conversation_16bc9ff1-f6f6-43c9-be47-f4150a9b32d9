package com.czur.czurwma.utils

import android.app.Activity
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logW

/**
 * 自适应布局适配工具类
 * Android 16在大屏设备上强制自适应布局
 */
object AdaptiveLayoutUtils {

    /**
     * 检查是否为大屏设备
     */
    fun isLargeScreen(activity: Activity): Boolean {
        val displayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = activity.display
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            activity.windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
        val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
        val smallestWidthDp = minOf(screenWidthDp, screenHeightDp)
        
        // 600dp以上认为是大屏设备
        return smallestWidthDp >= 600
    }

    /**
     * 检查是否为平板设备
     */
    fun isTablet(activity: Activity): Boolean {
        val configuration = activity.resources.configuration
        return (configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK) >= 
               Configuration.SCREENLAYOUT_SIZE_LARGE
    }

    /**
     * 获取屏幕尺寸分类
     */
    fun getScreenSizeCategory(activity: Activity): ScreenSizeCategory {
        val displayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = activity.display
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            activity.windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
        val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
        val smallestWidthDp = minOf(screenWidthDp, screenHeightDp)
        
        return when {
            smallestWidthDp >= 960 -> ScreenSizeCategory.XLARGE
            smallestWidthDp >= 720 -> ScreenSizeCategory.LARGE
            smallestWidthDp >= 600 -> ScreenSizeCategory.MEDIUM
            else -> ScreenSizeCategory.SMALL
        }
    }

    /**
     * 屏幕尺寸分类
     */
    enum class ScreenSizeCategory {
        SMALL,   // 手机
        MEDIUM,  // 小平板
        LARGE,   // 大平板
        XLARGE   // 超大屏
    }

    /**
     * 检查是否需要适配自适应布局
     */
    fun needsAdaptiveLayout(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= 36) { // Android 16
            // Android 16在大屏设备上强制自适应布局
            isLargeScreen(activity)
        } else {
            false
        }
    }

    /**
     * 设置Activity的自适应布局
     */
    fun setupAdaptiveLayout(activity: Activity) {
        if (!needsAdaptiveLayout(activity)) {
            logI("AdaptiveLayoutUtils", "No adaptive layout needed for ${activity.javaClass.simpleName}")
            return
        }

        logI("AdaptiveLayoutUtils", "Setting up adaptive layout for ${activity.javaClass.simpleName}")
        
        // Android 16在大屏设备上不允许强制横竖屏
        if (Build.VERSION.SDK_INT >= 36) {
            setupAdaptiveLayoutApi36(activity)
        }
    }

    /**
     * Android 16的自适应布局设置
     */
    @RequiresApi(36)
    private fun setupAdaptiveLayoutApi36(activity: Activity) {
        try {
            // 移除强制的屏幕方向设置
            activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
            
            // 允许窗口调整大小
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                activity.window.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
            }
            
            logI("AdaptiveLayoutUtils", "Adaptive layout configured for Android 16")
        } catch (e: Exception) {
            logW("AdaptiveLayoutUtils", "Failed to setup adaptive layout: ${e.message}")
        }
    }

    /**
     * 获取推荐的列数（用于网格布局）
     */
    fun getRecommendedColumnCount(activity: Activity): Int {
        val screenSize = getScreenSizeCategory(activity)
        val orientation = activity.resources.configuration.orientation
        
        return when (screenSize) {
            ScreenSizeCategory.SMALL -> if (orientation == Configuration.ORIENTATION_PORTRAIT) 2 else 3
            ScreenSizeCategory.MEDIUM -> if (orientation == Configuration.ORIENTATION_PORTRAIT) 3 else 4
            ScreenSizeCategory.LARGE -> if (orientation == Configuration.ORIENTATION_PORTRAIT) 4 else 6
            ScreenSizeCategory.XLARGE -> if (orientation == Configuration.ORIENTATION_PORTRAIT) 5 else 8
        }
    }

    /**
     * 获取推荐的边距（dp）
     */
    fun getRecommendedMargin(activity: Activity): Int {
        val screenSize = getScreenSizeCategory(activity)
        
        return when (screenSize) {
            ScreenSizeCategory.SMALL -> 16
            ScreenSizeCategory.MEDIUM -> 24
            ScreenSizeCategory.LARGE -> 32
            ScreenSizeCategory.XLARGE -> 48
        }
    }

    /**
     * 检查是否应该使用双窗格布局
     */
    fun shouldUseTwoPaneLayout(activity: Activity): Boolean {
        val screenSize = getScreenSizeCategory(activity)
        val orientation = activity.resources.configuration.orientation
        
        return when (screenSize) {
            ScreenSizeCategory.SMALL -> false
            ScreenSizeCategory.MEDIUM -> orientation == Configuration.ORIENTATION_LANDSCAPE
            ScreenSizeCategory.LARGE, ScreenSizeCategory.XLARGE -> true
        }
    }

    /**
     * 获取屏幕信息
     */
    fun getScreenInfo(activity: Activity): ScreenInfo {
        val displayMetrics = DisplayMetrics()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = activity.display
            display?.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            activity.windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        val widthDp = displayMetrics.widthPixels / displayMetrics.density
        val heightDp = displayMetrics.heightPixels / displayMetrics.density
        val smallestWidthDp = minOf(widthDp, heightDp)
        val largestWidthDp = maxOf(widthDp, heightDp)
        
        return ScreenInfo(
            widthPixels = displayMetrics.widthPixels,
            heightPixels = displayMetrics.heightPixels,
            widthDp = widthDp,
            heightDp = heightDp,
            smallestWidthDp = smallestWidthDp,
            largestWidthDp = largestWidthDp,
            density = displayMetrics.density,
            densityDpi = displayMetrics.densityDpi,
            category = getScreenSizeCategory(activity),
            isLargeScreen = isLargeScreen(activity),
            isTablet = isTablet(activity)
        )
    }

    /**
     * 屏幕信息数据类
     */
    data class ScreenInfo(
        val widthPixels: Int,
        val heightPixels: Int,
        val widthDp: Float,
        val heightDp: Float,
        val smallestWidthDp: Float,
        val largestWidthDp: Float,
        val density: Float,
        val densityDpi: Int,
        val category: ScreenSizeCategory,
        val isLargeScreen: Boolean,
        val isTablet: Boolean
    ) {
        override fun toString(): String {
            return "ScreenInfo(${widthPixels}x${heightPixels}px, ${widthDp.toInt()}x${heightDp.toInt()}dp, " +
                   "sw${smallestWidthDp.toInt()}dp, ${category.name}, density=${density})"
        }
    }

    /**
     * 日志输出屏幕信息
     */
    fun logScreenInfo(activity: Activity) {
        val screenInfo = getScreenInfo(activity)
        logI("AdaptiveLayoutUtils", "Screen info for ${activity.javaClass.simpleName}: $screenInfo")
    }

    /**
     * 检查是否为Android 16+
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }
}
