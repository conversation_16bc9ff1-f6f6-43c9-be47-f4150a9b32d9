package com.czur.czurwma.utils

import android.app.Application
import com.czur.czurutils.log.initLogUtil
import com.czur.czurwma.common.CZURConstants
import java.io.File

const val CZURLogTag = "CZUR"

object AppInitUtils {

    // 初始化CZURLog日志系统
    @JvmStatic
    fun initCZURLog(application: Application){
        initLogUtil(application) {
            configTag = "CZUR"//默认tag,默认 czurLogUtil

            // log输出位置设置
            logOutputSwitch {
                consoleShowAll = true   // 输出到控制台
                fileSaveAll = true      // 保存到文件中
            }

            // log显示样式设置
            logStyle {
                showHeader = true      // 显示头
                showArgIndex = true    // 显示参数索引
            }

            // log保存文件设置
            //CZURConstants.MIRROR_PATH + "log/";
            logSaveConfig {
                customLogSaveDir = File(CZURConstants.MIRROR_PATH, "log/") // Log保存位置
                maxSaveDays = 7 // 保存天数
                maxFileCount = 5    // 最多保存5个文件
                maxFileSizeInByte = 5 * 1024 * 1024     // 单个文件最大5MB
                logFileExtension = "log"    // 文件扩展名

                showInfoEachLine = false    // 不显示每行的Log信息

                logFileHead {
                    addFileHead = true // 是否添加头信息
                }
            }
        }
    }

}