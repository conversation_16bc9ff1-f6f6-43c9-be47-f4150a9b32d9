package com.czur.czurwma.utils

import android.app.Activity
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.RequiresApi
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.czur.czurutils.log.logI

/**
 * Edge-to-Edge适配工具类
 * Android 16强制启用Edge-to-Edge，需要适配
 */
object EdgeToEdgeUtils {

    /**
     * 为Activity启用Edge-to-Edge
     */
    fun enableEdgeToEdge(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            enableEdgeToEdgeApi30(activity)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            enableEdgeToEdgeApi21(activity)
        }
        logI("EdgeToEdgeUtils", "Edge-to-Edge enabled for ${activity.javaClass.simpleName}")
    }

    /**
     * Android 11+ (API 30+) 的Edge-to-Edge实现
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private fun enableEdgeToEdgeApi30(activity: Activity) {
        val window = activity.window
        
        // 设置状态栏和导航栏透明
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // 设置状态栏和导航栏颜色
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT
        
        // 设置状态栏和导航栏文字颜色
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.isAppearanceLightStatusBars = true
        controller.isAppearanceLightNavigationBars = true
    }

    /**
     * Android 5+ (API 21+) 的Edge-to-Edge实现
     */
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    private fun enableEdgeToEdgeApi21(activity: Activity) {
        val window = activity.window
        
        // 设置系统UI标志
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
        
        // 设置状态栏和导航栏颜色
        window.statusBarColor = Color.TRANSPARENT
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            window.navigationBarColor = Color.TRANSPARENT
        }
        
        // 设置状态栏文字颜色
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or 
                View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
    }

    /**
     * 为View设置系统窗口插入处理
     */
    fun applySystemWindowInsets(view: View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            val paddingLeft = v.paddingLeft
            val paddingRight = v.paddingRight
            val paddingTop = if (applyTop) systemBars.top else v.paddingTop
            val paddingBottom = if (applyBottom) systemBars.bottom else v.paddingBottom
            
            v.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
            insets
        }
    }

    /**
     * 为ViewGroup设置系统窗口插入处理（使用margin）
     */
    fun applySystemWindowInsetsWithMargin(
        view: View, 
        applyTop: Boolean = true, 
        applyBottom: Boolean = true
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            val layoutParams = v.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParams?.let { params ->
                val originalTopMargin = params.topMargin
                val originalBottomMargin = params.bottomMargin
                
                params.topMargin = if (applyTop) systemBars.top else originalTopMargin
                params.bottomMargin = if (applyBottom) systemBars.bottom else originalBottomMargin
                
                v.layoutParams = params
            }
            insets
        }
    }

    /**
     * 获取状态栏高度
     */
    fun getStatusBarHeight(activity: Activity): Int {
        return ViewCompat.getRootWindowInsets(activity.window.decorView)
            ?.getInsets(WindowInsetsCompat.Type.statusBars())?.top ?: 0
    }

    /**
     * 获取导航栏高度
     */
    fun getNavigationBarHeight(activity: Activity): Int {
        return ViewCompat.getRootWindowInsets(activity.window.decorView)
            ?.getInsets(WindowInsetsCompat.Type.navigationBars())?.bottom ?: 0
    }

    /**
     * 检查是否启用了Edge-to-Edge
     */
    fun isEdgeToEdgeEnabled(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            !WindowCompat.getInsetsController(activity.window, activity.window.decorView)
                .systemBarsBehavior.equals(WindowInsetsControllerCompat.BEHAVIOR_SHOW_BARS_BY_TOUCH)
        } else {
            // 对于低版本，检查系统UI标志
            val flags = activity.window.decorView.systemUiVisibility
            (flags and View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN) != 0
        }
    }

    /**
     * 为RecyclerView或ScrollView设置Edge-to-Edge适配
     */
    fun applyEdgeToEdgeForScrollView(scrollView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(scrollView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            // 为滚动视图设置padding，确保内容不被系统栏遮挡
            v.setPadding(
                v.paddingLeft,
                systemBars.top,
                v.paddingRight,
                systemBars.bottom
            )
            
            // 设置clipToPadding为false，允许内容滚动到padding区域
            if (v is ViewGroup) {
                v.clipToPadding = false
            }
            
            insets
        }
    }

    /**
     * 为Toolbar设置Edge-to-Edge适配
     */
    fun applyEdgeToEdgeForToolbar(toolbar: View) {
        ViewCompat.setOnApplyWindowInsetsListener(toolbar) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            // 为Toolbar设置top margin，避免被状态栏遮挡
            val layoutParams = v.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParams?.let { params ->
                params.topMargin = systemBars.top
                v.layoutParams = params
            }
            
            insets
        }
    }

    /**
     * 为底部导航栏设置Edge-to-Edge适配
     */
    fun applyEdgeToEdgeForBottomNavigation(bottomNav: View) {
        ViewCompat.setOnApplyWindowInsetsListener(bottomNav) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            // 为底部导航设置bottom margin，避免被导航栏遮挡
            val layoutParams = v.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParams?.let { params ->
                params.bottomMargin = systemBars.bottom
                v.layoutParams = params
            }
            
            insets
        }
    }

    /**
     * 检查是否为Android 16+
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }

    /**
     * 获取系统窗口插入信息
     */
    fun getSystemWindowInsets(activity: Activity): WindowInsetsCompat? {
        return ViewCompat.getRootWindowInsets(activity.window.decorView)
    }
}
