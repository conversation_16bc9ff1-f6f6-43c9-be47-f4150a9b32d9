package com.czur.czurwma.utils

import android.text.Spannable
import android.text.method.LinkMovementMethod
import android.text.method.MovementMethod

import android.text.method.ScrollingMovementMethod
import android.view.MotionEvent
import android.widget.TextView


class CustomLinkMovementMethod : LinkMovementMethod() {
    private var mScrollingMovementMethod: MovementMethod? =
        ScrollingMovementMethod.getInstance()

    override fun onTouchEvent(
        widget: TextView,
        buffer: Spannable,
        event: MotionEvent
    ): Boolean {
        val handled = super.onTouchEvent(widget, buffer, event)
        return handled || mScrollingMovementMethod?.onTouchEvent(widget, buffer, event) ?: false
    }

    companion object {
        var instance: MovementMethod? = null
            get() {
                if (field == null) {
                    field = CustomLinkMovementMethod()
                }
                return field
            }
    }
}


