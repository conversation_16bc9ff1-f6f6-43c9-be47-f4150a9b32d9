package com.czur.czurwma.utils

import android.annotation.SuppressLint
import android.content.Context
import android.util.TypedValue
import android.view.View
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern

object Tools {

    private const val BUTTON_DISABLE_ALPHA4 = 0.4f
    private const val BUTTON_NO_ALPHA = 1.0f

    /**
     * 获取字符数量 汉字占2个长度，英文占1个长度
     * @param text
     * @return
     */
    fun getTextLength(text: String): Int {
        var length = 0
        for (element in text) {
            if (element.code > 255) {
                length += 2
            } else {
                length++
            }
        }
        return length
    }

    // "createTime": 1629850781000,
    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getFormatDateFromStamp(strStamp: String):String{
        if (strStamp.length > 13){
            return strStamp
        }

        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val netDate = Date(strStamp.toLong())
            sdf.format(netDate)
        } catch (e: Exception) {
            ""
        }

    }

    // 使按钮置灰、不可点击 false 或 可以点击 true
    fun setViewButtonEnable(view: View, flag: Boolean, alpha: Float) {
        if (Validator.isEmpty(view)) {
            return
        }
        if (flag) {
            view.alpha = BUTTON_NO_ALPHA
        } else {
            view.alpha = alpha
        }
        view.visibility = View.VISIBLE
        view.isEnabled = flag
        view.isClickable = flag
    }

    @JvmStatic
    fun setViewButtonEnable(view: View, flag: Boolean) {
        setViewButtonEnable(view, flag, BUTTON_DISABLE_ALPHA4)
    }


    // 会议时长：从会议开始至会议结束的时长显示，01:05:14（时分秒）
    fun meetingTimeLongFormat(timeLong: Int): String{
        var retTime = ""
        val longTime = timeLong.toLong()
        val h = TimeUnit.SECONDS.toHours(longTime).toInt()
        val min = (longTime - 60*60*h)
        val m = TimeUnit.SECONDS.toMinutes(min).toInt()
        val s = (min % 60).toInt()

        retTime = String.format("%02d:%02d:%02d", h, m, s)
        return retTime
    }

    // 最近会议 时间显示逻辑：
    // 当天精确到时分、
    // 昨天、
    // 星期（3-6天）、
    // 月日（超过6天）、
    // 年月日（超过当年）
    // 2021-08-23 13:50:22
    @JvmStatic
    fun getFormatDateTimeForRecently(dateTime: String): String {
//        if (dateTime == ""){
//            return ""
//        }
//
//        val now = getNowDateTime()
//
//        val dStart = getFormatTime(dateTime, "yyyy-MM-dd")
//        val dEnd = getFormatTime(now, "yyyy-MM-dd")
//        val diff = daysBetweenDatesDay(dStart, dEnd)
////        println("日期1为:${dStart};\n日期2为:${dEnd}")
////        println("当前日期和时间差为:${diff}天")
//        val oldYear = getFormatTime(dateTime, "yyyy")
//        val nowYear = getFormatTime(now, "yyyy")
//
//        return if (diff < 1L) {
//            getFormatTime(dateTime, "HH:mm")
//        } else if (diff == 1L) {
//            CZURAtyManager.appContext.getString(R.string.starry_recently_yesterday)
//        } else if (diff in 2L..6L) {
////            getWeekName(dateTime)
//            getWeekNameNew(dateTime)
//        } else {
//            if (oldYear == nowYear){
//                getFormatTime(dateTime, "MM/dd")
//            }else{
//                getFormatTime(dateTime, "yyyy/MM/dd")
//            }
//        }
        return ""
    }

    // 会议详情
    // 时间显示逻辑：当天显示上下午+时分（24小时制）；上一日显示昨天；近一年显示月日；超过一年显示年月日
    //  当日：今天  13:44
//      上一日：昨天 10:44
//      本年内：7月1日 10:44
//      非本年:2020/01/01 10:44
    fun getFormatDateTimeForMeetDetail(dateTime: String): String {
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
//            return dateTime
//        }
//        if (dateTime == ""){
//            return ""
//        }
//
//        val now = getNowDateTime()
//        val dStart = getFormatTime(dateTime, "yyyy-MM-dd")
//        val dEnd = getFormatTime(now, "yyyy-MM-dd")
//        val diff = daysBetweenDatesDay(dStart, dEnd)
//        val oldYear = getFormatTime(dateTime, "yyyy")
//        val nowYear = getFormatTime(now, "yyyy")
//
//        return if (diff < 1L) {
//            getFormatTime(dateTime, "HH:mm") +
//                    "\r\n" +
//                    CZURAtyManager.appContext.getString(R.string.starry_recently_today)
//
//        } else if (diff == 1L) {
//            getFormatTime(dateTime, "HH:mm") +
//                    "\r\n" +
//                    CZURAtyManager.appContext.getString(R.string.starry_recently_yesterday)
//
////        } else if (diff < 365L) {
////            getFormatTime(dateTime, "H:mm\r\nMM/dd")
//        } else {
//            if (oldYear == nowYear){
//                getFormatTime(dateTime, "HH:mm\r\nMM/dd")
//            }else{
//                getFormatTime(dateTime, "HH:mm\r\nyyyy/MM/dd")
//            }
//        }

        return ""
    }

    // 获取星期的序号
    private fun getDayofWeek(dateTime: String): Int {
        val cal = Calendar.getInstance()
        if (dateTime == "") {
            cal.time = Date(System.currentTimeMillis())
        } else {
            val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            var date: Date?
            try {
                date = sdf.parse(dateTime)
            } catch (e: ParseException) {
                date = null
                e.printStackTrace()
            }
            if (date != null) {
                cal.time = Date(date.time)
            }
        }
        return cal[Calendar.DAY_OF_WEEK]
    }

    // 获取星期的中文名
//    private fun getWeekNameNew(dateTime: String): String {
//        var week = ""
//
//        week = when (getDayofWeek(dateTime)) {
//            1 -> getString(R.string.starry_week_1)
//            2 -> getString(R.string.starry_week_2)
//            3 -> getString(R.string.starry_week_3)
//            4 -> getString(R.string.starry_week_4)
//            5 -> getString(R.string.starry_week_5)
//            6 -> getString(R.string.starry_week_6)
//            7 -> getString(R.string.starry_week_7)
//            else -> ""
//        }
//        return week
//    }

    // 获取规定的格式的返回
    // formate: "yyyy-MM-dd"， "HH:mm"
//    @RequiresApi(Build.VERSION_CODES.O)
    fun getFormatTime(oldDate: String, formatStr: String): String{
        if (oldDate == ""){
            return ""
        }
        val format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val date1 = LocalDateTime.parse(oldDate, format)
        val format2 = DateTimeFormatter.ofPattern(formatStr)
        return date1.format(format2)
    }

    // 获取2个时间相差 几天---yyyy-MM-dd
//    @RequiresApi(Build.VERSION_CODES.O)
    private fun daysBetweenDatesDay(start: String, end: String): Long {
        val format = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val mStart = LocalDate.parse(start, format)
        val mEnd = LocalDate.parse(end, format)
        return ChronoUnit.DAYS.between(mStart, mEnd)
    }

    // 获取当前日期时间
    @JvmStatic
    fun getNowDateTime(): String {
        val current = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        return current.format(formatter)
    }

    /**
     * 是否包含表情
     * @return
     */
    @JvmStatic
    fun containsEmoji(source: String): Boolean {
        if (source.isEmpty()){
            return false
        }
        val len = source.length
        for (i in 0 until len) {
            val codePoint = source[i]
            if (isEmojiCharacter(codePoint)
                || isEmojiCharType(codePoint)
                || isEmoji(codePoint)) {
                return true
            }
        }
        return false
    }

    private fun isEmojiCharacter(codePoint: Char): Boolean {
        return !(codePoint.code == 0x0
                || codePoint.code == 0x9
                || codePoint.code == 0xA
                || codePoint.code == 0xD
                || codePoint.code in 0x20..0xD7FF
                || codePoint.code in 0xE000..0xFFFD
                || codePoint.code in 0x10000..0x10FFFF)
    }

    private fun isEmoji(codePoint:Char) :Boolean {

        return when (codePoint.code) {

            in 0x1f601..0x1f64f ->true

            in 0x2702..0x27b0 ->true

            in 0x1f680..0x1f6c0 ->true

            0x24c2,in 0x1f170..0x1f251 ->true

            0x00a9,0x00ae,0x203c,0x2049,
            0x002320e3,0x003820e3,0x003920e3,0x003720e3,0x003020e3,0x003620e3,
            0x003520e3,0x003420e3,0x003320e3,0x003220e3,0x003120e3,
            in 0x2122..0x21aa,
            in 0x231a..0x23f3,
            in 0x25aa..0x25fe,
            in 0x2600..0x26fd,
            in 0x2934..0x2935,
            in 0x2b05..0x2b55,
            0x3030,0x303d,0x3297,0x3299,
            0x1f004,0x1f0cf,
            in 0x1f300..0x1f5ff ->true

            in 0x1f600..0x1f636 ->true

            in 0x1f681..0x1f6c5 ->true

            in 0x1f30d..0x1f567 ->true

            else ->false
        }
    }

    private fun isEmojiCharType(codePoint: Char): Boolean {
        return Character.getType(codePoint) == Character.SURROGATE.toInt()
                || Character.getType(codePoint) == Character.OTHER_SYMBOL.toInt()
    }

    /**
     * 判断是否包含特殊字符
     * @return  false:未包含 true：包含
     */
    @JvmStatic
    fun inputJudge(editText: String?): Boolean {
        val speChat = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]"
        val pattern = Pattern.compile(speChat)
        val matcher = pattern.matcher(editText)
        return matcher.find()
    }

    // 从剪贴板字符串中获取会议名称等信息
    @JvmStatic
    fun getMeetingNameFromClipboard(ret: String, key: String): String{
        val keyLine = "\n"
        var retStr = ""
        val indx = ret.indexOf(key)
        if (indx > 0){
            var indx2 = ret.indexOf(keyLine, indx)
            if (indx2 < 1){
                indx2 = ret.length - indx - key.length
            }
            retStr = ret.substring(indx + key.length, indx2)
        }
        return retStr
    }

    @JvmStatic
    fun formateMeetCode(code: String, format: String = " "): String{
        if (code.length < 6){
            return code
        }
        val ret1 = code.substring(0,3)
        val ret2 = code.substring(3,6)
        val ret3 = code.substring(6)
        return "$ret1$format$ret2$format$ret3"
    }

    @JvmStatic
    fun resumeMeetCode(code: String, format: String = " "): String {
        return code.replace(format, "")
    }

    @JvmStatic
    fun onRandMeetingPassword(): String{
        val list = listOf(0,1,2,3,4,5,6,7,8,9)
        val n = 6
        var anyStr = ""
        for (i in 1..n) {
            anyStr += list.random().toString()
        }
//        Log.i("StarryViewModel","onRandMeetingPwd.anyStr=${anyStr}")
        return anyStr
    }

    // 不足6位，末尾补0
    fun onMakeUpPwd(pwd: String): String{
        val max_len = 6
        val number = 0
        var ret = pwd ?: ""
        if (ret.length < max_len){
            val l = max_len - ret.length
            ret = String.format("%s%0${l}d", ret, number)
        }

        return ret
    }

    @JvmStatic
    fun getUUIDFromUrl(url: String): String{
        // https://meeting-test.czur.cc/m/900cd03977384cc1ad1bddd6a88443df
        if (url.length < 32){
            return url
        }
        return url.substring(url.length-32).trim()
    }
}
fun dp2px(context: Context, value: Int): Int {
    return  dp2px(context, value.toFloat())
}
fun dp2px(context: Context, value: Float): Int {
    return (TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        value,
        context.resources.displayMetrics
    ) + 0.5f).toInt()
}