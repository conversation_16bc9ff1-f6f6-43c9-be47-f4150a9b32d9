package com.czur.czurwma.utils

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.net.*
import android.net.wifi.*
import android.os.Build
import android.os.Handler
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import com.czur.czurutils.log.logTagI

/**
 * android9.0需要打开位置权限
 * mWifiConfiguration：是指手机本身保存过的wifi
 *
 * Level>-50    信号最强4格
 * -50<Level></Level><-65  信号3格
 * -65<Level></Level><-75  信号2格
 * -75<Level></Level><-90  信号1格
 * -90<Level 信号0格></Level>
 *
 * 可能会有点报错，线程改协程就可以了
 */

class WifiUtils private constructor(var context: Context) : BroadcastReceiver() {
    // 定义WifiManager对象
    private val mWifiManager: WifiManager
    // 定义WifiInfo对象
    private val mWifiInfo: WifiInfo?
    // 扫描出的网络连接列表
    // 得到网络列表
    var wifiList: List<ScanResult>? = null
        private set
    // 网络连接列表
    // 得到配置好的网络
    val configuration: List<WifiConfiguration>
    val handler = Handler()
    var filter: IntentFilter? = null
    var SSID: String = ""
    var listen: ((Boolean) -> Unit)? = null

    var lastWifiInfo: WifiInfo? = null
    var lastNetWorkId = -1
    var lastSSID: String = ""    //  保存上一次的SSID

    var isDeviceOnline = true // 当前设备是否在线
    /**
     * 单例
     */
    companion object {
        @Volatile
        var instance: WifiUtils? = null

        fun getInstance(context: Context): WifiUtils {
            if (instance == null) {
                synchronized(WifiUtils::class) {
                    if (instance == null) {
                        instance = WifiUtils(context)
                    }
                }
            }
            return instance!!
        }
    }

    //构造函数
    init {
        // 取得WifiManager对象
        mWifiManager = context
            .getSystemService(Context.WIFI_SERVICE) as WifiManager
        // 取得WifiInfo对象
        mWifiInfo = mWifiManager.connectionInfo
        // 得到配置好的网络连接(手机保存过的wifi)
        configuration = mWifiManager.configuredNetworks
    }

    /**
     * 1、连接指定wifi
     * 需要密码连接
    示例：WifiUtils.getInstance(this).connectwifi("Fslihua_AI", "fslihuaqaz2020"){
    if (it){
    Log.d("WY+", "连接成功")
    }else{
    Log.d("WY+", "连接失败")
    }
    }
     */

    fun connectWiFi(SSID: String, Password: String, mListen: (Boolean) -> Unit) {
        this.SSID = SSID
        listen = mListen
        //注册广播
        filter = IntentFilter()
        filter!!.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)//监听wifi改变
        filter!!.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)//监听wifi连接成功
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {//注册广播
            context.registerReceiver(this, filter, RECEIVER_NOT_EXPORTED)
        } else {
            context.registerReceiver(this, filter)
        }

        val wifiInfoOld = mWifiManager.connectionInfo
        lastWifiInfo = wifiInfoOld
        lastSSID = wifiInfoOld.ssid
        lastNetWorkId = wifiInfoOld.networkId
        println("lastWifiInfo=${lastWifiInfo}")
        println("lastSSID=${lastSSID}")
        println("lastNetWorkId=${lastNetWorkId}")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectWiFiForQ(SSID, Password, mListen)
        } else {
            //开始倒计时，如果10秒还是没有连接上传进来的wifi就返回失败
            handler.postDelayed({
                listen?.let { it(false) }//回调给调用者
                context.unregisterReceiver(this)//注销广播
                handler.removeCallbacksAndMessages(null)//注销handler
            }, 10000)//十秒返回连接失败
            connectWiFiForP(SSID, Password, mListen)
        }
    }

    // <=Android 9
    private fun connectWiFiForP(SSID: String, Password: String, mListen: (Boolean) -> Unit) {
        val wificonfi = IsExsits(SSID)//如果存在就返回配置信息
        println("wificonfi=${wificonfi}")
        if (wificonfi == null) {//为空表示没有保存过wifi
            //需要密码
            val newWifiConfig = setWifiParamsPassword(SSID, Password)
            println("newWifiConfig=${newWifiConfig}")
            println("newWifiConfig.SSID=${newWifiConfig.SSID}")
            val networkId = mWifiManager.addNetwork(newWifiConfig)
            println("networkId=${networkId}")
            mWifiManager.enableNetwork(networkId, true)
        } else {//不为空表示：保存过wifi
            println("wificonfi.networkId=${wificonfi.networkId}")
            mWifiManager.enableNetwork(wificonfi.networkId, true)
        }
    }

    // > Android 9
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectWiFiForQ(SSID: String, Password: String, mListen: (Boolean) -> Unit) {
        val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
            .setSsid(SSID)
            .setWpa2Passphrase(Password)
            .build()

        val networkRequest = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_TRUSTED)
            .setNetworkSpecifier(wifiNetworkSpecifier)
            .build()

        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        cm.requestNetwork(networkRequest, object: ConnectivityManager.NetworkCallback(){
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                // Wi-Fi 连接建立成功
                println("onAvailable.连接建立成功.network=${network.toString()}")
            }

            override fun onUnavailable() {
                super.onUnavailable()
                // Wi-Fi 连接建立失败
                println("onUnavailable.连接建立失败")
            }
        })
    }

    //判断手机是否保存有这个wifi
    private fun IsExsits(SSID: String): WifiConfiguration? {
        val existingConfigs = mWifiManager.configuredNetworks
        for (existingConfig in existingConfigs) {
            if (existingConfig.SSID == "\"" + SSID + "\"") {
                return existingConfig
            }
        }
        return null
    }

    /**
     * 连接有密码的wifi.
     *
     * @param SSID     ssid
     * @param Password Password
     * @return apConfig
     */
    private fun setWifiParamsPassword(SSID: String, Password: String): WifiConfiguration {
        val apConfig = WifiConfiguration()
        apConfig.SSID = "\"" + SSID + "\""
        apConfig.preSharedKey = "\"" + Password + "\""
        //不广播其SSID的网络
        apConfig.hiddenSSID = true
        apConfig.status = WifiConfiguration.Status.ENABLED
        //公认的IEEE 802.11验证算法。
        apConfig.allowedAuthAlgorithms.clear()
        apConfig.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
        //公认的的公共组密码
        apConfig.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
        apConfig.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
        //公认的密钥管理方案
        apConfig.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
        //密码为WPA。
        apConfig.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
        apConfig.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
        //公认的安全协议。
        apConfig.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
        return apConfig
    }

    /**
     * 广播监听wifi连接情况
     */
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == WifiManager.NETWORK_STATE_CHANGED_ACTION) {//wifi连接上与否
            val info = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
            if (info?.state == NetworkInfo.State.DISCONNECTED) {
            } else if (info?.state == NetworkInfo.State.CONNECTED) {
                val wifiManager = context?.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                logTagI("WY+", "wifiInfo: $wifiInfo")
                //获取当前wifi名称
                logTagI("WY+", "连接到网络: " + wifiInfo.ssid)

                if (wifiInfo.ssid.replace("\"", "") == SSID) {//判断连接的wifi是否为自己想要连的wifi
                    listen?.let { it(true) }//回调给调用者
                    handler.removeCallbacksAndMessages(null)//取消handler，让延迟发送不生效
                    context.unregisterReceiver(this)//注销广播
                }
            }
        } else if (intent?.action == WifiManager.WIFI_STATE_CHANGED_ACTION) {//wifi打开与否
            val wifistate = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_DISABLED)

            if (wifistate == WifiManager.WIFI_STATE_DISABLED) {
                logTagI("WY+", "系统关闭wifi ")
            } else if (wifistate == WifiManager.WIFI_STATE_ENABLED) {
                logTagI("WY+", "系统开启wifi ")
            }
        }
    }


    /**
     * 2、获取wifi列表
     */
    fun getWifiList(){
        wifiList = mWifiManager.scanResults
//        wifiList = getScanResults()
        logScanResult()//打印扫描结果
    }

    /**
    @description 获取系统扫描的wifi列表信息
     */
    private fun getScanResults(): MutableList<ScanResult> {
        //过滤空的ssid并去重
        val resultMap =
            mWifiManager.scanResults?.filter {
                !it.SSID.isNullOrBlank()
            }?.groupBy {
                it.SSID
            }
        val scanResults = mutableListOf<ScanResult>()
        if (resultMap.isNullOrEmpty()) {
            return scanResults
        }

        logTagI("WY+", "resultMap：$resultMap")

        for ((key, value) in resultMap) {
            // 取首个值
            value.firstOrNull()?.let {
                scanResults.add(it)
            }
        }
        return scanResults
    }

    //打印列表结果
    private fun logScanResult() {
        (wifiList as MutableList<ScanResult>?)?.forEach {
            var ssidName = ""
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ssidName = it.wifiSsid.toString()
                logTagI("WY+", "(api 33)ssid:${ssidName},it=$it")
            }else{
                ssidName = it.SSID  //Get the SSID
                logTagI("WY+", "ssid:${ssidName},it=$it")
            }
        }
    }

    // 断开，重连原来的wifi
    fun reConnectWifi(){
        println("reConnectWifi")
        println("lastWifiInfo=${lastWifiInfo}")
        println("lastNetWorkId=${lastNetWorkId}")
        println("lastSSID=${lastSSID}")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            reConnectWiFiForQ()
        }else {
            reConnectWiFiForP()
        }
//        reConnectWiFiForP()

    }

    // 断开设备wifi连接,重新连接原来的wifi
    // <=Android 9
    fun reConnectWiFiForP(){
        if (lastWifiInfo != null) {
            mWifiManager.enableNetwork(lastWifiInfo!!.networkId, true)
        }
        if (lastNetWorkId != -1){
            mWifiManager.enableNetwork(lastNetWorkId, true)
        }
    }

    // > Android 9
    private fun reConnectWiFiForQNew(activity: Activity) {
        val dialog = AlertDialog.Builder(context)
            .setTitle("打开 Wi-Fi 设置")
            .setMessage("在 Wi-Fi 设置中查找可用网络")
            .setPositiveButton(android.R.string.ok){ dialog, which ->
                // 点击 "确定" 按钮的操作
                val intent = Intent(Settings.Panel.ACTION_WIFI)
                activity.startActivityForResult(intent, 100)
                dialog.dismiss()
            }
            .setCancelable(false)
            .create()

        dialog.show()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun reConnectWiFiForQ(){
        val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
            .setSsid(lastSSID)
//            .setWpa2Passphrase("")
            .build()

        val networkRequest = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_NOT_RESTRICTED)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_TRUSTED)
            .setNetworkSpecifier(wifiNetworkSpecifier)
            .build()

        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        cm.requestNetwork(networkRequest, object: ConnectivityManager.NetworkCallback(){
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                // Wi-Fi 连接建立成功
                println("onAvailable.连接建立成功.network=${network.toString()}")
            }

            override fun onUnavailable() {
                super.onUnavailable()
                // Wi-Fi 连接建立失败
                println("onUnavailable.连接建立失败")
            }
        })
    }

}
