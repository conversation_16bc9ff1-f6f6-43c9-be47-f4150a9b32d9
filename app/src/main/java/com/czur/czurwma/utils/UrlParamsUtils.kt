package com.czur.czurwma.utils

object UrlParamsUtils {

    /**
     * 将url参数转换成map
     *
     * @param param aa=11&bb=22&cc=33
     * @return
     */
    fun getUrlParams(param: String): Map<String, String> {
        val map: MutableMap<String, String> = HashMap(0)
        if (param.isEmpty()) {
            return map
        }
        val params = param.split("&".toRegex()).toTypedArray()
        for (i in params.indices) {
            val p = params[i].split("=".toRegex()).toTypedArray()
            if (p.size == 2) {
                map[p[0]] = p[1]
            }
        }
        return map
    }
}