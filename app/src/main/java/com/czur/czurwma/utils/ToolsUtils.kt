package com.czur.czurwma.utils

import android.app.Application
import android.content.Context
import android.text.InputFilter
import android.text.Spanned
import android.util.Log
import android.widget.EditText
import com.blankj.utilcode.util.Utils
import com.czur.czurwma.eshare.transmitfile.DiskLruCacheManager
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.preferences.VersionPreferences
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest

// 各种工具的初始化,需要在用户同意协议之后调用
fun initializeInit(application :Application) {
    // CZURLog日志系统
    AppInitUtils.initCZURLog(application)

    VersionPreferences.init(application)
    //初始化工具类
    Utils.init(application)

    DiskLruCacheManager.init(application)
}
fun EditText.addNoSpaceFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(NoSpaceFilter())
    } else {
        val newFilters = filters.filter {
            it !is NoSpaceFilter
        }.toTypedArray()
        arrayOf(*newFilters, NoSpaceFilter())
    }
}

/**
 * 没有空格的过滤器
 */
class NoSpaceFilter : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence? {
//        Log.i("Jason", "source=${source},start=${start},end=${end},dest=${dest},dstart=${dstart},dend=${dend}")
//        return source?.filter {
//            it != ' '
//        }
        return if (source?.equals(" ") == true) {
            ""
        } else {
            null
        }
    }

}

fun EditText.addNoMinusFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(NoMinusFilter())
    } else {
        val newFilters = filters.filter {
            it !is NoMinusFilter
        }.toTypedArray()
        arrayOf(*newFilters, NoMinusFilter())
    }
}

/**
 * 减号 - 的过滤器
 */
class NoMinusFilter : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence? {
//        return source?.filter {
//            it != '-'
//        }
//        Log.i("Jason", "source=${source},start=${start},end=${end},dest=${dest},dstart=${dstart},dend=${dend}")
        return if ((source?.equals("-") == true)
            || (source?.equals("–") == true)
        ) {
            ""
        } else {
            null
        }
    }

}

fun String.isPinyinContains(string: String): Boolean {
    val stringNew = PinyinUtils.toDbc(string)
    return PinyinUtils.toDbc(this as String?).indexOf(stringNew, ignoreCase = true) != -1
}

fun EditText.addNoEmojiFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(NoEmojiFilter())
    } else {
        val newFilters = filters.filter {
            it !is NoEmojiFilter
        }.toTypedArray()
        arrayOf(*newFilters, NoEmojiFilter())
    }
}

// Emoji表情过滤器
class NoEmojiFilter : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence? {
        return source?.filter {
            Character.getType(it) != Character.SURROGATE.toInt()
                    && Character.getType(it) != Character.OTHER_SYMBOL.toInt()
        }
    }
}



fun bytesToHexString(src: ByteArray?): String? {
    val stringBuilder = StringBuilder("")
    if (src == null || src.size <= 0) {
        return null
    }
    for (i in src.indices) {
        val v = src[i].toInt() and 0xFF
        val hv = Integer.toHexString(v)
        if (hv.length < 2) {
            stringBuilder.append(0)
        }
        stringBuilder.append(hv)
    }
    return stringBuilder.toString()
}