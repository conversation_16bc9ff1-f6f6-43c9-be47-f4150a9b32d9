package com.czur.czurwma.utils.downloadapk;

import static com.blankj.utilcode.util.ActivityUtils.finishAllActivities;
import static com.blankj.utilcode.util.ActivityUtils.startActivity;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurwma.common.EshareConstants.APP_UPDATE_DONE;

import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.czurutils.log.CZURLogUtilsKt;
import com.czur.czurwma.R;
import com.czur.czurwma.common.CZURConstants;
import com.czur.czurwma.common.EshareConstants;
import com.czur.czurwma.eventbusevent.EventBusEvent;
import com.czur.czurwma.preferences.VersionPreferences;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.lang.ref.WeakReference;

public class AppDownloadManager {
    public static final String TAG = "AppDownloadManager";
    private WeakReference<Context> weakReference;
    private DownloadManager mDownloadManager;
    private DownloadChangeObserver mDownLoadChangeObserver;
    private DownloadReceiver mDownloadReceiver;
    private long mReqId;
    private OnUpdateListener mUpdateListener;
    private String downloadPath;
    private String apkName;


    // 安装时是否要清掉所有页面
    private boolean installNeedClearPage = false;

    public AppDownloadManager(Context context) {
        weakReference = new WeakReference<Context>(context);
        mDownloadManager = (DownloadManager) weakReference.get().getSystemService(Context.DOWNLOAD_SERVICE);
        mDownLoadChangeObserver = new DownloadChangeObserver(new Handler());
        mDownloadReceiver = new DownloadReceiver();

//      downloadPath = CZURConstants.SD_PATH + CZURConstants.APK_PATH;
//        downloadPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/";
        downloadPath = CZURConstants.DOWNLOAD_PATH;

    }

    public boolean isInstallNeedClearPage() {
        return installNeedClearPage;
    }

    public void setInstallNeedClearPage(boolean installNeedClearPage) {
        this.installNeedClearPage = installNeedClearPage;
    }

    public void setApkName(String apkName) {
        this.apkName = apkName;
    }

    public void setUpdateListener(OnUpdateListener mUpdateListener) {
        this.mUpdateListener = mUpdateListener;
    }

    private void deleteApk() {
        File apkfile = new File(downloadPath, apkName);
        logI("AppDownloadManager.deleteApk.apkfile=" + apkfile);
        if (apkfile.exists()) {
            apkfile.delete();
            logI("AppDownloadManager.deleteApk ok.");
        }
        logI("AppDownloadManager.deleteApk not.");
    }

    public void checkHistoryDownloadTask() {
        long lastDownID = VersionPreferences.getInstance().getDownloadId();
        if (lastDownID == -1) {
            return;
        }
        mReqId = lastDownID;
        apkName = VersionPreferences.getInstance().getApkName();

        // 查询之前的下载状态
        DownloadManager.Query query = new DownloadManager.Query();
        query.setFilterById(mReqId);
        Cursor cursor = mDownloadManager.query(query);
        Cursor c = null;
        try {
            c = mDownloadManager.query(query);
            if (c != null && c.moveToFirst()) {
                //已经下载的字节数
                //总需下载的字节数
                //状态所在的列索引
                int status = c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS));
                switch (status) {
                    case DownloadManager.STATUS_SUCCESSFUL:
                        ToastUtils.showLong(R.string.new_version_already);
                        installNewApk();
                        Context context = weakReference.get();
                        // 这里是从HomeFragment中复制来的逻辑
                        // 感觉有点奇怪, 判断是否是新版本 按照道理不应该以是否执行了安装操作判断
                        //          因为安装操作可以取消, 但是不确定是否有其他的逻辑在里面, 所以复制过来了
                        if (context != null) {
//                            FirstPreferences firstPreferences = FirstPreferences.getInstance(context.getApplicationContext());
//                            firstPreferences.setIsFirstNewVersion(false);
//                            firstPreferences.setIsUserNewVersion(false);
                        }
                        break;
                    case DownloadManager.STATUS_RUNNING:
                        ToastUtils.showShort(R.string.no_wifi_download);
                        // 就是正常下载中, 等待接收广播即可
                        // 似乎是通过这个变量来控制 关于页面不可进入....
//                        FastBleOperationUtils.setIsAPPUpdateOK(false);

                        // 通知正在下载中
//                        LiveDataBus.get().with(StarryConstants.DOWNLOAD_FORCE_RUNNING).postValue(true);
                        break;
                    default:

                }
            } else {
                // 走到这说明之前的下载任务已经被删除了,但是文件还存在
                // 清除之前的下载记录,重新走更新流程
                VersionPreferences.getInstance().saveDownloadInfo(-1, "", "");
                EventBus.getDefault().post(new EventBusEvent(EshareConstants.APP_UPDATE_RETRY));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (c != null) {
                c.close();
            }
        }

    }

    public void downloadApk(String apkUrl, String title, String desc) {
        // fix bug : 装不了新版本，在下载之前应该删除已有文件

        logI("AppDownloadManager.downloadApk.apkUrl=" + apkUrl,
                "downloadPath=" + downloadPath,
                "apkName=" + apkName);

//        deleteApk();

        try {
            //正常下载
            DownloadManager.Request request = new DownloadManager.Request(Uri.parse(apkUrl));
            //设置title
            request.setTitle(title);
            // 设置描述
            request.setDescription(desc);
            // 完成后显示通知栏
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, apkName);
            request.setMimeType("application/vnd.android.package-archive");
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE);
            mReqId = mDownloadManager.enqueue(request);
            String[] split = apkUrl.split("/");
            String realApkName = split[split.length - 1];
            // 记录下DownloadID, 之后应用启动可以根据DownloadID来进行检查是否还在下载
            VersionPreferences.getInstance().saveDownloadInfo(mReqId, apkName, realApkName);
        } catch (Exception exception) {
            //防止崩溃，再抓一下
            try {//携带下载链接跳转到浏览器
                if (!TextUtils.isEmpty(apkUrl) && apkUrl.contains("http")) {
                    Intent intent = new Intent();
                    intent.setAction("android.intent.action.VIEW");
                    Uri content_url = Uri.parse(apkUrl);
                    intent.setData(content_url);
                    startActivity(intent);
                }
            } catch (Exception e) {
                //异常处理
                logE("AppDownloadManager.downloadApk.e=" + e.toString());
            }
        }
    }

    /**
     * 取消下载
     */
    public void cancel() {
        mDownloadManager.remove(mReqId);
    }

    /**
     * 对应 {@link Activity }
     */
    public void resume() {
        //设置监听Uri.parse("content://downloads/my_downloads")
        weakReference.get().getContentResolver().registerContentObserver(Uri.parse("content://downloads/my_downloads"), true,
                mDownLoadChangeObserver);
        // 注册广播，监听APK是否下载完成
        logI("AppDownloadManager.resume().注册广播，监听APK是否下载完成");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {//注册广播
            weakReference.get().registerReceiver(mDownloadReceiver, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_NOT_EXPORTED);
        } else {
            weakReference.get().registerReceiver(mDownloadReceiver, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
        }
    }

    public void onPause() {
        weakReference.get().getContentResolver().unregisterContentObserver(mDownLoadChangeObserver);
        weakReference.get().unregisterReceiver(mDownloadReceiver);
    }

    private void updateView() {
        int[] bytesAndStatus = new int[]{0, 0, 0};
        DownloadManager.Query query = new DownloadManager.Query().setFilterById(mReqId);
        Cursor c = null;
        try {
            c = mDownloadManager.query(query);
            if (c != null && c.moveToFirst()) {
                //已经下载的字节数
                bytesAndStatus[0] = c.getInt(c.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
                //总需下载的字节数
                bytesAndStatus[1] = c.getInt(c.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));
                //状态所在的列索引
                bytesAndStatus[2] = c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS));
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }

        if (mUpdateListener != null) {
            mUpdateListener.update(bytesAndStatus[0], bytesAndStatus[1]);
        }

        CZURLogUtilsKt.logTagI(TAG, "下载进度：" + bytesAndStatus[0] + "/" + bytesAndStatus[1] + "");
        if (bytesAndStatus[0] > 0 && bytesAndStatus[1] > 0 && bytesAndStatus[0] >= bytesAndStatus[1]) {
            // 通知下载完成
            EventBus.getDefault().post(new EventBusEvent(APP_UPDATE_DONE));

//            LiveDataBus.get().with(StarryConstants.DOWNLOAD_FORCE_DONE).postValue(true);
        }
    }

    class DownloadChangeObserver extends ContentObserver {

        /**
         * Creates a content observer.
         *
         * @param handler The handler to run {@link #onChange} on, or null if none.
         */
        public DownloadChangeObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            updateView();
        }
    }

    class DownloadReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(final Context context, final Intent intent) {
            logI("DownloadReceiver.onReceive.AppUtils.installApp===" + downloadPath + "/" + apkName);
            installNewApk();
        }
    }

    /**
     * 安装下载完成的apk
     */
    private void installNewApk() {

//        AppUtils.installApp(downloadPath + "/" + apkName);
        Uri uri = mDownloadManager.getUriForDownloadedFile(mReqId);
        AppUtils.installApp(uri);

        if (installNeedClearPage) {
            finishAllActivities();
        }
    }

    //通过downLoadId查询下载的apk，解决6.0以后安装的问题
    public static File queryDownloadedApk(Context context, long downloadId) {
        File targetApkFile = null;
        DownloadManager downloader = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);

        if (downloadId != -1) {
            DownloadManager.Query query = new DownloadManager.Query();
            query.setFilterById(downloadId);
            query.setFilterByStatus(DownloadManager.STATUS_SUCCESSFUL);
            Cursor cur = downloader.query(query);
            if (cur != null) {
                if (cur.moveToFirst()) {
                    String uriString = cur.getString(cur.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI));
                    if (!TextUtils.isEmpty(uriString)) {
                        targetApkFile = new File(Uri.parse(uriString).getPath());
                    }
                }
                cur.close();
            }
        }
        return targetApkFile;
    }

    public interface OnUpdateListener {
        void update(int currentByte, int totalByte);
    }

    public interface AndroidOInstallPermissionListener {
        void permissionSuccess();

        void permissionFail();
    }
}

