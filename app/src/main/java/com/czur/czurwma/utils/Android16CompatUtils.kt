package com.czur.czurwma.utils

import android.app.AppOpsManager
import android.content.Context
import android.os.Binder
import android.os.Build
import android.os.Process
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logW
import java.lang.reflect.Modifier

/**
 * Android 16兼容性工具类
 * 处理反射限制和新的API适配
 */
object Android16CompatUtils {

    /**
     * 检查AppOps权限 - Android 16兼容版本
     * 替代原有的反射调用方式
     */
    fun checkOp(context: Context, op: Int): Boolean {
        val version = Build.VERSION.SDK_INT
        if (version >= Build.VERSION_CODES.KITKAT) {
            val manager = context.getSystemService(Context.APP_OPS_SERVICE) as? AppOpsManager
                ?: return false
            
            return try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // Android 10+ 使用公开API
                    val mode = manager.unsafeCheckOpNoThrow(op, Binder.getCallingUid(), context.packageName)
                    mode == AppOpsManager.MODE_ALLOWED
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // Android 6+ 使用noteOp
                    val mode = manager.noteOpNoThrow(op, Binder.getCallingUid(), context.packageName)
                    mode == AppOpsManager.MODE_ALLOWED
                } else {
                    // Android 19+ 使用反射（仅在低版本）
                    checkOpWithReflection(manager, op, context)
                }
            } catch (e: Exception) {
                logE("Android16CompatUtils", "checkOp failed: ${e.message}")
                false
            }
        }
        return false
    }

    /**
     * 仅在低版本Android使用反射的备用方法
     */
    private fun checkOpWithReflection(manager: AppOpsManager, op: Int, context: Context): Boolean {
        return try {
            val clazz: Class<*> = AppOpsManager::class.java
            val method = clazz.getDeclaredMethod(
                "checkOp",
                Int::class.javaPrimitiveType,
                Int::class.javaPrimitiveType,
                String::class.java
            )
            AppOpsManager.MODE_ALLOWED == method.invoke(
                manager,
                op,
                Binder.getCallingUid(),
                context.packageName
            ) as Int
        } catch (e: Exception) {
            logE("Android16CompatUtils", "checkOpWithReflection failed: ${e.message}")
            false
        }
    }

    /**
     * 获取进程名 - Android 16兼容版本
     * 避免使用反射访问ActivityThread
     */
    fun getProcessName(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9+ 使用公开API
                getProcessNameApi28()
            } else {
                // 低版本使用反射备用方案
                getProcessNameWithReflection()
            }
        } catch (e: Exception) {
            logE("Android16CompatUtils", "getProcessName failed: ${e.message}")
            ""
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun getProcessNameApi28(): String {
        return try {
            // Android 9+ 可以使用Application.getProcessName()
            android.app.Application.getProcessName() ?: ""
        } catch (e: Exception) {
            logW("Android16CompatUtils", "getProcessNameApi28 failed, fallback to reflection")
            getProcessNameWithReflection()
        }
    }

    /**
     * 反射获取进程名的备用方法（仅用于低版本）
     */
    private fun getProcessNameWithReflection(): String {
        return try {
            val currentProcessNameMethod = Class.forName("android.app.ActivityThread")
                .getMethod("currentProcessName")
            currentProcessNameMethod.invoke(null)?.toString() ?: ""
        } catch (e: Exception) {
            logE("Android16CompatUtils", "getProcessNameWithReflection failed: ${e.message}")
            ""
        }
    }

    /**
     * 获取所有AppOps字段 - Android 16兼容版本
     * 减少反射使用，提供更安全的实现
     */
    fun getAllOPSField(context: Context): Map<String, Int> {
        val result = mutableMapOf<String, Int>()
        
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用已知的常量，避免反射
                getKnownOpsConstants()
            } else {
                // 低版本使用反射获取
                getOpsFieldWithReflection(context)
            }
        } catch (e: Exception) {
            logE("Android16CompatUtils", "getAllOPSField failed: ${e.message}")
            emptyMap()
        }
    }

    /**
     * 获取已知的AppOps常量（避免反射）
     */
    private fun getKnownOpsConstants(): Map<String, Int> {
        return mapOf(
            "OP_COARSE_LOCATION" to AppOpsManager.OPSTR_COARSE_LOCATION.hashCode(),
            "OP_FINE_LOCATION" to AppOpsManager.OPSTR_FINE_LOCATION.hashCode(),
            "OP_CAMERA" to AppOpsManager.OPSTR_CAMERA.hashCode(),
            "OP_RECORD_AUDIO" to AppOpsManager.OPSTR_RECORD_AUDIO.hashCode(),
            "OP_READ_PHONE_STATE" to AppOpsManager.OPSTR_READ_PHONE_STATE.hashCode(),
            "OP_WRITE_EXTERNAL_STORAGE" to AppOpsManager.OPSTR_WRITE_EXTERNAL_STORAGE.hashCode(),
            "OP_READ_EXTERNAL_STORAGE" to AppOpsManager.OPSTR_READ_EXTERNAL_STORAGE.hashCode()
            // 可以根据需要添加更多常量
        )
    }

    /**
     * 反射获取AppOps字段的备用方法（仅用于低版本）
     */
    private fun getOpsFieldWithReflection(context: Context): Map<String, Int> {
        val result = mutableMapOf<String, Int>()
        
        try {
            val manager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val fields = manager.javaClass.declaredFields

            for (field in fields) {
                if (field.type == Int::class.java && Modifier.isStatic(field.modifiers)) {
                    field.isAccessible = true
                    val value = field.get(null) as? Int
                    if (value != null) {
                        result[field.name] = value
                    }
                }
            }
        } catch (e: Exception) {
            logE("Android16CompatUtils", "getOpsFieldWithReflection failed: ${e.message}")
        }
        
        return result
    }

    /**
     * 检查是否运行在Android 16+
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }

    /**
     * 检查是否需要使用兼容模式
     */
    fun needsCompatMode(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 获取Android版本信息
     */
    fun getVersionInfo(): String {
        return "Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})"
    }
}
