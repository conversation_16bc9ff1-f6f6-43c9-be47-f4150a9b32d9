package com.czur.czurwma.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logW

/**
 * Intent安全适配工具类
 * Android 16对Intent使用增加了更多限制
 */
object IntentSecurityUtils {

    /**
     * 安全地启动Activity
     */
    fun startActivitySafely(context: Context, intent: Intent): Bo<PERSON>an {
        return try {
            if (Build.VERSION.SDK_INT >= 36) { // Android 16
                startActivityForAndroid16(context, intent)
            } else {
                startActivityLegacy(context, intent)
            }
        } catch (e: Exception) {
            logE("IntentSecurityUtils", "Failed to start activity: ${e.message}")
            false
        }
    }

    /**
     * Android 16的安全Intent启动
     */
    @RequiresApi(36)
    private fun startActivityForAndroid16(context: Context, intent: Intent): Boolean {
        // Android 16要求显式Intent必须与目标组件的Intent Filter相匹配
        // 并且所有Intent都必须指定action
        
        if (intent.component != null) {
            // 显式Intent需要验证
            if (!validateExplicitIntent(context, intent)) {
                logW("IntentSecurityUtils", "Explicit intent validation failed")
                return false
            }
        }
        
        if (intent.action == null) {
            // Android 16要求所有Intent都必须指定action
            logW("IntentSecurityUtils", "Intent action is required in Android 16")
            return false
        }
        
        context.startActivity(intent)
        logI("IntentSecurityUtils", "Activity started successfully with Android 16 security checks")
        return true
    }

    /**
     * 低版本的Intent启动
     */
    private fun startActivityLegacy(context: Context, intent: Intent): Boolean {
        context.startActivity(intent)
        logI("IntentSecurityUtils", "Activity started with legacy method")
        return true
    }

    /**
     * 验证显式Intent
     */
    private fun validateExplicitIntent(context: Context, intent: Intent): Boolean {
        val component = intent.component ?: return false
        
        try {
            val packageManager = context.packageManager
            val activityInfo = packageManager.getActivityInfo(component, PackageManager.GET_META_DATA)
            
            // 检查Activity是否存在且可访问
            if (!activityInfo.exported && component.packageName != context.packageName) {
                logW("IntentSecurityUtils", "Target activity is not exported")
                return false
            }
            
            return true
        } catch (e: PackageManager.NameNotFoundException) {
            logE("IntentSecurityUtils", "Target activity not found: ${e.message}")
            return false
        }
    }

    /**
     * 创建安全的显式Intent
     */
    fun createSafeExplicitIntent(
        context: Context,
        targetClass: Class<*>,
        action: String = Intent.ACTION_DEFAULT
    ): Intent {
        val intent = Intent(context, targetClass)
        
        if (Build.VERSION.SDK_INT >= 36) { // Android 16
            // 确保设置action
            intent.action = action
            
            // 添加默认category
            intent.addCategory(Intent.CATEGORY_DEFAULT)
        }
        
        return intent
    }

    /**
     * 创建安全的隐式Intent
     */
    fun createSafeImplicitIntent(action: String): Intent {
        val intent = Intent(action)
        
        if (Build.VERSION.SDK_INT >= 36) { // Android 16
            // 添加默认category
            intent.addCategory(Intent.CATEGORY_DEFAULT)
        }
        
        return intent
    }

    /**
     * 安全地启动外部应用的Activity
     */
    fun startExternalActivitySafely(
        context: Context,
        packageName: String,
        className: String,
        action: String = Intent.ACTION_DEFAULT
    ): Boolean {
        return try {
            val intent = Intent()
            intent.component = ComponentName(packageName, className)
            intent.action = action
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            
            // 检查Intent是否可以被解析
            if (!canResolveIntent(context, intent)) {
                logW("IntentSecurityUtils", "Cannot resolve external intent")
                return false
            }
            
            startActivitySafely(context, intent)
        } catch (e: Exception) {
            logE("IntentSecurityUtils", "Failed to start external activity: ${e.message}")
            false
        }
    }

    /**
     * 检查Intent是否可以被解析
     */
    fun canResolveIntent(context: Context, intent: Intent): Boolean {
        return try {
            val packageManager = context.packageManager
            val resolveInfos = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.queryIntentActivities(intent, PackageManager.ResolveInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                packageManager.queryIntentActivities(intent, 0)
            }
            resolveInfos.isNotEmpty()
        } catch (e: Exception) {
            logE("IntentSecurityUtils", "Failed to resolve intent: ${e.message}")
            false
        }
    }

    /**
     * 创建分享Intent
     */
    fun createShareIntent(text: String? = null, uri: android.net.Uri? = null): Intent {
        val intent = Intent(Intent.ACTION_SEND)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        
        when {
            text != null -> {
                intent.type = "text/plain"
                intent.putExtra(Intent.EXTRA_TEXT, text)
            }
            uri != null -> {
                intent.type = "image/*" // 根据实际类型调整
                intent.putExtra(Intent.EXTRA_STREAM, uri)
            }
        }
        
        return intent
    }

    /**
     * 创建打开文件的Intent
     */
    fun createOpenFileIntent(uri: android.net.Uri, mimeType: String): Intent {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.setDataAndType(uri, mimeType)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        
        return intent
    }

    /**
     * 创建拨打电话的Intent
     */
    fun createCallIntent(phoneNumber: String): Intent {
        val intent = Intent(Intent.ACTION_CALL)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.data = android.net.Uri.parse("tel:$phoneNumber")
        
        return intent
    }

    /**
     * 创建发送短信的Intent
     */
    fun createSmsIntent(phoneNumber: String, message: String? = null): Intent {
        val intent = Intent(Intent.ACTION_SENDTO)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.data = android.net.Uri.parse("smsto:$phoneNumber")
        
        if (message != null) {
            intent.putExtra("sms_body", message)
        }
        
        return intent
    }

    /**
     * 创建发送邮件的Intent
     */
    fun createEmailIntent(
        to: Array<String>? = null,
        subject: String? = null,
        body: String? = null
    ): Intent {
        val intent = Intent(Intent.ACTION_SENDTO)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.data = android.net.Uri.parse("mailto:")
        
        to?.let { intent.putExtra(Intent.EXTRA_EMAIL, it) }
        subject?.let { intent.putExtra(Intent.EXTRA_SUBJECT, it) }
        body?.let { intent.putExtra(Intent.EXTRA_TEXT, it) }
        
        return intent
    }

    /**
     * 创建打开浏览器的Intent
     */
    fun createBrowserIntent(url: String): Intent {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.addCategory(Intent.CATEGORY_BROWSABLE)
        intent.data = android.net.Uri.parse(url)
        
        return intent
    }

    /**
     * 检查是否为Android 16+
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 BAKLAVA
    }

    /**
     * 获取Intent安全信息
     */
    fun getIntentSecurityInfo(): String {
        return if (isAndroid16OrHigher()) {
            "Android 16+ - Enhanced Intent security enabled"
        } else {
            "Legacy Intent handling"
        }
    }

    /**
     * 验证Intent的安全性
     */
    fun validateIntentSecurity(intent: Intent): ValidationResult {
        val issues = mutableListOf<String>()
        
        if (Build.VERSION.SDK_INT >= 36) { // Android 16
            if (intent.action == null) {
                issues.add("Action is required in Android 16")
            }
            
            if (intent.component != null && intent.categories.isEmpty()) {
                issues.add("Explicit intents should have categories")
            }
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues
        )
    }

    /**
     * Intent验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val issues: List<String>
    )
}
