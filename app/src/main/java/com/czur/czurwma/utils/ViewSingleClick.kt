package com.czur.czurwma.utils

import android.view.View
import android.widget.Checkable
import com.czur.czurutils.log.logTagD



// 单个view的防止快速点击的事件
inline fun <T : View> T.singleViewClick(time: Long = 800, crossinline block: (T) -> Unit) {
    setOnClickListener {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - viewLastClickTime > time || this is Checkable) {
            viewLastClickTime = currentTimeMillis
            block(this)
        }
    }
}

// 所有view都不能连续点击的事件
inline fun <T : View> T.singleClick(time: Long = 350, crossinline block: (T) -> Unit) {
    setOnClickListener {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastClickTime > time || this is Checkable) {
            lastClickTime = currentTimeMillis
            block(this)
        }
    }
}

//兼容点击事件设置为this的情况
fun <T : View> T.singleClick(onClickListener: View.OnClickListener, time: Long = 800) {
    setOnClickListener {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - lastClickTime > time || this is Checkable) {
            lastClickTime = currentTimeMillis
            onClickListener.onClick(this)
        }
    }
}

var lastClickTime = 0L
var <T : View> T.viewLastClickTime: Long
    set(value) = setTag(1766613352, value)
    get() = getTag(1766613352) as? Long ?: 0
