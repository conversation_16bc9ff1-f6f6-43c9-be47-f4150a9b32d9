package com.czur.czurwma.utils

import android.net.ConnectivityManager
import android.net.Network
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.Utils
import com.czur.czurutils.log.logI

object NetConnectListenerUtil {
    var listenerList = mutableListOf<NetStateListener>()
    var manager: ConnectivityManager? = null
    var callback: ConnectivityManager.NetworkCallback? = null

    fun addNetListener(netStateListener: NetStateListener) {
        listenerList.add(netStateListener)
    }

    fun removeNetListener(netStateListener: NetStateListener) {
        listenerList.remove(netStateListener)
    }

    fun init() {
        // api大于27
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O_MR1) {
            manager = Utils.getApp().getSystemService(
                ConnectivityManager::class.java
            )
            callback =
                object : ConnectivityManager.NetworkCallback() {
                    override fun onAvailable(network: Network) {
                        logI( "网络环境监听-onAvailable")
                        tellAllListenerNetChanged(true)
                        //网络连接
                    }

                    override fun onLost(network: Network) {
                        logI( "网络环境监听-onLost")
                        tellAllListenerNetChanged(false)
                        //网络断开
                    }
                }

            manager?.registerDefaultNetworkCallback(callback as ConnectivityManager.NetworkCallback)

        } else {

            NetworkUtils.registerNetworkStatusChangedListener(object :
                NetworkUtils.OnNetworkStatusChangedListener {
                override fun onDisconnected() {
                    tellAllListenerNetChanged(false)
                    logI( "网络环境监听-onDisconnected")
                }

                override fun onConnected(networkType: NetworkUtils.NetworkType?) {
                    tellAllListenerNetChanged(true)
                    logI( "网络环境监听-onConnected")
                }

            })

        }


    }

    fun unregistNetListener(netStateListener: NetStateListener) {
        listenerList.remove(netStateListener)
        if (listenerList.isEmpty()){
            callback?.let { manager?.unregisterNetworkCallback(it) };
        }
    }

    fun tellAllListenerNetChanged(isConnected : Boolean) {
        listenerList.forEach {
            it.onNetConnectedChange(isConnected)
        }
    }

    interface NetStateListener {
        fun onNetConnectedChange(isConnect : Boolean)
    }
}