package com.czur.czurwma.utils.downloadapk

import android.content.DialogInterface
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.Utils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurwma.BuildConfig
import com.czur.czurwma.R
import com.czur.czurwma.common.EshareConstants.APP_UPDATE_HAS_UPDATE_INFO
import com.czur.czurwma.common.EshareConstants.APP_UPDATE_NO_UPDATE_INFO
import com.czur.czurwma.common.NetUrls
import com.czur.czurwma.myentity.CheckSeriesModel
import com.czur.czurwma.myentity.CheckUpdateInfoData
import com.czur.czurwma.myentity.CheckUpdateInfoModel
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.widget.CloudCommonDialog
import com.czur.czurwma.widget.CloudCommonPopupConstants
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.HttpUrl
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import org.greenrobot.eventbus.EventBus
import java.util.Objects

object UpdateUtil {
    private val TAG = "UpdateUtil"
    private var updateInfoDialog : CloudCommonDialog? = null
    suspend fun checkUpdateAndGetInfo(): String? {
        if (BuildConfig.IS_OVERSEAS){
            return ""
        }
        if (!NetworkUtils.isConnected()) {
            return ""
        }
        return withContext(Dispatchers.IO) {
            // 1 检查id
            val checkUpdateSeries = checkUpdateSeries()
            if (checkUpdateSeries.isEmpty()) {
                EventBus.getDefault().postSticky(EventBusEvent(APP_UPDATE_NO_UPDATE_INFO))
                ""
            } else {
                // 2 检查版本
                checkUpdateVersion(checkUpdateSeries)
            }
        }
    }

    /**
     * directUpdate 如果是自动重试,不需要弹窗
     */
    suspend fun checkUpdate(directUpdate: Boolean = false) {
        if (BuildConfig.IS_OVERSEAS){
            logTagI(TAG,"海外版本不检测在线更新")
            return
        }
        if (!NetworkUtils.isConnected()) {
            logTagI(TAG,"网络未连接,不检测在线更新")
            return
        }

        withContext(Dispatchers.IO) {

            val checkUpdateVersionStr = checkUpdateAndGetInfo()
            if (checkUpdateVersionStr?.isNotEmpty() == true) {
                try {
                    val checkUpdateInfoModel =
                        Gson().fromJson(checkUpdateVersionStr, CheckUpdateInfoModel::class.java)

                    val body = checkUpdateInfoModel.data
                    // 3 处理更新
                    if (body == null) {//没有需要更新的数据(有可能没有选择"最终版"这个选项)
                        logTagI(TAG,"升级body为空,没有更新信息")
                        EventBus.getDefault().postSticky(EventBusEvent(APP_UPDATE_NO_UPDATE_INFO))
                    } else {
                        logTagI(TAG,"body${body.toString()} body.buildNumber ${body.buildNumber} appVersionName${AppUtils.getAppVersionName()}")
                        val result = compareVersions(body.buildNumber, AppUtils.getAppVersionName())
                        if (result > 0) {//如果是升级版本
                            logTagI(TAG,"升级版本 ${result}")

                            EventBus.getDefault()
                                .postSticky(EventBusEvent(APP_UPDATE_HAS_UPDATE_INFO))
                            withContext(Dispatchers.Main) {
                                if (directUpdate) {
                                    startUpdate(body)
                                } else {
                                    showUpdateDialog(body)
                                }
                            }
                        } else {
                            logTagI(TAG,"不升级版本 ${result}")
                            EventBus.getDefault().postSticky(EventBusEvent(APP_UPDATE_NO_UPDATE_INFO))
                            return@withContext
                        }

                    }

                } catch (e: Exception) {
                    logD("e.printStackTrace() = ${e.printStackTrace()}")

                    EventBus.getDefault().postSticky(EventBusEvent(APP_UPDATE_NO_UPDATE_INFO))
                }
            } else {
                EventBus.getDefault().postSticky(EventBusEvent(APP_UPDATE_NO_UPDATE_INFO))
            }
        }
    }


    private fun showUpdateDialog(body: CheckUpdateInfoData) {
        if (updateInfoDialog != null && updateInfoDialog?.isShowing == true){
            logTagI(TAG,"正在展示更新dialog,不获取新状态")
            return
        }

        val force = body.isForce
        val msg = if (force) {
            if (BuildConfig.DEBUG) {
                Utils.getApp().getString(R.string.update_apk_force_msg) + "(测试)"
            } else {
                Utils.getApp().getString(R.string.update_apk_force_msg)
            }
        } else {
            if (BuildConfig.DEBUG) {
                Utils.getApp().getString(R.string.update_apk_msg) + "(测试)"
            } else {
                Utils.getApp().getString(R.string.update_apk_msg)
            }

        }
        val nBtn = if (force) {
            CloudCommonPopupConstants.COMMON_TWO_QUIT_BUTTON1
        } else {
            CloudCommonPopupConstants.COMMON_TWO_QUIT_BUTTON2
        }

        updateInfoDialog = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(),
            nBtn
        )
            .setTitle(Utils.getApp().getString(R.string.starry_popupwindow_title))
            .setMessage(msg)
            .setOnPositiveListener { dialog: DialogInterface, _: Int ->
                startUpdate(body)
                if (!force) {
                    updateInfoDialog?.dismiss()
                }
            }
            .setOnNegativeListener { dialog: DialogInterface, _: Int ->
                updateInfoDialog?.dismiss()
                if (force) {//退出应用
                    ActivityUtils.finishAllActivities()
                }
            }
            .create();
            updateInfoDialog?.show()
    }

    private fun startUpdate(data: CheckUpdateInfoData) {
//        if (jumpToStore()) { // 1.0.0暂时不跳转商店,等待商城上线,第二版再开放
//            // 跳转商城成功
//        }else{
        // 跳转商城失败,下载更新
        DownApkUtil.downloadApk(data)
//        }
    }

    private fun jumpToStore(): Boolean {
        return MarketUtils.startMarket(ActivityUtils.getTopActivity(), "com.czur.cloud")
    }


    private fun checkUpdateSeries(): String {
        val urlBuilder: HttpUrl.Builder = NetUrls.CHECK_SERIES.toHttpUrlOrNull()!!.newBuilder()
        urlBuilder.addQueryParameter("name", "成者投屏")//此处成者投屏或者成者妙传(孟哥已加入成者妙传的单独处理)
        val url: String = urlBuilder.build().toString()
        try {
            val checkRequest: Request =
                Request.Builder().url(url).get().build()
            val checkCall = OkHttpClient().newCall(checkRequest)
            checkCall.execute().use {
                if (it.isSuccessful) {
                    val responseStr = Objects.requireNonNull(it.body)?.string()
                    try {
                        val checkSeriesModel =
                            Gson().fromJson(responseStr, CheckSeriesModel::class.java)
//
                        val body = checkSeriesModel.data
                        body.first {
                            if (BuildConfig.IS_OVERSEAS) {
                                it.platform == "Android" && it.supportArea == 2 || it.supportArea == 0
                            } else {
                                it.platform == "Android" && it.supportArea == 1 || it.supportArea == 0
                            }
                        }.let {
//                        checkUpdateVersion(it.seriesId)
                            return it.seriesId
                        }

                    } catch (e: Exception) {

                    }
                } else {

                }
            }

        }catch (e :Exception){
            logTagE(TAG,e.toString())
            return ""
        }

        return ""
    }

    private fun checkUpdateVersion(seriesId: String): String? {
        val urlBuilder: HttpUrl.Builder = NetUrls.CHECK_UPDATE.toHttpUrlOrNull()!!.newBuilder()
        urlBuilder.addQueryParameter("seriesId", seriesId)
        urlBuilder.addQueryParameter("version", AppUtils.getAppVersionName())
        val url: String = urlBuilder.build().toString()
        val checkRequest: Request =
            Request.Builder().url(url).get().build()
        val checkCall = OkHttpClient().newCall(checkRequest)

        checkCall.execute().use {
            if (it.isSuccessful) {
                val responseStr = Objects.requireNonNull(it.body)?.string()
                logI("checkForceUpdate.onResponse: ${responseStr}")

                return responseStr
            } else {
                return ""
            }
        }

    }

    fun compareVersions(version1: String, version2: String): Int {
        val vals1 = version1.split("\\.".toRegex()).map { it.toInt() }
        val vals2 = version2.split("\\.".toRegex()).map { it.toInt() }
        val size = Math.max(vals1.size, vals2.size)
        for (i in 0 until size) {
            val v1 = if (i < vals1.size) vals1[i] else 0
            val v2 = if (i < vals2.size) vals2[i] else 0
            if (v1 != v2) {
                return v1 - v2
            }
        }
        return 0
    }
}