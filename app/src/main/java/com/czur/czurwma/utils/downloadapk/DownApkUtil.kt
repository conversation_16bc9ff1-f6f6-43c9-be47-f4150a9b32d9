package com.czur.czurwma.utils.downloadapk

import android.content.DialogInterface
import android.content.Intent
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ServiceUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.czur.czurutils.log.logI
import com.czur.czurwma.R
import com.czur.czurwma.common.CZURConstants.DOWNLOAD_PATH
import com.czur.czurwma.myentity.CheckUpdateInfoData
import com.czur.czurwma.preferences.VersionPreferences
import com.czur.czurwma.widget.CloudCommonDialog
import com.czur.czurwma.widget.CloudCommonPopupConstants
import java.io.File

object DownApkUtil {
    private var commonPopup: CloudCommonDialog? = null
    val apkPath = DOWNLOAD_PATH
    var version = ""
    var apkName = ""
    fun downloadApk(dataBody: CheckUpdateInfoData) {
        version = dataBody.buildNumber
        var randomVersion = (System.currentTimeMillis() / 1000).toString()
        randomVersion = randomVersion.substring(randomVersion.length - 6, randomVersion.length)
        apkName = Utils.getApp()
            .getString(R.string.app_name) + "_" + version + "." + randomVersion + ".apk"
        logI("HomeFragment.checkNewVersion.apkPath=$apkPath;apkName=$apkName")
        var apkFile: File = File(apkPath, apkName)
        var realApkFile: File = File(apkPath, apkName)
        if (VersionPreferences.getInstance().downloadId != -1L) { //如果存有下载记录并且下载记录是当前版本的,就去验证是否可以使用
            if (VersionPreferences.getInstance().apkName.contains(version)) {
                apkFile = File(apkPath, VersionPreferences.getInstance().apkName)
                if (VersionPreferences.getInstance().realApkName.isNotEmpty()){
                    realApkFile = File(apkPath, VersionPreferences.getInstance().realApkName)
                }
            }
        }

        //apk是否存在，存在就进行安装
        val flag = (apkFile.exists() || realApkFile.exists()) && VersionPreferences.getInstance().downloadId != -1L
        logI("HomeFragment.checkNewVersion.apkFile=$apkFile;apkFile.exists()=$flag")

        if (flag) {
            // apk 文件存在, 证明开启过下载任务
            // 启动Service 去同步一下下载任务, 看看是否完成
            logI(apkPath + " apk is exist!")
            DownloadApkService.checkHistoryDownloadTask(ActivityUtils.getTopActivity(), false)
        } else {
//            FastBleOperationUtils.setIsAPPUpdateOK(false);
            if (NetworkUtils.isWifiConnected()) {
                startDownloadService(true, dataBody)
            } else {
                downloadInNotWifi(dataBody)
            }
        }

    }

    private fun downloadInNotWifi(dataBody: CheckUpdateInfoData) {

        val builder: CloudCommonDialog.Builder =
            CloudCommonDialog.Builder(
                ActivityUtils.getTopActivity(),
                CloudCommonPopupConstants.COMMON_TWO_BUTTON
            )
        builder.setTitle(Utils.getApp().getResources().getString(R.string.starry_popupwindow_title))
        builder.setMessage(Utils.getApp().getResources().getString(R.string.download_app_prompt))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            commonPopup?.dismiss()
            startDownloadService(false, dataBody)
        })
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        commonPopup = builder.create()
        commonPopup?.show()
    }

    private fun startDownloadService(isWifi: Boolean, dataBody: CheckUpdateInfoData) {
        val flag = ServiceUtils.isServiceRunning(DownloadApkService::class.java)
        if (!flag) {
            if (isWifi) {
                ToastUtils.showShort(R.string.wifi_download)
            } else {
                ToastUtils.showShort(R.string.no_wifi_download)
            }
        } else {
            ServiceUtils.stopService(DownloadApkService::class.java)
            ToastUtils.showShort(R.string.no_wifi_download)
        }
        logI("DOWNLOAD startDownloadService")
        val intent = Intent(ActivityUtils.getTopActivity(), DownloadApkService::class.java)
        intent.putExtra("updateUrl", dataBody.downloadUrl)
        intent.putExtra("notes", dataBody.releaseDate)
        intent.putExtra("apkName", apkName)
        ActivityUtils.getTopActivity().startService(intent)
    }

}