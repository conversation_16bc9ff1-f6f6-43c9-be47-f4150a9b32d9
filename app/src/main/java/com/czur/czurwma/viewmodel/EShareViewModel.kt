package com.czur.czurwma.viewmodel

import android.app.Application
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import androidx.lifecycle.AndroidViewModel
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.eshare.EShareActivity
import com.czur.czurwma.eshare.transmitfile.TransmitFileRootBrowserActivity
import com.czur.czurwma.myenum.TransmitFileResultEnum.*
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.utils.launch
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.TransmitFileHttpClient
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

class EShareViewModel(application: Application) : AndroidViewModel(application) {
    private val TAG = "EShareViewModel"

    enum class TransmitType {
        UPLOAD_TYPE,
        DOWNLOAD_TYPE
    }

    private val applicationViewModel by lazy {
        (application as CzurWMAApplication).getEshareViewModel1()
    }

    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(application).device()
    }

    override fun onCleared() {
        super.onCleared()
    }

    var downloadSupport = false
    var uploadSupport = false
    var downloadLowVersion = false


    fun getIntentSwitch() {
        runBlocking {
            launch(Dispatchers.IO) {
                downloadLowVersion = false
                // 判断上传文件功能是否可用
                if (mDeviceManager.extraInfo != null && mDeviceManager.extraInfo.isNotEmpty() && mDeviceManager.extraInfo != "null") {
                    uploadSupport = true
                } else {
                    uploadSupport = false
                }
                logTagD(TAG, "viewModel.uploadSupport-${uploadSupport}")

                if (applicationViewModel.currentIP.isNotEmpty()) {
                    // 使用async和await确保按顺序执行
                    val serverCheckDeferred = async {
                        TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
                    }
                    serverCheckDeferred.await() // 等待服务器检查完成

                    val functionsDeferred = async {
                        TransmitFileHttpClient.getFunctions(applicationViewModel.currentIP)
                    }
                    val functions = functionsDeferred.await() // 等待获取功能列表

                    // 判断是否有下载文件的功能
                    val downloadSwitch = when {
                        functions?.isEmpty() == true -> {
                            downloadLowVersion = false
                            false
                        }

                        functions == "timeOut" -> {
                            downloadLowVersion = true
                            false
                        }

                        else -> {
                            downloadLowVersion = false
                            val json = JSONObject(functions)
                            val downloadFlag = json.getString("download")
                            downloadFlag == "true"
                        }
                    }

                    downloadSupport = downloadSwitch
                    logTagD(TAG, "viewModel.downloadSupport-${downloadSupport}")
                }
            }.join() // 等待launch块内的代码执行完毕
        }
    }

    // 获取上传文件和下载文件功能的开关状态
//    fun getIntentSwitch() {
//        launch(Dispatchers.IO) {
//            downloadLowVersion = false
//            // 判断上传文件功能是否可用
//            if (mDeviceManager.extraInfo != null && mDeviceManager.extraInfo.isNotEmpty() && mDeviceManager.extraInfo != "null") {
//                uploadSupport = true
//            } else {
//                uploadSupport = false
//            }
//            logTagD(TAG, "viewModel.uploadSupport-${uploadSupport}")
//
//            if (applicationViewModel.currentIP.isNotEmpty()) {
//                // 使用async和await确保按顺序执行
//                val serverCheckDeferred = async {
//                    TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
//                }
//                serverCheckDeferred.await() // 等待服务器检查完成
//
//                val functionsDeferred = async {
//                    TransmitFileHttpClient.getFunctions(applicationViewModel.currentIP)
//                }
//                val functions = functionsDeferred.await() // 等待获取功能列表
//
//                // 判断是否有下载文件的功能
//                val downloadSwitch = when {
//                    functions?.isEmpty() == true -> {
//                        downloadLowVersion = false
//                        false
//                    }
//                    functions == "timeOut" -> {
//                        downloadLowVersion = true
//                        false
//                    }
//                    else -> {
//                        downloadLowVersion = false
//                        val json = JSONObject(functions)
//                        val downloadFlag = json.getString("download")
//                        downloadFlag == "true"
//                    }
//                }
//
//                downloadSupport = downloadSwitch
//                logTagD(TAG, "viewModel.downloadSupport-${downloadSupport}")
//            }
//        }
//    }

    /**
     * type :fun getIntentSwitch() {
    runBlocking {
    launch(Dispatchers.IO) {
    downloadLowVersion = false
    // 判断上传文件功能是否可用
    if (mDeviceManager.extraInfo != null && mDeviceManager.extraInfo.isNotEmpty() && mDeviceManager.extraInfo != "null") {
    uploadSupport = true
    } else {
    uploadSupport = false
    }
    logTagD(TAG, "viewModel.uploadSupport-${uploadSupport}")

    if (applicationViewModel.currentIP.isNotEmpty()) {
    // 使用async和await确保按顺序执行
    val serverCheckDeferred = async {
    TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
    }
    serverCheckDeferred.await() // 等待服务器检查完成

    val functionsDeferred = async {
    TransmitFileHttpClient.getFunctions(applicationViewModel.currentIP)
    }
    val functions = functionsDeferred.await() // 等待获取功能列表

    // 判断是否有下载文件的功能
    val downloadSwitch = when {
    functions?.isEmpty() == true -> {
    downloadLowVersion = false
    false
    }
    functions == "timeOut" -> {
    downloadLowVersion = true
    false
    }
    else -> {
    downloadLowVersion = false
    val json = JSONObject(functions)
    val downloadFlag = json.getString("download")
    downloadFlag == "true"
    }
    }

    downloadSupport = downloadSwitch
    logTagD(TAG, "viewModel.downloadSupport-${downloadSupport}")
    }
    }.join() // 等待launch块内的代码执行完毕
    }
    }
     */
    fun wantToTransmitFile(
        eShareActivity: EShareActivity,
        selectFileResultLauncher: ActivityResultLauncher<Intent>,
        ipAddress: String,
        type: TransmitType
    ) {

        launch {
            withContext(Dispatchers.IO) {
                getIntentSwitch()
            }


            if (downloadLowVersion && type == TransmitType.DOWNLOAD_TYPE) {
                TransmitFileDialogManager.showLowStarryHubDialog(eShareActivity)
                return@launch
            }

//            if (!downloadSupport && type == TransmitType.DOWNLOAD_TYPE) {
//                TransmitFileDialogManager.showErrorDevicesServerDialog()
////                TransmitFileDialogManager.showLowStarryHubDialog(eShareActivity)
//                return@launch
//            }

            if (!uploadSupport && type == TransmitType.UPLOAD_TYPE) {
                TransmitFileDialogManager.showLowStarryHubDialog(eShareActivity)
                return@launch
            }


            eShareActivity.showProgressDialog()

//            if (!NetworkUtils.isWifiConnected()) {
//                eShareActivity.hideProgressDialog(true)
//                return@launch
//            }

            val checkResult = withContext(Dispatchers.IO) {
                val code =
                    TransmitFileHttpClient.checkServer(ipAddress)
                code
            }



            if (!checkResult) {
                ToastUtils.showLong(R.string.network_error)
                eShareActivity.hideProgressDialog(true)
                return@launch
            }

            val verifyCodeResult = withContext(Dispatchers.IO) {
                val code =
                    TransmitFileHttpClient.getVerifyCode(ipAddress)
                code
            }

            val verifyContent = verifyCodeResult.content
            val transmitFileCode = ESharePreferences.getInstance().transmitFileCode
            val enum = TransmitFileResultEnum.find(verifyCodeResult.code)
            when (enum) {
                CODE_200,
                CODE_202 -> {// 有验证码
                    if (verifyContent != "null" // 有时候会返回null,然后tostring了
                        && transmitFileCode != verifyContent// 传输码如果和记住的一样,直接通过)
                    ) {
                        TransmitFileDialogManager.showInputVerifyCodeDialog(
                            eShareActivity,
                            verifyContent ?: "",
                            selectFileResultLauncher,
                            type
                        )
                    } else {
                        if (type == TransmitType.DOWNLOAD_TYPE) {// 跳转到download页面
                            val intent =
                                Intent(eShareActivity, TransmitFileRootBrowserActivity::class.java)
                            eShareActivity.startActivity(intent)
                        } else {
                            CzurFileUtils.openDocumentToPick(
                                selectFileResultLauncher,
                                eShareActivity
                            )
                        }
                    }
                }

                CODE_9529,//文件不存在(上传中,删掉源文件)
                CODE_205,//md5校验失败
                CODE_300,   //起始字节错误
                CODE_400 -> {// 未知错误
                    CzurFileUtils.openDocumentToPick(selectFileResultLauncher)
                }

                CODE_210 -> {//空间不足
                    TransmitFileHttpClient.handleResultMessage(
                        enum.code
                    )
                }


                CODE_230 -> {
                    TransmitFileDialogManager.showInMeetingDialog(false)
                }

                CODE_220 -> {//上传开关关闭
                    // 处理收到的code是否允许继续走
                    TransmitFileDialogManager.showErrorDevicesServerDialog()
                }

                CODE_9527 -> {
                    // 网络连接失败
                    ToastUtils.showLong(R.string.network_error)
                }

                CODE_9528 -> {
                    // 有返回,但是返回失败, 不做处理 直接进入
                    CzurFileUtils.openDocumentToPick(selectFileResultLauncher)
                }

                CODE_404 -> {

                }

                CODE_405 -> {

                }

                CODE_406 -> {

                }
            }
            eShareActivity.hideProgressDialog(true)
        }

    }

}