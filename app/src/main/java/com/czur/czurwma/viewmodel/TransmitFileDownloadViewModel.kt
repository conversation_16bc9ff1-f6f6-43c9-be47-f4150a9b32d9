package com.czur.czurwma.viewmodel

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.BaseActivity
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.myentity.FileBrowserEntity
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.eshare.engine.Constants
import com.czur.czurwma.myenum.TransmitFileResultEnum.*
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.utils.launch
import com.czur.czurwma.widget.CloudCommonPopupConstants
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.DownloadProgressListener
import com.czur.starry.device.file.server.TransmitFileHttpClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.CancellationException

/**
 * 13600kf 1600
 *     4060ti 3000
 *     z690p 900
 *     内存32g 350
 *     硬盘500g 200
 *     电源750w 300
 *     散热 100
 */
class TransmitFileDownloadViewModel(application: Application) : AndroidViewModel(application) {

    var pwd: String = ""
    var needPwd: Boolean = false
    private val TAG = "TransmitFileDownloadViewModel"
    private val MAX_FILE_SIZE = 4 * 1024 * 1024 * 1024L
    private val STOP_CURRENT_DOWNLOAD = "STOP_CURRENT_DOWNLOAD"
    private val STOP_ALL_DOWNLOAD = "STOP_ALL_DOWNLOAD"
    private val NO_DOWNLOAD_FILE = "NO_DOWNLOAD_FILE"


    private val applicationViewModel by lazy {
        (application as CzurWMAApplication).getEshareViewModel1()
    }

    var downloadLaunch: Job? = null
    var fileList: ArrayList<FileBrowserEntity.FileEntity> = ArrayList()
    var currentDownLoadFileEntity: FileBrowserEntity.FileEntity? = null
    var currentFileDownloadResultPair: Pair<String, TransmitFileResultEnum> =
        Pair("", CODE_9528)
    var reconnectEshareTimeVM = MutableSharedFlow<Long>()//重连Eshare

    var finishDownloadFilePathAndResult =
        MutableSharedFlow<Pair<String, TransmitFileResultEnum>>()//当前完成上传的文件,可以进行下一个了

    var fileDisplayListFlow = MutableSharedFlow<List<FileBrowserEntity.FileEntity>>()

    var lastErrorFilePath = ""

    var currentDownloadFileEntity: FileBrowserEntity.FileEntity? = null

    var displayProgressDialogFlow = MutableSharedFlow<Boolean>()


    fun initRefreshFileList(fileList: MutableList<FileBrowserEntity.FileEntity>) {
        this.fileList = fileList as ArrayList<FileBrowserEntity.FileEntity>
    }

    fun startDownload(context: Context) {
        logTagD(TAG,"startUpload.isActive = ${downloadLaunch?.isActive}")

        if (downloadLaunch?.isActive == true) {//如果正在下载,就不再进行下载
            return
        }

        downloadLaunch = launch(Dispatchers.IO) {
            delay(3000)
            val entity = fileList.firstOrNull {//查找当前可以进行继续下载的文件
                it.status == UploadFileEnum.DOWNLOADING
            } ?: kotlin.run {// 没有下载文件了就结束
                downloadLaunch?.cancel(CancellationException(NO_DOWNLOAD_FILE))
                return@launch
            }
            //复制entity
            currentDownloadFileEntity = entity.copy()
//            currentUploadStartByte = 0L
            currentFileDownloadResultPair = Pair(entity.absPath!!, CODE_9528)

            //检查是否连的通
            val checkResult = TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)


            if (!checkResult) {
                // 说明没有好用的端口
                currentFileDownloadResultPair =
                    Pair(entity.absPath!!, CODE_9527)
                downloadLaunch?.cancel()
                return@launch
            }


            // 看看服务是否还在
            val verifyCodeResult = withContext(Dispatchers.IO) {
                val code =
                    TransmitFileHttpClient.getVerifyCode(applicationViewModel.currentIP)
                code
            }
            val verifyCodeHandleResult = withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(verifyCodeResult.code, false)
            }


            if (!verifyCodeHandleResult) {
                currentFileDownloadResultPair =
                    Pair(
                        entity.absPath!!,
                        TransmitFileHttpClient.findTransmitResultEnum(verifyCodeResult.code)
                    )
                downloadLaunch?.cancel()
                return@launch
            }


            // 检查文件状态
            val checkFileResult = withContext(Dispatchers.IO) {
                val result = TransmitFileHttpClient.checkDownloadFile(
                    context,
                    entity.absPath!!,
                    applicationViewModel.currentIP,
                    entity.currentChunkNumber,
                    needPwd,
                    pwd
                )
                result
            }
            val checkFileHandleResult = withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(checkFileResult, false)
            }


            if (!checkFileHandleResult) {
                currentFileDownloadResultPair =
                    Pair(
                        entity.absPath!!,
                        TransmitFileHttpClient.findTransmitResultEnum(checkFileResult)
                    )
                downloadLaunch?.cancel()
                return@launch
            }


            var lastRefreshTime = 0L
            var localChunkFilePath = ""
            var chunkFileMD5 = ""
            var currentDownloadFileResultCode = TransmitFileHttpClient.downloadFile(
                context,
                entity.absPath!!,
                applicationViewModel.currentIP,
                entity.currentChunkNumber,
                needPwd,
                pwd,
                object : DownloadProgressListener {
                    override fun update(
                        currentChunkDownloadSize: String,
                        currentChunkFileSize: String,
                        currentChunkNumber: String,
                        allChunkFileSize: String,
                        totalChunkNumber: String,
                        currentChunkFileMD5: String,
                        localChunkPath: String
                    ) {

                        val currentChunkDownloadDone =
                            currentChunkDownloadSize == currentChunkFileSize
                        val progress =
                            if ((currentChunkDownloadSize.toLong() + entity.hasDownloadedChunkFilesSize.toLong()) == 0L) {
                                0L
                            } else {
                                100 * (currentChunkDownloadSize.toLong() + entity.hasDownloadedChunkFilesSize.toLong()) / allChunkFileSize.toLong()
                            }

                        val currentTime = System.currentTimeMillis()

                        // 每隔500ms以上更新一次
                        if ((currentTime - lastRefreshTime > 800) || currentChunkDownloadDone) {
                            logI("currentChunkDownloadSize = $currentChunkDownloadSize " +
                                        "currentChunkFileSize = $currentChunkFileSize " +
                                        "currentChunkNumber = $currentChunkNumber " +
                                        "allChunkFileSize = $allChunkFileSize " +
                                        "totalChunkNumber = $totalChunkNumber" +
                                        "localChunkPath = $localChunkPath"
                            )

                            val mapList =
                                fileList.map { it.copy() } as MutableList<FileBrowserEntity.FileEntity>;

                            mapList.find {
                                it.absPath == entity.absPath
                            }?.let {
                                it.progress = progress.toFloat()

                                it.currentChunkNumber = currentChunkNumber
                                it.totalChunkNumber = totalChunkNumber
                                it.currentChunkDownloadSize = currentChunkDownloadSize
                                it.currentChunkFileSize = currentChunkFileSize
                                it.allChunkFilesSize = allChunkFileSize
                                it.localFilePath = localChunkPath
                                localChunkFilePath = localChunkPath
                                chunkFileMD5 = currentChunkFileMD5
                            }
                            fileList.clear()
                            fileList.addAll(mapList)

                            lastRefreshTime = currentTime

                            logI("uploadFile-progress: $progress% bytes${currentChunkDownloadSize + "/"+entity.hasDownloadedChunkFilesSize.toLong()}")

                            launch {
                                fileDisplayListFlow.emit(mapList)
                            }
                        }


                    }


                })

            logI("currentDownloadFileResultCode $currentDownloadFileResultCode")


            if (currentDownloadFileResultCode != CODE_9527.code) {
                // md5验证
                CzurFileUtils.getFileMD5(File(localChunkFilePath))?.let {
                    if (it != chunkFileMD5) {
                        currentDownloadFileResultCode = CODE_9528.code
                        // 并且删除这个文件
                        try {
                            File(localChunkFilePath).delete()
                        } catch (_: Exception) {
                        }
                    }
                }
            }

            // 有可能9527 9528
            currentFileDownloadResultPair = Pair(
                entity.absPath!!,
                TransmitFileHttpClient.findTransmitResultEnum(currentDownloadFileResultCode)
            )
            withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(currentDownloadFileResultCode, false)
            }

        }

        downloadLaunch?.invokeOnCompletion {
            logTagD(TAG,"invokeOnCompletion = ${it?.message}")
            logTagD(TAG,
                "currentFilePostResult = ${currentFileDownloadResultPair.first} ${currentFileDownloadResultPair.second}"
            )
            launch(Dispatchers.IO) {
                when (it?.message) {
                    STOP_ALL_DOWNLOAD -> {
                        //停止上传,所有文件都变成failed状态
                        val mapList =
                            fileList.map { it.copy() } as List<FileBrowserEntity.FileEntity>;

                        val list = changeAllFileState(
                            mapList,
                            UploadFileEnum.FAIL
                        )
                        fileDisplayListFlow.emit(list)
                    }

                    STOP_CURRENT_DOWNLOAD -> {// 说明是手动停止的,不在这里做处理,会在stopUpload()里面处理

                    }

                    NO_DOWNLOAD_FILE -> {//没有文件上传了,不做任何处理

                    }

                    else -> {
                        if (currentFileDownloadResultPair.second == CODE_9527) {
                            var active = false
                            var isConnected = false
                            var times = 0
                            while (!active) {
                                logI("start time${System.currentTimeMillis()}")

                                val result1 =
                                    TransmitFileHttpClient.checkServer(
                                        applicationViewModel.currentIP,
                                        1000
                                    )
                                isConnected = result1
                                times += 1
                                delay(1000)
                                if (times == 3) {
                                    active = true
                                }
                            }
                            logI(
                                "checkServerFinish ${System.currentTimeMillis()} isConnected = $isConnected"
                            )

                            if (isConnected
                                && lastErrorFilePath.isNotEmpty()
                                && lastErrorFilePath != currentDownloadFileEntity?.absPath
                            ) {
                                /**
                                 * 加上这个逻辑避免有一些没有catch的9527类别错误变成死循环(9527后,ping通了,又尝试,又9527)
                                 * 现在只允许重试一次这样的逻辑
                                 */
                                finishDownloadFilePathAndResult.emit(
                                    Pair(
                                        currentFileDownloadResultPair.first!!,
                                        CODE_9528
                                    )
                                )
                            } else
                                if (isConnected) {

                                    lastErrorFilePath = currentDownloadFileEntity?.absPath ?: ""

                                    startDownload(context)
                                } else {
                                    finishDownloadFilePathAndResult.emit(
                                        currentFileDownloadResultPair
                                    )
                                }
                        } else {
                            withContext(Dispatchers.Main) {
                                TransmitFileHttpClient.handleResultMessage(
                                    currentFileDownloadResultPair.second.code,
                                    true
                                )
                            }

                            finishDownloadFilePathAndResult.emit(currentFileDownloadResultPair)
                        }
                    }
                }

                logTagD(TAG,"invokeOnCompletion.end")
                currentDownloadFileEntity = null
                currentFileDownloadResultPair = Pair("", CODE_9528)
            }

        }


    }


    // 网络连接失败情况,需要把所有数据都改成failed
    fun changeAllFileState(
        list: List<FileBrowserEntity.FileEntity>,
        targetEnum: UploadFileEnum
    ): List<FileBrowserEntity.FileEntity> {
        val mapList = list.map { it.copy() }
        mapList.forEach {
            if (it.status == UploadFileEnum.FAIL
                || it.status == UploadFileEnum.PAUSE
                || it.status == UploadFileEnum.UPLOADING
            ) {
                it.status = targetEnum
            }
        }
        return mapList
    }

    fun findMergeFilesList(context: Context, fileEntity: FileBrowserEntity.FileEntity): List<File> {
        val totalChunkNumber = fileEntity.totalChunkNumber.toInt()
        val allChunksFileSize = fileEntity.allChunkFilesSize
        val fileName = FileUtils.getFileName(fileEntity.absPath)


//        val fileName = fileEntity.absPath
        val allFileList: ArrayList<File> = ArrayList()
        for (i in 0 until totalChunkNumber) {
            val chunkFileName = "${fileName}_${allChunksFileSize}_${i}"
//            val chunkFile = File(chunkFileName)
            val chunkFile = File(
                context.filesDir,
                "${Constants.CZUR_SHARE_FOLDER}/${chunkFileName}"
            )
            if (chunkFile.exists()) {
                allFileList.add(chunkFile)
            } else {
                return ArrayList()
            }
        }

        return allFileList
    }

    fun deleteDownloadFile(
        context: Context,
        filePath: String,
        dataList: MutableList<FileBrowserEntity.FileEntity>
    ) {
        if (applicationViewModel.isCloseDOWNTransmitFileDeleteRemindDialog) {
            launch {
                deleteFileFromLocal(
                    context,
                    filePath,
                    dataList
                )
            }
        } else {
            deleteFileWithDialog(
                context,
                filePath,
                dataList
            )
        }
    }

    private fun deleteFileWithDialog(
        context: Context,
        filePath: String,
        dataList: MutableList<FileBrowserEntity.FileEntity>
    ) {
        TransmitFileDialogManager.showDeleteFileDialog(
            context
        ) { result, dialog, noMoreRemind ->

            if (result) {
                launch {
                    deleteFileFromLocal(
                        context,
                        filePath,
                        dataList
                    )
                }
                applicationViewModel.isCloseDOWNTransmitFileDeleteRemindDialog = noMoreRemind
                dialog.dismiss()
            } else {
                dialog.dismiss()
            }
        }
    }

    private fun deleteFileFromLocal(
        context: Context,
        filePath: String,
        dataList: MutableList<FileBrowserEntity.FileEntity>
    ) {

        var deleteLaunch: Job? = null
        deleteLaunch = launch(Dispatchers.IO) {

            val list = dataList
                .map { it.copy() } as MutableList<FileBrowserEntity.FileEntity>

            val fileEntity = list.firstOrNull {
                it.absPath == filePath
            }
            if (fileEntity?.status == UploadFileEnum.DONE_DOWNLOAD) {//已完成了不删除
                deleteLaunch?.cancel(CancellationException(CODE_9528.code.toString()))
                return@launch
            }

            // 文件超大就不md5然后掉接口了, 直接删除,因为远端也不会有
            if (fileEntity?.status == UploadFileEnum.LARGE_SIZE_ERROR) {
                launch {
                    list.find { it.absPath == filePath }?.let {
                        list.remove(it)
                    }
                    fileDisplayListFlow.emit(list)
                }
                deleteLaunch?.cancel(CancellationException(CODE_200.code.toString()))
                return@launch
            }

            // 没有问题就继续流程
            deleteLaunch?.cancel(CancellationException(CODE_200.code.toString()))
        }

        deleteLaunch.invokeOnCompletion { completeIt ->
            logI("删除:deleteLaunch.invokeOnCompletion = ${completeIt?.message}")
            val codeStr = completeIt?.message ?: "0"
            when (TransmitFileResultEnum.find(codeStr.toLong())) {
                CODE_200,
                CODE_202 -> {// 单个删除成功,刷新
                    logI("删除成功")
                    launch {


                        val list = dataList
                            .map { it.copy() } as MutableList<FileBrowserEntity.FileEntity>

                        list.find { it.absPath == filePath }?.let { fileEntityIt ->
                            val toInt = fileEntityIt.totalChunkNumber.toInt()
                            // 循环toInt次,删除相关的chunkFile
                            for (i in 0 until toInt) {
                                val chunkFileName =
                                    "${fileEntityIt.name}_${fileEntityIt.allChunkFilesSize}_${i}"
                                val chunkFile = File(
                                    context.filesDir,
                                    "${Constants.CZUR_SHARE_FOLDER}/${chunkFileName}"
                                )
                                if (chunkFile.exists()) {
                                    chunkFile.delete()
                                }
                            }
                            //删除数据库中的数据
//                            launch(Dispatchers.IO) {
//                                database.localDownloadFileDao().delete(fileEntityIt.id)
//                            }

                            list.remove(fileEntityIt)
                        }


                        if (filePath == currentDownloadFileEntity?.absPath) {
                            stopDownload()
                        }

                        fileDisplayListFlow.emit(list)
                        initRefreshFileList(list)
                        startDownload(context)
                    }
                }

                CODE_230 -> {
                    TransmitFileDialogManager.showInMeetingDialog()
                }

                CODE_210,
                CODE_220,
                CODE_300,
                CODE_400,
                CODE_9529,
                CODE_205,
                CODE_9528 -> {
                    logI("删除失败9528")
                    // 单个文件各种原因删除失败
                    ToastUtils.showLong(R.string.delete_file_failed)
                }

                CODE_9527 -> {// 网络问题
                    logI("删除失败9527")
                }

                CODE_404 -> {
                }

                CODE_405 -> {
                }

                CODE_406 ->{

                }
            }

        }
    }


    fun showReconnectDownloadDialog(context: Context, reconnectResult: (Boolean) -> Unit) {
        launch(Dispatchers.Main) {
            TransmitFileDialogManager.showReConnectDialog(CloudCommonPopupConstants.DOWNLOAD_FILE_ERROR_NET) { reconnect, dialogInterface ->
                dialogInterface.dismiss()
                logI("showReconnectUploadDialog reconnect = $reconnect")
                if (reconnect) {
                    launch(Dispatchers.IO) {
                        displayProgressDialogFlow.emit(true)
                        val checkServerResult = async {
                            val checkServer =
                                TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
                            if (!checkServer) {
                                delay(3000)
                                false
                            } else {
                                true
                            }
                        }

                        val delayTime = async {
                            delay(3000)
                            true
                        }
                        val checkServerAwait = checkServerResult.await()
                        val delayTimeAwait = delayTime.await()
                        if (checkServerAwait || delayTimeAwait) {
                            if (checkServerAwait) {
                                logI("showReconnectUploadDialog reconnect Result = true")
                                reconnectResult.invoke(true)
                                displayProgressDialogFlow.emit(false)
                            } else {
                                logI("showReconnectUploadDialog reconnect Result = false")
                                reconnectResult.invoke(false)
                                displayProgressDialogFlow.emit(false)
                            }
                        } else {
                            logI("showReconnectUploadDialog reconnect timeout Result = false")
                            reconnectResult.invoke(false)
                            displayProgressDialogFlow.emit(false)
                        }

                    }
                } else {
                    stopUploadAndFinish(context as Activity)
                }
            }
        }

    }


    fun stopDownload() {
        TransmitFileHttpClient.stopCurrentDownload()
        downloadLaunch?.cancel(CancellationException(STOP_CURRENT_DOWNLOAD))
    }

    fun stopUploadAndFinish(activity: Activity) {
        var hasDownload = false
        fileList.forEach {
            if (it.status == UploadFileEnum.DOWNLOADING
                || it.status == UploadFileEnum.PAUSE
            ) {
                hasDownload = true
                return@forEach
            }
        }
        if (hasDownload) {
            launch(Dispatchers.Main) {
                TransmitFileDialogManager.finishUploadActivityDialog(
                    CloudCommonPopupConstants.DOWNLOAD_FILE_TWO_BUTTON,
                    R.string.transmit_cancel_download_dialog,
                    activity
                ) { result, dialog ->
                    if (result) {
                        dialog.dismiss()
                    } else {
                        stopDownload()
                        dialog.dismiss()
                        activity.setResult(BaseActivity.RESULT_OK)
                        activity.finish()
                    }
                }
            }
        } else {
            stopDownload()
            activity.finish()
        }

    }
}

