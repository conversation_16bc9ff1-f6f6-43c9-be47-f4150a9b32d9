package com.czur.czurwma.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.blankj.utilcode.util.ActivityUtils
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.service.EShareHeartBeatService
import com.czur.czurwma.utils.launch
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import com.eshare.api.bean.Device
import com.eshare.api.callback.ConnectDeviceCallback
import com.eshare.api.utils.EShareException
import kotlinx.coroutines.Dispatchers
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class EShareApplicationViewModel(application: Application) : AndroidViewModel(application) {

    //    val userPreferences by lazy {
//        UserPreferences.getInstance()
//    }


    private val mExecutorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor()
    }

    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(application).device()
    }


    val esharePreferences by lazy {
        ESharePreferences.getInstance()
    }

    var isCloseUPTransmitFileDeleteRemindDialog = false // 是否关闭上传的提示删除文件的确认弹窗
    var isCloseDOWNTransmitFileDeleteRemindDialog = false // 是否关闭下载的提示删除文件的确认弹窗

    var currentIP = "" // 当前或者是上个连接的设备ip
    // 当前的连接设备s
    var currentDevice: Device? = null
        set(value) {
            field = value
            currentIP = value?.ipAddress ?: currentIP
        }

    // 无线投屏主页面的play状态 true:已投屏，显示stop；false：未投屏显示play
    var esharePlayStatus: Boolean = false

    var isReconnecting = false // 是否正在重新连接中
    //保存最后连接的投屏码
    fun savePinCode(inputCode: String) {
        esharePreferences.eSharePinCode = inputCode.uppercase()
    }

    fun getPinCode(): String {
        return esharePreferences.eSharePinCode ?: ""
    }

    //保存ip 对于的投屏码
    fun savePinCode(ip: String, pinCode: String) {
        esharePreferences.setESharePinCode(ip, pinCode.uppercase())
    }

    fun disconnectDevice() {
        if (mDeviceManager.isDeviceConnect && mDeviceManager.currentDevice != null){
            mDeviceManager.disconnectDevice(currentDevice?.name)
        }
    }
    suspend fun connectDeviceByAddress(address: String, result: (Boolean) -> Unit) {
        if (isReconnecting){
            return
        }

        isReconnecting = true
        mExecutorService.execute {
            mDeviceManager.connectDevice(address, "",
                object : ConnectDeviceCallback {
                    override fun onSuccess(device: Device) {
                        isReconnecting = false
                        currentDevice = device
                        launch(Dispatchers.IO) {
                            EShareHeartBeatService.startService(ActivityUtils.getTopActivity())
//                            HostHearBeat.get(mDeviceManager).startHearBeatThread()
                        }
                        result.invoke(true)
                    }

                    override fun onError(e: EShareException) {
                        isReconnecting = false
                        result.invoke(false)
                    }
                })
        }
    }


    override fun onCleared() {
        super.onCleared()
    }
}