package com.czur.czurwma.viewmodel

import android.app.Activity
import android.app.Application
import android.content.Context
import android.net.Uri
import androidx.lifecycle.AndroidViewModel
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.BaseActivity
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.common.TransmitFileEntity
import com.czur.czurwma.eshare.engine.Constants.FILE_NOT_FOUND_EXCEPTION
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.myenum.TransmitFileResultEnum.*
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.utils.launch
import com.czur.czurwma.widget.CloudCommonPopupConstants
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.ProgressRequestBody
import com.czur.starry.device.file.server.TransmitFileHttpClient
import com.eshare.api.bean.Device
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.withContext
import java.util.concurrent.CancellationException
import kotlin.properties.Delegates

class TransmitFileUploadViewModel(application: Application) : AndroidViewModel(application) {

    private val MAX_FILE_SIZE = 4 * 1024 * 1024 * 1024L
    private val STOP_CURRENT_UPLOAD = "STOP_CURRENT_UPLOAD"
    private val STOP_ALL_UPLOAD = "STOP_ALL_UPLOAD"
    private val NO_UPLOAD_FILE = "NO_UPLOAD_FILE"
    private val ERROR_NET = "ERROR_NET"
    private val DELETE_FAILED = "DELETE_FAILED"
    private val DELETE_SUCCESS = "DELETE_SUCCESS"


    var fileDisplayListFlow = MutableSharedFlow<MutableList<TransmitFileEntity>>()
    var displayProgressDialogFlow = MutableSharedFlow<Boolean>()
    var fileList: MutableList<TransmitFileEntity> = mutableListOf()
    var finishUploadFilePathAndResult =
        MutableSharedFlow<Pair<String, TransmitFileResultEnum>>()//当前完成上传的文件,可以进行下一个了
    var reconnectEshareTimeVM = MutableSharedFlow<Long>()//重连Eshare
    var uploadLaunch: Job? = null
    var currentUploadStartByte = 0L //当前上传的文件的开始字节
    var currentUploadFileEntity: TransmitFileEntity? = null
    var currentFilePostResult: Pair<String, TransmitFileResultEnum> =
        Pair("", CODE_9528)
    var lastErrorFilePath = ""

    private val applicationViewModel by lazy {
        (application as CzurWMAApplication).getEshareViewModel1()
    }

    var a: String by Delegates.observable("asd") { a, old, new ->
        println("a = $a,old = $old,new = $new")

    }

    override fun onCleared() {
        super.onCleared()
    }

    suspend fun restartUpload(context: Context) {
        val list = changeAllFileState(
            fileList,
            UploadFileEnum.UPLOADING
        )
        fileDisplayListFlow.emit(list)
        startUpload(context)
    }

    fun startUpload(context: Context) {
        logTagD("startUpload.isActive = ${uploadLaunch?.isActive}")

        if (uploadLaunch?.isActive == true) {
            return
        }

        uploadLaunch = launch(Dispatchers.IO) {

            val entity = fileList.firstOrNull {
                it.status == UploadFileEnum.UPLOADING
            } ?: kotlin.run {
                uploadLaunch?.cancel(CancellationException(NO_UPLOAD_FILE))
                return@launch
            }

            currentUploadFileEntity = entity
            currentUploadStartByte = 0L
            currentFilePostResult = Pair(entity.filePath, CODE_9528)


            val checkResult = TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
            if (!checkResult) {
                // 说明没有好用的端口
                currentFilePostResult = Pair(entity.filePath, CODE_9527)
                return@launch
            }


            val verifyCodeResult = withContext(Dispatchers.IO) {
                val code =
                    TransmitFileHttpClient.getVerifyCode(applicationViewModel.currentIP)
                code
            }
            val verifyCodeHandleResult = withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(verifyCodeResult.code, false)
            }
            if (!verifyCodeHandleResult) {
                currentFilePostResult =
                    Pair(entity.filePath, TransmitFileHttpClient.findTransmitResultEnum(verifyCodeResult.code))
                return@launch
            }

            val md5 = CzurFileUtils.getFileMD5(entity.uri, context)

//            val md5= entity.uri.md5()
            if (md5 == CODE_9528.code.toString()
                || md5 == CODE_9529.code.toString()
            ) {
                currentFilePostResult =
                    Pair(entity.filePath, TransmitFileResultEnum.find(md5.toLong()))
                return@launch
            }
            entity.md5 = md5

            val byteStartResult = TransmitFileHttpClient.getFileStartBytes(
                applicationViewModel.currentIP,
                entity,
                context
            )

            val handleResult = withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(byteStartResult.code, false)
            }
            if (!handleResult) {
                currentFilePostResult =
                    Pair(entity.filePath, TransmitFileHttpClient.findTransmitResultEnum(byteStartResult.code))
                return@launch
            }

            currentUploadStartByte = byteStartResult.byteStart

            var lastRefreshTime = 0L

            val currentPostFileResultCode = TransmitFileHttpClient.postFile(
                applicationViewModel.currentIP,
                entity,
                byteStartResult.byteStart,
                context,
                object : ProgressRequestBody.ProgressListener {
                    override fun update(bytesRead: Long, contentLength: Long, done: Boolean) {
                        val progress = if ((bytesRead + currentUploadStartByte) == 0L ||
                            contentLength + currentUploadStartByte == 0L
                        ) {
                            0L
                        } else {
                            (100 * (bytesRead + currentUploadStartByte)) / (contentLength + currentUploadStartByte)
                        }

                        val currentTime = System.currentTimeMillis()

                        // 每隔500ms以上更新一次
                        if ((currentTime - lastRefreshTime > 500) || done) {
                            val mapList =
                                fileList.map { it.copy() } as MutableList<TransmitFileEntity>;

                            mapList.find {
                                it.filePath == entity.filePath
                            }?.let {
                                if (done) {
                                    it.progress = 100f
                                } else {
                                    it.progress = progress.toFloat()
                                }
                            }

                            lastRefreshTime = currentTime

                            logI("uploadFile-progress: $progress% bytes${bytesRead + currentUploadStartByte}")

                            launch {
                                fileDisplayListFlow.emit(mapList)
                            }
                        }


                    }

                })

            // 有可能9527 9528
            currentFilePostResult = Pair(
                entity.filePath,
                TransmitFileHttpClient.findTransmitResultEnum(currentPostFileResultCode)
            )
            withContext(Dispatchers.Main) {
                TransmitFileHttpClient.handleResultMessage(currentPostFileResultCode, false)
            }

        }

        uploadLaunch?.invokeOnCompletion {
            logTagD("invokeOnCompletion = ${it?.message}")
            logTagD(
                "currentFilePostResult = ${currentFilePostResult.first} ${currentFilePostResult.second}"
            )
            launch(Dispatchers.IO) {
                when (it?.message) {
                    STOP_ALL_UPLOAD -> {
                        //停止上传,所有文件都变成failed状态
                        val mapList =
                            fileList.map { it.copy() } as MutableList<TransmitFileEntity>;

                        val list = changeAllFileState(
                            mapList,
                            UploadFileEnum.FAIL
                        )
                        fileDisplayListFlow.emit(list)
                    }

                    STOP_CURRENT_UPLOAD -> {// 说明是手动停止的,不在这里做处理,会在stopUpload()里面处理

                    }

                    NO_UPLOAD_FILE -> {//没有文件上传了,不做任何处理

                    }

                    else -> {
                        if (currentFilePostResult.second == CODE_9527) {
                            var active = false
                            var isConnected = false
                            var times = 0
                            while (!active) {
                                logI("start time${System.currentTimeMillis()}")

                                val result1 =
                                    TransmitFileHttpClient.checkServer(
                                        applicationViewModel.currentIP,
                                        1000
                                    )
                                isConnected = result1
                                times += 1
                                delay(1000)
                                if (times == 3) {
                                    active = true
                                }
                            }
                            logI(
                                "checkServerFinish ${System.currentTimeMillis()} isConnected = $isConnected"
                            )

                            if (isConnected
                                && lastErrorFilePath.isNotEmpty()
                                && lastErrorFilePath != currentUploadFileEntity?.filePath
                            ) {
                                /**
                                 * 加上这个逻辑避免有一些没有catch的9527类别错误变成死循环(9527后,ping通了,又尝试,又9527)
                                 * 现在只允许重试一次这样的逻辑
                                 */
                                finishUploadFilePathAndResult.emit(
                                    Pair(
                                        currentFilePostResult.first,
                                        CODE_9528
                                    )
                                )
                            } else
                                if (isConnected) {

                                    lastErrorFilePath = currentUploadFileEntity?.filePath ?: ""
                                    currentUploadFileEntity
                                    startUpload(context)
                                } else {
                                    finishUploadFilePathAndResult.emit(currentFilePostResult)
                                }
                        } else {
                            withContext(Dispatchers.Main) {
                                TransmitFileHttpClient.handleResultMessage(
                                    currentFilePostResult.second.code,
                                    true
                                )
                            }

                            finishUploadFilePathAndResult.emit(currentFilePostResult)
                        }
                    }
                }

                logTagD("invokeOnCompletion.end")
                currentUploadFileEntity = null
                currentFilePostResult = Pair("", CODE_9528)
            }

        }


    }

    // 停止当前上传动作,暂时没有处理UI
    fun stopAllUpload() {
        CzurFileUtils.md5WorkFlag = false
        TransmitFileHttpClient.stopCurrentPost()
        uploadLaunch?.cancel(CancellationException(STOP_ALL_UPLOAD))
    }

    // 停止当前上传动作,暂时没有处理UI
    fun stopUpload() {
        TransmitFileHttpClient.stopCurrentPost()
        uploadLaunch?.cancel(CancellationException(STOP_CURRENT_UPLOAD))
    }

    fun showReconnectUploadDialog(context: Context, reconnectResult: (Boolean) -> Unit) {
        launch(Dispatchers.Main) {
            TransmitFileDialogManager.showReConnectDialog(CloudCommonPopupConstants.UPLOAD_FILE_ERROR_NET) { reconnect, dialogInterface ->
                dialogInterface.dismiss()
                logI("showReconnectUploadDialog reconnect = $reconnect")
                if (reconnect) {
                    launch(Dispatchers.IO) {
                        displayProgressDialogFlow.emit(true)
                        val checkServerResult = async {
                            val checkServer =
                                TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)
                            if (!checkServer) {
                                delay(3000)
                                false
                            } else {
                                true
                            }
                        }

                        val delayTime = async {
                            delay(3000)
                            true
                        }
                        val checkServerAwait = checkServerResult.await()
                        val delayTimeAwait = delayTime.await()
                        if (checkServerAwait || delayTimeAwait) {
                            if (checkServerAwait) {
                                logI("showReconnectUploadDialog reconnect Result = true")
                                reconnectResult.invoke(true)
                                displayProgressDialogFlow.emit(false)
                            } else {
                                logI("showReconnectUploadDialog reconnect Result = false")
                                reconnectResult.invoke(false)
                                displayProgressDialogFlow.emit(false)
                            }
                        } else {
                            logI("showReconnectUploadDialog reconnect timeout Result = false")
                            reconnectResult.invoke(false)
                            displayProgressDialogFlow.emit(false)
                        }

                    }
                } else {
                    stopUploadAndFinish(context as Activity)
                }
            }
        }

    }

    fun deleteFile(
        context: Context,
        filePath: String,
        dataList: MutableList<TransmitFileEntity>
    ) {
        if (applicationViewModel.isCloseUPTransmitFileDeleteRemindDialog) {
            launch {
                deleteFileFromStarry(
                    context,
                    filePath,
                    dataList
                )
            }
        } else {
            deleteFileWithDialog(
                context,
                filePath,
                dataList
            )
        }
    }

    private fun showReDeleteFileDialog() {
        if (applicationViewModel.isCloseUPTransmitFileDeleteRemindDialog) {
            launch {
                deleteFileFromStarry(
                    getApplication(),
                    currentUploadFileEntity?.filePath ?: "",
                    fileList
                )
            }
        } else {
            deleteFileWithDialog(
                getApplication(),
                currentUploadFileEntity?.filePath ?: "",
                fileList
            )
        }
    }

    private fun deleteFileWithDialog(
        context: Context,
        filePath: String,
        dataList: MutableList<TransmitFileEntity>
    ) {
        TransmitFileDialogManager.showDeleteFileDialog(
            context
        ) { result, dialog, noMoreRemind ->

            if (result) {
                launch {
                    deleteFileFromStarry(
                        context,
                        filePath,
                        dataList
                    )
                }
                applicationViewModel.isCloseUPTransmitFileDeleteRemindDialog = noMoreRemind
                dialog.dismiss()
            } else {
                dialog.dismiss()
            }
        }
    }

    private fun deleteFileFromStarry(
        context: Context,
        filePath: String,
        dataList: MutableList<TransmitFileEntity>
    ) {

        var deleteLaunch: Job? = null
        deleteLaunch = launch(Dispatchers.IO) {

            val list = dataList
                .map { it.copy() } as MutableList<TransmitFileEntity>

            val fileEntity = list.firstOrNull {
                it.filePath == filePath
            }
            if (fileEntity?.status == UploadFileEnum.DONE_UPLOAD) {//已完成了不删除
                deleteLaunch?.cancel(CancellationException(CODE_9528.code.toString()))
                return@launch
            }

            // 文件超大就不md5然后掉接口了, 直接删除,因为远端也不会有
            if (fileEntity?.status == UploadFileEnum.LARGE_SIZE_ERROR) {
                launch {
                    list.find { it.filePath == filePath }?.let {
                        list.remove(it)
                    }
                    fileDisplayListFlow.emit(list)
                }
                deleteLaunch?.cancel(CancellationException(CODE_200.code.toString()))
                return@launch
            }

            displayProgressDialogFlow.emit(true)

            val checkServer = TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)

            if (!checkServer) {
                deleteLaunch?.cancel(CancellationException(CODE_9527.code.toString()))
                // 说明没有好用的端口
                return@launch
            }

            if (fileEntity?.uri == null) {
                deleteLaunch?.cancel(CancellationException(CODE_9528.code.toString()))
                return@launch
            }

            val deleteFileResult =
                TransmitFileHttpClient.deleteFile(context, fileEntity, applicationViewModel.currentIP)

            deleteLaunch?.cancel(CancellationException(deleteFileResult.toString()))
        }

        deleteLaunch.invokeOnCompletion { completeIt ->
            logI("删除:deleteLaunch.invokeOnCompletion = ${completeIt?.message}")
            val codeStr = completeIt?.message ?: "0"
            when (TransmitFileResultEnum.find(codeStr.toLong())) {
                CODE_200,
                CODE_202 -> {// 单个删除成功,刷新
                    logI("删除成功")
                    launch {
                        val list = dataList
                            .map { it.copy() } as MutableList<TransmitFileEntity>

                        list.find { it.filePath == filePath }?.let {
                            list.remove(it)
                        }

                        if (filePath == currentUploadFileEntity?.filePath) {
                            stopUpload()
                        }

                        fileDisplayListFlow.emit(list)
                        displayProgressDialogFlow.emit(false)
                        startUpload(context)
                    }
                }

                CODE_230 -> {
                    TransmitFileDialogManager.showInMeetingDialog()
                }

                CODE_210,
                CODE_220,
                CODE_300,
                CODE_400,
                CODE_9529,
                CODE_205,
                CODE_9528 -> {
                    logI("删除失败9528")
                    // 单个文件各种原因删除失败
                    ToastUtils.showLong(R.string.delete_file_failed)
                    launch {
                        displayProgressDialogFlow.emit(false)
                    }
                }

                CODE_9527 -> {// 网络问题
                    logI("删除失败9527")
                    launch {
                        displayProgressDialogFlow.emit(false)
                        finishUploadFilePathAndResult.emit(
                            Pair(
                                filePath,
                                CODE_9527
                            )
                        )
                    }

                }

                CODE_404 -> {
                }
                CODE_405 -> {
                }
                CODE_406 -> {
                }
            }

        }
    }


    fun initRefreshList(context: Context, fileUris: MutableList<Uri>?) {
        if (fileUris != null) {
            fileList = makeDataList(context, fileUris)
            launch {
                fileDisplayListFlow.emit(fileList)
            }
        }
    }

    //继续添加文件
    suspend fun addFileUris(context: Context, fileUris: MutableList<Uri>) {
        val map = fileList.map { it.copy() }.toMutableList()
        map.addAll(makeDataList(context, fileUris))
        fileDisplayListFlow.emit(map)
        startUpload(context)
    }

    private fun makeDataList(
        context: Context,
        fileUris: MutableList<Uri>
    ): MutableList<TransmitFileEntity> {
        val newDataList = mutableListOf<TransmitFileEntity>()
        val currentTimeMillis = System.currentTimeMillis()
        for ((index, uri) in fileUris.withIndex()) {
            println("uri = ${uri.toString()}")
            // 进度,开始字符串,状态,名字,大小
            var fileName = CzurFileUtils.getFileName(context, uri)
            val fileSize = CzurFileUtils.getFileSize(context, uri)
            val filePath = currentTimeMillis.toString() + uri.path
            var status =
//                if (NetworkUtils.isWifiConnected()) {
                    UploadFileEnum.UPLOADING
//                } else {
//                    UploadFileEnum.FAIL
//                }
            val fileSizeLong = fileSize?.toLong() ?: 0L
            // 文件大于4G不让上传

            if (fileSizeLong > MAX_FILE_SIZE) {
                status = UploadFileEnum.LARGE_SIZE_ERROR
            }
            if (FILE_NOT_FOUND_EXCEPTION == fileName){
                continue
            }
            logD("fileName = ${fileName},fileSize = ${fileSize},filePath = ${filePath}")
            newDataList.add(
                TransmitFileEntity(
                    fileName = fileName,
                    fileSize = fileSize.toString(),
                    filePath = filePath,
                    progress = 0f,
                    uri = uri,
                    status = status
                )
            )
        }
        return newDataList
    }

    // 网络连接失败情况,需要把所有数据都改成failed
    fun changeAllFileState(
        list: MutableList<TransmitFileEntity>,
        targetEnum: UploadFileEnum
    ): MutableList<TransmitFileEntity> {
        val mapList = list.map { it.copy() } as MutableList<TransmitFileEntity>;
        mapList.forEach {
            if (it.status == UploadFileEnum.FAIL
                || it.status == UploadFileEnum.PAUSE
                || it.status == UploadFileEnum.UPLOADING
            ) {
                it.status = targetEnum
            }
        }
        return mapList
    }

    fun stopUploadAndFinish(activity: Activity) {
        var hasUploading = false
        fileList.forEach {
            if (it.status == UploadFileEnum.UPLOADING
                || it.status == UploadFileEnum.PAUSE
            ) {
                hasUploading = true
                return@forEach
            }
        }
        if (hasUploading) {
            launch(Dispatchers.Main) {
                TransmitFileDialogManager.finishUploadActivityDialog(CloudCommonPopupConstants.UPLOAD_FILE_TWO_BUTTON
                    ,R.string.transmit_cancel_upload_dialog
                    ,activity) { result, dialog ->
                    if (result) {
                        dialog.dismiss()
                    } else {
                        stopUpload()
                        dialog.dismiss()
                        activity.setResult(BaseActivity.RESULT_OK)
                        activity.finish()
                    }
                }
            }
        } else {
            stopUpload()
            activity.setResult(BaseActivity.RESULT_OK)
            activity.finish()
        }
    }

    // 检查设备和网络是否连接正常
    private fun checkDeviceAndNetIsConnected(currentDevice: Device?): Boolean {

        return currentDevice?.ipAddress != null
    }

}