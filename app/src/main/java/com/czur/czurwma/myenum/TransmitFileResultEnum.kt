package com.czur.czurwma.myenum

enum class TransmitFileResultEnum(val code: Long, val msg: String = "") {
    CODE_200(200L),// 正常
    CODE_202(202L),// 校验码
    CODE_205(205L),// md5校验失败
    CODE_210(210L),// 空间不足
    CODE_220(220L),// 上传开关关闭
    CODE_230(230L),// 服务器负载过高
    CODE_300(300L),// 起始字节错误
    CODE_400(400L),// 未知错误
    CODE_404(404L),// 文件不存在
    CODE_405(405L),// 文件加锁
    CODE_406(406L),// 密码错误
    CODE_9527(-9527L), // 因为网络联不通或者服务器错误
    CODE_9528(-9528L), // 接口连通了,返回的内容无法解析
    CODE_9529(-9529L); // 文件不存在(上传中,删掉源文件)



    companion object {
        fun find(code: Long): TransmitFileResultEnum {
            for (value in values()) {
                if (value.code == code) {
                    return value
                }
            }
            return CODE_200
        }
    }
}

class DownloadFileResultEntity(
    var resultEnum: TransmitFileResultEnum = TransmitFileResultEnum.CODE_200,
)