package com.czur.czurwma;


import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.czurutils.log.CZURLogUtilsKt;
import com.czur.czurwma.utils.Android16AdaptationManager;
import com.czur.czurwma.utils.EdgeToEdgeUtils;
import com.czur.czurwma.widget.NoNetworkDialogFragment;
import com.czur.czurwma.widget.ProgressDialogFragment;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 必须重写的方法
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class BaseActivity extends AppCompatActivity {
    private static final String TAG = BaseActivity.class.getSimpleName();
    protected boolean activityDestroyed = false;
    private int progressDialogRefCount = 0;
    public static boolean isActive = true;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onCreate()", getClass().getSimpleName()));

        // Android 16 Edge-to-Edge适配
        EdgeToEdgeUtils.enableEdgeToEdge(this);

        BarUtils.setNavBarLightMode(this, true);
        BarUtils.setNavBarColor(this, getColor(R.color.gary_f9));
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
//        new ViewModelProvider((ViewModelStoreOwner) getApplicationContext()).get(EShareViewModel.class);
    }

    @Override
    public boolean moveTaskToBack(boolean nonRoot) {
        return super.moveTaskToBack(nonRoot);
    }


    /**
     * @des: 设置状态栏颜色
     * @params: resId
     * @return:
     */
    public void setStatusBarColor(int resId) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (resId > 0) {
                Window window = getWindow();
                window.setStatusBarColor(getResources().getColor(resId));
            }
        }
    }


    /**
     * @des: 设置透明状态栏亮度色模式
     * @params:
     * @return:
     */
    protected void setStatusBarLightMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }

    /**
     * @des: 短Toast
     * @params:
     * @return:
     */

    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }

    public void showMessage(int resId, Object... args) {
        ToastUtils.showShort(resId, args);
    }

    /**
     * @des: 长Toast
     * @params:
     * @return:
     */
    public void showLongMessage(int resId) {
        ToastUtils.showLong(resId);
    }

    public void showLongMessage(String text) {
        ToastUtils.showLong(text);
    }

    public void showLongMessage(int resId, Object... args) {
        ToastUtils.showLong(resId, args);
    }


    /**
     * @des: 显示加载
     * @params:
     * @return:
     */
    public void showProgressDialog() {
        showProgressDialog(true, false, null, false, false);
    }

    public void showProgressDialog(boolean isDark) {
        showProgressDialog(true, false, null, isDark, false);
    }

    public void showProgressDialog(boolean isDark, boolean isCancelable) {
        showProgressDialog(isCancelable, false, null, isDark, false);
    }

    public void showProgressDialog(String tag) {
        showProgressDialog(true, false, tag, false, false);
    }

    public void showProgressDialogMoment() {
        showProgressDialog(true, false, null, false, true);
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        }, 300);
    }

    /**
     * @des: 初始化一个加载dialog
     * @params:[cancelable, touchable, tag]
     * @return:void
     */

    public void showProgressDialog(boolean cancelable, boolean touchable, String tag, boolean isDark, boolean isMomentDialog) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (progressDialogRefCount == 0) {
                    FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
                    ProgressDialogFragment prevDialog = (ProgressDialogFragment) getSupportFragmentManager().findFragmentByTag(ProgressDialogFragment.TAG);
                    if (prevDialog != null) {
                        fragmentTransaction.remove(prevDialog);
                    }

                    ProgressDialogFragment progressDialog = ProgressDialogFragment.newInstance(tag);
                    progressDialog.setIsDark(isDark);
                    progressDialog.setCancelable(cancelable);
                    progressDialog.setTouchable(touchable);
                    progressDialog.setMomentDialog(isMomentDialog);
                    progressDialog.setOnCancelListener(new ProgressDialogFragment.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialog, String tag) {
                            hideProgressDialog(true);
                        }
                    });
                    fragmentTransaction.add(progressDialog, ProgressDialogFragment.TAG);
                    fragmentTransaction.commitAllowingStateLoss();
                }

                progressDialogRefCount++;
            }
        });

    }


    NetworkUtils.OnNetworkStatusChangedListener onNetworkStatusChangedListener;

    public void setNetListener() {
        onNetworkStatusChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {
                showNoNetDialog();
            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                dismissNoNetDialog();
            }
        };
        NetworkUtils.registerNetworkStatusChangedListener(onNetworkStatusChangedListener);
    }


    boolean isNoNetShow;

    private void showNoNetDialog() {
        if (!isNoNetShow) {
            FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
            Fragment fragment = getSupportFragmentManager().findFragmentByTag(NoNetworkDialogFragment.TAG);
            if (fragment != null) {
                fragmentTransaction.show(fragment);
                isNoNetShow = true;
                return;
            }
            NoNetworkDialogFragment progressDialog = NoNetworkDialogFragment.newInstance(null);
            progressDialog.setCancelable(false);
            progressDialog.setOnCancelListener(new NoNetworkDialogFragment.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog, String tag) {
                    dismissNoNetDialog();
                }
            });
            fragmentTransaction.add(progressDialog, ProgressDialogFragment.TAG);
            fragmentTransaction.commitAllowingStateLoss();
            isNoNetShow = true;
        }
    }


    private void dismissNoNetDialog() {
        if (isNoNetShow) {
            Fragment fragment = getSupportFragmentManager().findFragmentByTag(NoNetworkDialogFragment.TAG);
            if (fragment == null) {
                isNoNetShow = false;
                return;
            }
            FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
            fragmentTransaction.remove(fragment);
            fragmentTransaction.commitAllowingStateLoss();
            isNoNetShow = false;
        }
    }

    /**
     * @des: 隐藏加载
     * @params:
     * @return:
     */
    public void hideProgressDialog(final boolean immediately) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                progressDialogRefCount--;
                if (immediately){
                    progressDialogRefCount = 0;
                }
                if (progressDialogRefCount <= 0) {
                    progressDialogRefCount = 0;
                    ProgressDialogFragment fragment = null;
                    try {
                        fragment = (ProgressDialogFragment) getSupportFragmentManager().findFragmentByTag(ProgressDialogFragment.TAG);
                    } catch (Exception e) {
                        //
                    }
                    if (fragment == null) {
                        return;
                    }
                    fragment.getDialog().cancel();
                    FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
                    fragmentTransaction.remove(fragment);
                    fragmentTransaction.commitAllowingStateLoss();
                }
            }
        });

    }

    public void hideProgressDialog() {
        hideProgressDialog(false);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
//        Log.i(TAG, String.format("%s.onNewIntent()", getClass().getSimpleName()));
    }

    @Override
    protected void onStart() {
        super.onStart();
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onStart()", getClass().getSimpleName()));
    }

    @Override
    protected void onResume() {
        super.onResume();
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onResume()", getClass().getSimpleName()));
//        MobSDKUtils.INSTANCE.onResume(this);

        if (!isActive) {
            //app 从后台唤醒，进入前台
            isActive = true;
//            Log.i(getClass().getSimpleName(), "程序从后台唤醒");
        }
    }


    /**
     * 检查是否重启心跳
     *
     * @param
     * @return
     */

    @Override
    protected void onPause() {
        super.onPause();
//        MobSDKUtils.INSTANCE.onPause(this);
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onPause()", getClass().getSimpleName()));
    }

//    protected void startAutoSync() {
//        if (UserPreferences.getInstance(this).isUserLogin()) {
//            if (NetworkUtils.isConnected()) {
//                if (UserPreferences.getInstance(this).getIsAutoSync()) {
//                    if (UserPreferences.getInstance(this).getIsSyncOnlyWifi()) {
//                        if (NetworkUtils.isWifiConnected()) {
//                            if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
//                                EventBus.getDefault().post(new ResetTimeCountEvent(EventType.RESET_TIME_COUNT));
//                            } else {
//                                startTimeCountService();
//                            }
//                        }
//                    } else {
//                        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
//                            EventBus.getDefault().post(new ResetTimeCountEvent(EventType.RESET_TIME_COUNT));
//                        } else {
//                            startTimeCountService();
//                        }
//                    }
//
//                }
//            }
//        }
//
//    }

    /**
     * @des: 如果计时service在运行则关闭再重新开启
     * @params:
     * @return:
     */
//    private void startTimeCountService() {
//        Intent intent = new Intent(this, AutoSyncTimeCountService.class);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForegroundService(intent);
//        } else {
//            startService(intent);
//        }
//    }

//    protected void startSyncNow() {
//        if (NetworkUtils.isConnected()) {
//            if (UserPreferences.getInstance(this).isUserLogin() && UserPreferences.getInstance(this).isHasAuraMate()) {
//                if (!ServiceUtils.isServiceRunning(SyncService.class)) {
//                    if (UserPreferences.getInstance(this).getIsAutoSync()) {
//                        if (UserPreferences.getInstance(this).getIsSyncOnlyWifi()) {
//                            if (NetworkUtils.isWifiConnected()) {
//                                startSyncService();
//                            }
//                        } else {
//                            startSyncService();
//                        }
//
//                    }
//                }
//            }
//        }
//    }


    /**
     * 开启心跳服务
     *
     * @param
     * @return
     */

    protected void startNetty() {
//        UserPreferences userPreferences = UserPreferences.getInstance(this);
//        if (NetworkUtils.isConnected()) {
//            if (userPreferences.isUserLogin() && userPreferences.isHasAuraMate()) {
//                if (!ServiceUtils.isServiceRunning(NettyService.class)) {
//                    NettyUtils.getInstance().startNettyService();
//                }
//            }
//        }
    }


    protected void removeStickyEvent() {
//        SyncFinishEvent syncFinishEvent = EventBus.getDefault().getStickyEvent(SyncFinishEvent.class);
//        if (syncFinishEvent != null) {
//            EventBus.getDefault().removeStickyEvent(syncFinishEvent);
//        }
//        SynchronizingEvent synchronizingEvent = EventBus.getDefault().getStickyEvent(SynchronizingEvent.class);
//        if (synchronizingEvent != null) {
//            EventBus.getDefault().removeStickyEvent(synchronizingEvent);
//        }
//        NoticeServiceIsStopEvent noticeServiceIsStopEvent = EventBus.getDefault().getStickyEvent(NoticeServiceIsStopEvent.class);
//        if (noticeServiceIsStopEvent != null) {
//            EventBus.getDefault().removeStickyEvent(noticeServiceIsStopEvent);
//        }
//
//        NoticeServiceEnoughStopEvent noticeServiceEnoughStopEvent = EventBus.getDefault().getStickyEvent(NoticeServiceEnoughStopEvent.class);
//        if (noticeServiceEnoughStopEvent != null) {
//            EventBus.getDefault().removeStickyEvent(noticeServiceEnoughStopEvent);
//        }
//
//        BooksOrPagesChangedEvent booksOrPagesChangedEvent = EventBus.getDefault().getStickyEvent(BooksOrPagesChangedEvent.class);
//        if (booksOrPagesChangedEvent != null) {
//            EventBus.getDefault().removeStickyEvent(booksOrPagesChangedEvent);
//        }
//        DownloadMp3FailedEvent downloadMp3FailedEvent = EventBus.getDefault().getStickyEvent(DownloadMp3FailedEvent.class);
//        if (downloadMp3FailedEvent != null) {
//            logI("removeStickyEvent DownloadMp3FailedEvent");
//            EventBus.getDefault().removeStickyEvent(downloadMp3FailedEvent);
//        }
//
//        DownloadMp3SuccessEvent downloadMp3SuccessEvent = EventBus.getDefault().getStickyEvent(DownloadMp3SuccessEvent.class);
//        if (downloadMp3SuccessEvent != null) {
//            logI("removeStickyEvent DownloadMp3SuccessEvent");
//            EventBus.getDefault().removeStickyEvent(downloadMp3SuccessEvent);
//        }
    }

    /**
     * @des: 如果计时service在运行则关闭service
     * @params:
     * @return:
     */
    protected void startSyncService() {
//        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
//            EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
//        }
//        Intent intent = new Intent(this, SyncService.class);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForegroundService(intent);
//        } else {
//            startService(intent);
//        }

    }

    @Override
    protected void onStop() {
        super.onStop();
        if (!AppUtils.isAppForeground()) {
            //app 进入后台
            isActive = false;//记录当前已经进入后台
//            Log.i(getClass().getSimpleName(), "程序进入后台");
        }
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onStop()", getClass().getSimpleName()));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        activityDestroyed = true;
        CZURLogUtilsKt.logTagI(TAG, String.format("life-%s.onDestroy()", getClass().getSimpleName()));
        if (onNetworkStatusChangedListener != null) {
            NetworkUtils.unregisterNetworkStatusChangedListener(onNetworkStatusChangedListener);
        }
    }

//    protected boolean isValidatorLoginName(String name) {
//        if (BuildConfig.IS_OVERSEAS) {
//            return !StringUtilsKt.isValidEmail(name);
//        } else {
//            return !RegexUtils.isMobileExact(name) && !StringUtilsKt.isValidEmail(name);
//        }
//    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
//        Log.i(TAG, String.format("%s.onSaveInstanceState()", getClass().getSimpleName()));
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
//        Log.i(TAG, String.format("%s.onRestoreInstanceState()", getClass().getSimpleName()));
    }

    public void setServerTime() {
//        if (Validator.isEmpty(UserPreferences.getInstance(this).getReportTime())) {
//            getServerTime();
//        }
    }

    private void getServerTime() {
//        UserPreferences userPreferences = UserPreferences.getInstance(this);
//        HttpManager.getInstance().request().getReportServerTime(userPreferences.getUserId(), String.class, new MiaoHttpManager.Callback<String>() {
//            @Override
//            public void onStart() {
//
//            }
//
//            @Override
//            public void onResponse(MiaoHttpEntity<String> entity) {
//                String serverTime = entity.getBody();
//                userPreferences.setReportTime(serverTime);
//                userPreferences.setCallTime(serverTime);
////                logE("第一次存储报告时间" + serverTime);
//            }
//
//            @Override
//            public void onFailure(MiaoHttpEntity<String> entity) {
//                hideProgressDialog();
//                showMessage(R.string.toast_server_error);
//            }
//
//            @Override
//            public void onError(Exception e) {
//                hideProgressDialog();
//                showMessage(R.string.toast_server_error);
//            }
//        });
    }

    @Override
    public Resources getResources() {//还原字体大小
        Resources res = super.getResources();
        Configuration configuration = res.getConfiguration();
        if (configuration.fontScale != 1f) {//fontScale要缩放的比例
            configuration.fontScale = 1f;
            res.updateConfiguration(configuration, res.getDisplayMetrics());
        }
        return res;
    }

    // Jason
//    public void showProgressDialogSitting() {
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
//                DialogProgressFragment prevDialog = (DialogProgressFragment) getSupportFragmentManager().findFragmentByTag(DialogProgressFragment.TAG);
//                if (prevDialog != null) {
//                    fragmentTransaction.remove(prevDialog);
//                }
//
//                DialogProgressFragment progressDialog = DialogProgressFragment.newInstance(null);
//                progressDialog.setIsDark(true);
//                progressDialog.setCancelable(true);
//                progressDialog.setTouchable(true);
//                progressDialog.setOnCancelListener(new DialogProgressFragment.OnCancelListener() {
//                    @Override
//                    public void onCancel(DialogInterface dialog, String tag) {
//                        hideProgressDialogSitting();
//                    }
//                });
//                fragmentTransaction.add(progressDialog, DialogProgressFragment.TAG);
//                fragmentTransaction.commitAllowingStateLoss();
//            }
//        });
//    }

//    public void hideProgressDialogSitting() {
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                DialogProgressFragment fragment = null;
//                try {
//                    fragment = (DialogProgressFragment) getSupportFragmentManager().findFragmentByTag(DialogProgressFragment.TAG);
//                }catch (Exception e) {
//                    //
//                }
//                if (fragment == null) {
//                    return;
//                }
//                fragment.getDialog().cancel();
//                FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
//                fragmentTransaction.remove(fragment);
//                fragmentTransaction.commitAllowingStateLoss();
//            }
//        });
//    }

}
