package com.czur.czurwma.preferences;

import android.app.Application;
import android.content.Context;

import com.czur.czurwma.utils.Validator;

public class TransmitFilePreferences extends BasePreferences {

    private static final String PREF = TransmitFilePreferences.class.getSimpleName();
    //基础变量
    private static final String FILE_PATH = "transmit_download_file_path";
    private static final String PINCODEMAP = "transmit_download_chunk_number";
    private static final String MORE_VISIBLE = "czur_eshare_more_visible";
    private static final String BACK_VISIBLE = "czur_eshare_back_visible";

    private static final String TRANSFER_FILE_CODE = "TRANSFER_FILE_CODE";
    private static final String UUID = "UUID";

    private static TransmitFilePreferences instance;

    public static void init(Application application) {
        instance = new TransmitFilePreferences(application, PREF);
    }

    public static TransmitFilePreferences getInstance() {
        return instance;
    }

    public static TransmitFilePreferences getInstance(Context context) {
        if (instance == null) {
            instance = new TransmitFilePreferences(context, PREF);
        }
        return instance;
    }

    public TransmitFilePreferences(Context context, String prefsName) {
        super(context, prefsName);
    }


    // EShare save pinCode
//    public void setESharePinCode(String pincode) {
//        put(PINCODE, pincode);
//    }
//
//    public String getESharePinCode() {
//        Object obj = get(PINCODE);
//        if (Validator.isEmpty(obj)) {
//            return "";
//        } else {
//            return (String) obj;
//        }
//    }

    // EShare save pinCode
    public void setUUID(String pincode) {
        put(UUID, pincode);
    }

    public String getUUID() {
        Object obj = get(UUID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    // 文件传输验证码
    public void setTransmitFileCode(String code) {
        put(TRANSFER_FILE_CODE, code);
    }

    public String getTransmitFileCode() {
        Object obj = get(TRANSFER_FILE_CODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    //绑定ip
//    public void setESharePinCode(String ip, String pincode) {
//        put(PINCODE + ip, pincode);
//    }
//
//    public String getESharePinCode(String ip) {
//        Object obj = get(PINCODE + ip);
//        if (Validator.isEmpty(obj)) {
//            return "";
//        } else {
//            return (String) obj;
//        }
//    }

    public void setMoreBtnVisible(boolean visible) {
        put(MORE_VISIBLE, visible);
    }

    public boolean getMoreBtnVisible() {
        Object obj = get(MORE_VISIBLE);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setBackBtnVisible(boolean visible) {
        put(BACK_VISIBLE, visible);
    }

    public boolean getBackBtnVisible() {
        Object obj = get(BACK_VISIBLE);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

}
