package com.czur.czurwma.preferences;

import android.content.Context;

import com.czur.czurwma.utils.Validator;


public class FirstPreferences extends BasePreferences {

    private static final String PREF = FirstPreferences.class.getSimpleName();

    //首次进入APP，展示隐私政策弹窗
    private static final String IS_FIRST_ENTER_APP = "is_first_enter_app";

    // debug release区分字符串
    private static final String DEBUG_RELEASE_TYPE = "debug_release_type";

    private static FirstPreferences instance;

    public static FirstPreferences getInstance(Context context) {
        if (instance == null) {
            instance = new FirstPreferences(context, PREF);
        }
        return instance;
    }

    public FirstPreferences(Context context, String prefsName) {
        super(context, prefsName);
    }

    //debug release区分字符串
    public void setDebugReleaseType(String typeName) {
        put(DEBUG_RELEASE_TYPE, typeName);
    }

    //debug release区分字符串
    public String getDebugReleaseType() {
        Object obj = get(DEBUG_RELEASE_TYPE);
        if (Validator.isNotEmpty(obj)) {
            return (String) obj;
        }
        return "";
    }

    //首次进入APP
    public void setIsFirstEnterApp(boolean notFirst) {
        put(IS_FIRST_ENTER_APP, notFirst);
    }

    //首次进入APP
    public boolean isFirstEnterApp() {
        Object obj = get(IS_FIRST_ENTER_APP);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }


}
