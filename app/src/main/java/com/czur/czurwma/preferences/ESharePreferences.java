package com.czur.czurwma.preferences;

import android.app.Application;
import android.content.Context;

import com.czur.czurutils.log.CZURLogUtilsKt;
import com.czur.czurwma.utils.Validator;
import com.eshare.api.bean.Device;

import org.jetbrains.annotations.Nullable;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

public class ESharePreferences extends BasePreferences {


    private static final String ENCRYPT_DEVICE_IP = "deviceIp";
    private static final String ENCRYPT_LOCKED_TIME = "lockedTime";
    private static final String ENCRYPT_WRONG_TIMES = "pwdWrongTimes";
    private static final String ENCRYPT_PWD = "pwd";
    private static final ArrayList<String> keyList = new ArrayList<>(Arrays.asList(ENCRYPT_LOCKED_TIME, ENCRYPT_WRONG_TIMES, ENCRYPT_PWD));// 还有一个deviceIp 没添加到list中,因为是固定要添加
    private static final String PREF = ESharePreferences.class.getSimpleName();
    //基础变量
    private static final String PINCODE = "czur_eshare_pincode";
    private static final String PINCODEMAP = "czur_eshare_pincode_map";
    private static final String MORE_VISIBLE = "czur_eshare_more_visible";
    private static final String BACK_VISIBLE = "czur_eshare_back_visible";
    private static final String ENCRYPT_LOCK_INFO = "czur_eshare_lock_info";

    private static final String TRANSFER_FILE_CODE = "TRANSFER_FILE_CODE";
    private static final String STARRY_API_VERSION = "STARRY_API_VERSION";
    private static final String UUID = "UUID";
    private static final String TAG = "ESharePreferences";

    private static ESharePreferences instance;

    public static void init(Application application) {
        instance = new ESharePreferences(application, PREF);
    }

    public static ESharePreferences getInstance() {
        return instance;
    }

    public static ESharePreferences getInstance(Context context) {
        if (instance == null) {
            instance = new ESharePreferences(context, PREF);
        }
        return instance;
    }

    public ESharePreferences(Context context, String prefsName) {
        super(context, prefsName);
    }


    // EShare save pinCode
    public void setESharePinCode(String pincode) {
        put(PINCODE, pincode);
    }

    public String getESharePinCode() {
        Object obj = get(PINCODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    // EShare save pinCode
    public void setUUID(String pincode) {
        put(UUID, pincode);
    }

    public String getUUID() {
        Object obj = get(UUID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    // 文件传输验证码
    public void setTransmitFileCode(String code) {
        put(TRANSFER_FILE_CODE, code);
    }

    public String getTransmitFileCode() {
        Object obj = get(TRANSFER_FILE_CODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    //绑定ip
    public void setESharePinCode(String ip, String pincode) {
        put(PINCODE + ip, pincode);
    }

    public String getESharePinCode(String ip) {
        Object obj = get(PINCODE + ip);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setMoreBtnVisible(boolean visible) {
        put(MORE_VISIBLE, visible);
    }

    public boolean getMoreBtnVisible() {
        Object obj = get(MORE_VISIBLE);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setBackBtnVisible(boolean visible) {
        put(BACK_VISIBLE, visible);
    }

    public boolean getBackBtnVisible() {
        Object obj = get(BACK_VISIBLE);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setPwdWrongTimes(Device device, int wrongTimes) {
        refreshEncryptJson(device, ENCRYPT_WRONG_TIMES, String.valueOf(wrongTimes));
    }


    //密码错误次数
    public String getPwdWrongTimes(Device device) {
        return getEncryptJsonValue(device, ENCRYPT_WRONG_TIMES);
    }

    // 设置加密锁定时间
    public void setEncryptLockedTime(Device device, long lockedTime) {
        refreshEncryptJson(device, ENCRYPT_LOCKED_TIME, String.valueOf(lockedTime));
    }

    // 加密锁定时间
    public String getEncryptLockedTime(Device device) {
        return getEncryptJsonValue(device, ENCRYPT_LOCKED_TIME);
    }

    public String getEncryptJsonValue(Device device, String key) {
        Object obj = get(ENCRYPT_LOCK_INFO);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            try {
                JSONArray jsonArray = new JSONArray((String) obj);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    if (jsonObject.getString(ENCRYPT_DEVICE_IP).equals(device.getIpAddress())) {
                        return jsonObject.getString(key);
                    }
                }
                return "";
            } catch (JSONException e) {
                CZURLogUtilsKt.logTagE(TAG, e.getMessage());
                return "";
            }

        }
    }

    public void refreshEncryptJson(Device device, String key, String value) {
        Object obj = get(ENCRYPT_LOCK_INFO);

        JSONArray jsonArray = new JSONArray();

        if (Validator.isEmpty(obj)) {
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject()
                        .put(ENCRYPT_DEVICE_IP, device.getIpAddress());

                for (int i = 0; i < keyList.size(); i++) {
                    String keyy = keyList.get(i);
                    if (Objects.equals(keyy, key)) {
                        jsonObject.put(keyy, value);
                    } else {
                        jsonObject.put(keyy, "");
                    }
                }
                jsonArray.put(jsonObject);
                put(ENCRYPT_LOCK_INFO, jsonArray.toString());
            } catch (JSONException e) {
                CZURLogUtilsKt.logTagE(TAG, e.getMessage());
            }
        } else {
            try {
                jsonArray = new JSONArray((String) obj);
                JSONObject jsonObject = null;
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject json = jsonArray.getJSONObject(i);

                    if (json.getString(ENCRYPT_DEVICE_IP).equals(device.getIpAddress())) {
                        jsonObject = json;
                        jsonObject.put(key, value);
                        jsonArray.remove(i);
                        break;
                    }
                }
                jsonArray.put(jsonObject);
                put(ENCRYPT_LOCK_INFO, jsonArray.toString());
            } catch (JSONException e) {
                CZURLogUtilsKt.logTagE(TAG, e.getMessage());
            }
        }
    }

    // 设置加密锁定时间
    public void setEncryptPwd(Device device, String pwd) {
        refreshEncryptJson(device, ENCRYPT_PWD, pwd);
    }

    // 加密锁定时间
    public String getEncryptPwd(Device device) {
        return getEncryptJsonValue(device, ENCRYPT_PWD);
    }

    public void setStarryApiVersion(@Nullable String starryApiVersion) {
        put(STARRY_API_VERSION, starryApiVersion);
    }

    public String getStarryApiVersion() {
        Object obj = get(STARRY_API_VERSION);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

}
