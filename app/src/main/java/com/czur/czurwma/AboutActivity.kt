package com.czur.czurwma


import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.*
import com.czur.czurwma.common.EshareConstants
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.utils.downloadapk.UpdateUtil
import com.czur.czurwma.utils.launch
import com.czur.czurwma.utils.singleClick
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


/**
 * 关于页面
 */
class AboutActivity : StarryBaseActivity(), View.OnClickListener {

    private val about_back_btn by lazy {
        findViewById<ImageView>(R.id.about_back_btn)
    }
    private val app_version by lazy {
        findViewById<TextView>(R.id.app_version)
    }

    private val about_update_bg by lazy {
        findViewById<RelativeLayout>(R.id.about_update_bg)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f8f8f8)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_about)
        EventBus.getDefault().register(this)
        initComponent()
        launch {
            UpdateUtil.checkUpdate()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(updateEvent: EventBusEvent) {
        val event = updateEvent.event
        when (event) {
            EshareConstants.APP_UPDATE_HAS_UPDATE_INFO -> {
                about_update_bg.visibility = View.VISIBLE
            }

            EshareConstants.APP_UPDATE_NO_UPDATE_INFO -> {
                about_update_bg.visibility = View.GONE
            }
        }
    }



    private fun initComponent() {

        about_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        app_version.text = AppUtils.getAppVersionName()

        /// 添加条件，判断是否显示
        about_update_bg.visibility = View.GONE

        about_update_bg?.singleClick {
            launch {
                UpdateUtil.checkUpdate(true)
            }
        }
    }

}