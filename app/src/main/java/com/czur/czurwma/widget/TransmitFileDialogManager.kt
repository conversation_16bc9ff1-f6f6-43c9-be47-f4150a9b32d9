package com.czur.czurwma.widget

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.text.InputType
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.core.widget.doOnTextChanged
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.IntentUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.Utils
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.common.EshareConstants.ONE_HOUR
import com.czur.czurwma.common.EshareConstants.ONE_MINUTE
import com.czur.czurwma.common.EshareConstants.ONE_SECOND
import com.czur.czurwma.common.EshareConstants.PWD_ERROR_406
import com.czur.czurwma.common.EshareConstants.TRANSMIT_PWD_LOCKED_TIME
import com.czur.czurwma.eshare.EShareActivity
import com.czur.czurwma.eshare.transmitfile.TransmitFileRootBrowserActivity
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.viewmodel.EShareViewModel
import com.eshare.api.bean.Device
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import kotlin.math.abs

object TransmitFileDialogManager {


    var dialog: CloudCommonDialog? = null
    var needBack : Boolean = false

    fun dismissAllDialog() {
        dialog?.dismiss()
    }

    /**
     * 设备端断开
     * 网络异常，是否重新连接？
     * ————放弃上传 ————重新连接————
     */
    fun showReConnectDialog(
        buttons: CloudCommonPopupConstants,
        selectBtn: (Boolean, DialogInterface) -> Unit
    ) {
        if (dialogIsShowing()) {
            return
        }
        val builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(),
            buttons
        )
        builder.setTitle(
            Utils.getApp().resources.getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(Utils.getApp().resources.getString(R.string.transmit_connect_error))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            selectBtn.invoke(true, dialog)
        })
        builder.setOnNegativeListener { dialog, which ->
            selectBtn.invoke(false, dialog)
        }
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }

    /**
     * 显示空间不足的dialog
     *”会议星剩余空间不足1G，请清理空间后重试“——确认——
     */
    fun showSpaceNotEnoughDialog() {
        if (dialogIsShowing()) {
            return
        }
        val builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(),
            CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().resources.getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(Utils.getApp().resources.getString(R.string.eshare_transmit_file_toast_store_limited))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }

    /**
     *仍有未上传完毕的文件，是否继续上传？
     * 取消上传-- 继续上传
     */
    fun finishUploadActivityDialog(
        btns: CloudCommonPopupConstants,
        res: Int,
        activity: Activity, selectBtn: (Boolean, DialogInterface) -> Unit
    ) {
        if (dialogIsShowing()) {
            return
        }
        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), btns
        )
        builder.setTitle(
            Utils.getApp().resources.getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(Utils.getApp().resources.getString(res))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            selectBtn.invoke(true, dialog)
        })
        builder.setOnNegativeListener { dialog, which ->
            selectBtn.invoke(false, dialog)
        }
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()

    }

    /**
     * 输入传输码dialog
     *
     */
    fun showInputVerifyCodeDialog(
        eShareActivity: EShareActivity,
        verifyContent: String,
        selectFileResultLauncher: ActivityResultLauncher<Intent>,
        type: EShareViewModel.TransmitType
    ) {
        if (dialogIsShowing()) {
            return
        }
        var dialogEdt = EditText(eShareActivity)
        var errorCodeTv = TextView(eShareActivity)
        val builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.EDT_TWO_BUTTON
        ).setTitle(StringUtils.getString(R.string.eshare_dialog_code_tips))
            .setMessage(StringUtils.getString(R.string.eshare_dialog_check_in_starry))
            .setOnPositiveListener { dialog, which ->
                if (dialogEdt.text.toString() == verifyContent) {
                    ESharePreferences.getInstance().transmitFileCode = verifyContent
                    dialog.dismiss()
                    if (type == EShareViewModel.TransmitType.DOWNLOAD_TYPE) {// 跳转到download页面
                        val intent =
                            Intent(eShareActivity, TransmitFileRootBrowserActivity::class.java)
                        eShareActivity.startActivity(intent)
                    } else {
                        CzurFileUtils.openDocumentToPick(selectFileResultLauncher, eShareActivity)
                    }
                } else {
                    errorCodeTv.visibility = View.VISIBLE
                }
            }.setOnNegativeListener { dialog, which ->
                dialog.dismiss()
            }
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()

        dialogEdt = dialog?.window?.findViewById(R.id.edt) as EditText
        errorCodeTv = dialog?.window?.findViewById(R.id.error_code_tv) as TextView
        dialogEdt.inputType = InputType.TYPE_CLASS_NUMBER

        showSoftInputKeyboard(eShareActivity, dialogEdt)
    }

    /**
     * 在eshareAvtivity页面时候网络异常dialog
     *
     * 请开启会议星的传输文件后重试？\n可在会议星>文件>成者妙传目录下开启
     */
    fun showErrorDevicesServerDialog(obj: (() -> Unit)? = null) {
        if (dialogIsShowing()) {
            return
        }
        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().getResources().getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(Utils.getApp().resources.getString(R.string.eshare_transmit_file_dialog_retry_title))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }

    fun showDeleteFileDialog(
        context: Context, result: (Boolean, DialogInterface, Boolean) -> Unit
    ) {
        var noMoreRemind = false
        if (dialogIsShowing()) {
            return
        }
        val builder = CloudCommonDialog.Builder(
            context,
            CloudCommonPopupConstants.TRANSFER_FILE_TWO_CONFIRM_BUTTON
        )
            .setTitle(context.getString(R.string.starry_popupwindow_title))
            .setMessage(context.getString(R.string.transmit_delete_file_make_sure_delete))
            .setNoMoreTips(true, false) {
                noMoreRemind = it
            }
            .setOnNegativeListener { dialog, which ->
                result.invoke(false, dialog, noMoreRemind)
            }.setOnPositiveListener { dialog, which ->
                result.invoke(true, dialog, noMoreRemind)
            }
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()

    }


    fun showConnectWifiFirstDialog(context: Context) {
        if (dialogIsShowing()) {
            return
        }

        dialog =
            CloudCommonDialog.Builder(context, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
                .setTitle(R.string.starry_popupwindow_title)
                .setMessage(context.getString(R.string.eshare_connect_wifi_first))
                .setOnPositiveListener { dialog, which ->
                    dialog?.dismiss()
                }
                .create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }

    /**
     * 会议星负载过高时显示的
     * 提示
     *
     * 当前会议星正处于会议中，请稍后重试。
     *
     * -----确定-----
     *
     */
    fun showInMeetingDialog(inTransmitAty: Boolean = true) {
        if (dialogIsShowing()) {
            return
        }

        val textStr = if (inTransmitAty) {
            StringUtils.getString(R.string.starry_in_meeting_in_transmit)
        } else {
            StringUtils.getString(R.string.starry_in_meeting_out_transmit)
        }

        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().getResources().getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(textStr)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }


    /**
     * 会议星版本太低, 不支持传输文件
     */
    fun showLowStarryHubDialog(context: Context) {
        if (dialogIsShowing()) {
            return
        }

        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().getResources().getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(context.getString(R.string.transmit_low_starryhub_tips))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()
    }


    /**
     * 文件加密--会议星改密码了
     */
    fun showEncryptStarryChangePwdDialog(needBackToRoot : Boolean = false) {
        this.needBack = needBackToRoot
        if (dialogIsShowing()) {
            return
        }

        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().getResources().getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(
            Utils.getApp().getResources().getString(R.string.encrypt_pwd_change_pwd_error)
        )
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
            if (needBack){
                val intent = Intent(Utils.getApp(), TransmitFileRootBrowserActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                CzurWMAApplication.myApplication?.currentActivity?.startActivity(intent);
            }
            needBack = false
        }
        dialog?.show()
    }

    /**
     * 已锁定加密密码
     */
    fun showEncryptLockedDialog(device: Device?, context: Context) {
        if (dialogIsShowing()) {
            return
        }
        var timeStr = ""
        val encryptLocked =
            ESharePreferences.getInstance().getEncryptLockedTime(device)
                .toLong() + TRANSMIT_PWD_LOCKED_TIME
        val time = encryptLocked - System.currentTimeMillis()
        //小时按小时向上取整，低于59分钟时按分钟取整。如1小时1分，则显示2小时;如59:39显示为1小时，59:00显示为59;低于60s时，显示为1分钟--于洋
        if (time <= TRANSMIT_PWD_LOCKED_TIME) {
            val hour = (time / (ONE_HOUR))
            val minute = ((time - hour * ONE_HOUR) / (ONE_MINUTE))
            val second =
                ((time - hour * ONE_HOUR - minute * ONE_MINUTE) / ONE_SECOND)
            when {
                hour > 0 -> {
                    timeStr = if (minute > 0 || second > 0) {
                        context.getString(R.string.encrypt_pwd_hours, (hour + 1).toString())
                    } else {
                        if (hour == 1L){
                            context.getString(R.string.encrypt_pwd_hour, hour.toString())
                        }else{
                            context.getString(R.string.encrypt_pwd_hours, hour.toString())
                        }
                    }
                }
                minute > 0 -> {
                    timeStr = if (second > 0) {
                        val minutes = minute + 1
                        if (minutes == 60L) {
                            context.getString(R.string.encrypt_pwd_hour, 1.toString())
                        } else {
                            context.getString(R.string.encrypt_pwd_mins, minutes.toString())
                        }
                    } else {
                        if (minute == 1L){
                            context.getString(R.string.encrypt_pwd_min, minute.toString())
                        }else{
                            context.getString(R.string.encrypt_pwd_mins, minute.toString())
                        }
                    }
                }
                second > 0 -> {
                    timeStr = if (second == 1L){
                        context.getString(R.string.encrypt_pwd_sec, minute.toString())
                    }else{
                        context.getString(R.string.encrypt_pwd_secs, minute.toString())
                    }
                }
            }
        }

        val builder: CloudCommonDialog.Builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(
            Utils.getApp().getResources().getString(R.string.starry_popupwindow_title)
        )
        builder.setMessage(context.getString(R.string.encrypt_pwd_locked, timeStr))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
        })
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }

        val dialogEdt = dialog?.window?.findViewById(R.id.message) as TextView
        dialogEdt.setTextColor(context.resources.getColor(R.color.red_d54146))
        dialog?.show()
    }

    /**
     * 输入加密密码
     */
    fun showEncryptPwdDialog(
        context: Context, obj: ((String) -> Unit)? = null
    ) {
        if (dialogIsShowing()) {
            return
        }
        var posClickable = false
        var dialogEdt = EditText(context)
        val builder = CloudCommonDialog.Builder(
            ActivityUtils.getTopActivity(), CloudCommonPopupConstants.EDT_TWO_BUTTON
        ).setTitle(StringUtils.getString(R.string.encrypt_pwd))
//            .setMessage(StringUtils.getString(R.string.eshare_dialog_check_in_starry))
            .setOnPositiveListener { dialog, which ->
                if (posClickable) {
                    dialog.dismiss()
                    obj?.invoke(dialogEdt.text.toString())
                }
            }.setOnNegativeListener { dialog, which ->
                dialog.dismiss()
            }
        dialog = builder.create()
        dialog?.setOnDismissListener {
            dialog = null
        }
        dialog?.show()

        dialogEdt = dialog?.window?.findViewById<EditText>(R.id.edt)!!
        val buttonPositive = dialog?.window?.findViewById<TextView>(R.id.positive_button)!!
        dialogEdt.inputType = InputType.TYPE_CLASS_NUMBER
        buttonPositive.setBackgroundResource(R.drawable.eshare_circle_color_blue_5dp_30)
        dialogEdt.doOnTextChanged { text, start, before, count ->
            if (text?.length!! > 6) {
                dialogEdt.text.delete(6, text.length)
                dialogEdt.setSelection(6) //将光标移动到文本末尾
            }

            if (text.length >= 6) {
                buttonPositive.setBackgroundResource(R.drawable.eshare_circle_color_blue_5dp)
                posClickable = true
            } else {
                buttonPositive.setBackgroundResource(R.drawable.eshare_circle_color_blue_5dp_30)
                posClickable = false
            }
        }

        showSoftInputKeyboard(context, dialogEdt)
    }

    private fun dialogIsShowing(): Boolean {
        return dialog != null && dialog?.isShowing == true
    }

    fun showSoftInputKeyboard(context: Context, editText: EditText) {
        CoroutineScope(Dispatchers.IO).launch {
            delay(100)
            withContext(Dispatchers.Main) {
                editText.requestFocus()
            }
            delay(100)
            withContext(Dispatchers.Main) {

                val imm: InputMethodManager =
                    context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.toggleSoftInput(
                    InputMethodManager.SHOW_IMPLICIT,
                    InputMethodManager.HIDE_NOT_ALWAYS
                )
            }
        }
    }
}



