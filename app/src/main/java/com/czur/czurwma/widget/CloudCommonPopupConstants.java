package com.czur.czurwma.widget;


import com.czur.czurwma.R;

/**
 * Created by <PERSON>z on 2018/3/10
 * Email：<EMAIL>
 */

public enum CloudCommonPopupConstants {
    COMMON_ONE_BUTTON(-1, -1, R.string.OK_3, -1, -1),
    OK_ONE_BUTTON(-1, -1, R.string.ok_1, -1, -1),
    CONFIRM_ONE_BUTTON(-1, -1, R.string.agree, -1, -1),
    KNOWN_ONE_BUTTON(-1, -1, R.string.handwriting_confirm_text, -1, -1),
    COMMON_TWO_BUTTON(-1, -1, R.string.OK_3, R.string.cancel, -1),
    COMMON_TWO_QUIT_BUTTON1(-1, -1, R.string.update_apk_go_update, R.string.update_apk_quit, -1),
    COMMON_TWO_QUIT_BUTTON2(-1, -1, R.string.update_apk_go_update, R.string.cancel, -1),
    COMMON_TWO_BUTTON2(-1, -1, R.string.OK_3, R.string.cancel, -1),
    TRANSFER_FILE_TWO_CONFIRM_BUTTON(-1, -1, R.string.OK_3, R.string.cancel, -1),
    COMMON_TWO_BUTTON_YES_NO(-1, -1, R.string.starry_common_dialog_yes, R.string.starry_common_dialog_not, -1),
    GPS_TWO_BUTTON(-1, -1, R.string.OK_3, R.string.cancel, -1),
    NOTIFY_TWO_BUTTON(-1, -1, R.string.go_setting, R.string.cancel, -1),
    EDT_TWO_BUTTON(-1, -1, R.string.OK_3, R.string.cancel, 1),
    UPLOAD_FILE_TWO_BUTTON(-1, -1, R.string.transmit_resume_upload_btn, R.string.transmit_cancel_upload_btn, -1),
    DOWNLOAD_FILE_TWO_BUTTON(-1, -1, R.string.transmit_resume_download_btn, R.string.transmit_cancel_download_btn, -1),

    UPLOAD_FILE_ERROR_NET(-1, -1, R.string.transmit_file_reconnect, R.string.transmit_file_cancel_upload, -1),
    DOWNLOAD_FILE_ERROR_NET(-1, -1, R.string.transmit_file_reconnect, R.string.transmit_file_cancel_download, -1);

    private final int editText;
    private final int title;
    private final int message;
    private final int positiveBtn;
    private final int negativeBtn;

    CloudCommonPopupConstants(int title, int message, int positiveBtn, int negativeBtn, int editText) {
        this.editText = editText;
        this.title = title;
        this.message = message;
        this.positiveBtn = positiveBtn;
        this.negativeBtn = negativeBtn;
    }

    public int getMessage() {
        return message;
    }

    public int getPositiveBtn() {
        return positiveBtn;
    }

    public int getNegativeBtn() {
        return negativeBtn;
    }

    public int getEditText() {
        return editText;
    }

    public int getTitle() {
        return title;
    }
}
