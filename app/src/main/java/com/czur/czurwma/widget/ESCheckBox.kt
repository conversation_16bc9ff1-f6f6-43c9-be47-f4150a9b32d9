package com.czur.czurwma.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.res.ResourcesCompat
import com.czur.czurwma.R

class ESCheckBox : AppCompatImageView, View.OnClickListener {
    private var isChecked = false
    private var checkedImageResource = 0
    private var uncheckedImageResource = 0
    private var defaultCheckStatus = false
    private var listener: OnCheckedChangeListener? = null

    constructor(context: Context?) : super(context!!) {
        init(null)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(
        context!!, attrs
    ) {
        init(attrs)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context!!, attrs, defStyleAttr
    ) {
        init(attrs)
    }

    private fun init(attrs: AttributeSet?) {
        isClickable = true
        setOnClickListener(this)
        if (attrs != null) {
            val a = context.obtainStyledAttributes(attrs, R.styleable.ESCheckBox)
            checkedImageResource = a.getResourceId(R.styleable.ESCheckBox_checkedImage, 0)
            uncheckedImageResource = a.getResourceId(R.styleable.ESCheckBox_uncheckedImage, 0)
            defaultCheckStatus = a.getBoolean(R.styleable.ESCheckBox_defaultCheckStatus, false)
            a.recycle()
            isChecked = defaultCheckStatus
            updateImage()
        }
    }

    override fun onClick(v: View) {
        isChecked = !isChecked
        updateImage()
        if (listener != null) {
            listener!!.onCheckedChanged(this, isChecked)
        }
    }

    fun isChecked(): Boolean {
        return isChecked
    }

    fun setChecked(checked: Boolean) {
        isChecked = checked
        updateImage()
    }

    fun setCheckedImageResource(resourceId: Int) {
        checkedImageResource = resourceId
        updateImage()
    }

    fun setUncheckedImageResource(resourceId: Int) {
        uncheckedImageResource = resourceId
        updateImage()
    }

    fun setOnCheckedChangeListener(listener: OnCheckedChangeListener?) {
        this.listener = listener
    }

    private fun updateImage() {
        background = if (isChecked) {
            ResourcesCompat.getDrawable(resources, checkedImageResource, null)
        } else {
            ResourcesCompat.getDrawable(resources, uncheckedImageResource, null)
        }
    }

    interface OnCheckedChangeListener {
        fun onCheckedChanged(view: ESCheckBox?, isChecked: Boolean)
    }
}