package com.czur.czurwma.widget

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.utils.Tools
import com.czur.czurwma.utils.addNoMinusFilter
import com.czur.czurwma.R

/**
 * 自定义选择弹框
 */
class MyInputDialog(context: Context) : AlertDialog(context), View.OnClickListener {

    var call: clickCallBack? = null
    var callNo: clickCallBack? = null
    private var TextView_title: TextView? = null
    private var TextView_content: TextView? = null
    var editText: EditText? = null
    private var yesButton: View? = null
    var noButton: View? = null

    constructor(
        context: Context, title: String, ipAddress: String, content: String,
        yesCallBack: clickCallBack
    ) : this(context) {
        call = yesCallBack
        TextView_title?.text = title
        TextView_content?.text = title

        // 上次正确的投屏码
        val pincode = ESharePreferences.getInstance().getESharePinCode(ipAddress)
        if (pincode.isNotEmpty() && pincode.length == 8) {
            editText?.setText(pincode)
        }
        val filters = arrayOf<InputFilter>(InputFilter.LengthFilter(10)) // 最大输入长度
//        editText?.filters = filters
        editText?.addNoMinusFilter()
        editText?.isFocusable = true
        editText?.isFocusableInTouchMode = true
        editText?.requestFocus()
        this.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)

    }

    init {
        val inflate =
            LayoutInflater.from(context).inflate(R.layout.eshare_common_custom_input_dialog, null);
        setView(inflate)
        //设置点击别的区域不关闭页面
        setCancelable(false)

        TextView_title = inflate.findViewById<TextView>(R.id.title)
        TextView_content = inflate.findViewById<TextView>(R.id.message)
        editText = inflate.findViewById<EditText>(R.id.eshare_pincode)
        yesButton = inflate.findViewById<View>(R.id.button_yes)
        noButton = inflate.findViewById<View>(R.id.button_no)

//        yesButton?.setOnClickListener(this)
        yesButton?.setOnClickListener {
            call?.yesClick(this)
        }
        noButton?.setOnClickListener {
            call?.noClick(this)
        }

        yesButton?.setOnClickListener(null)
        yesButton?.setBackgroundResource(R.drawable.eshare_input_ok_60_bg)

        editText?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable?) {
                selectionStart = editText?.selectionStart ?: 0
                selectionEnd = editText?.selectionEnd ?: 0
                val str = temp.toString().replace(" ", "")
                if (!TextUtils.isEmpty(str)) {

                    val l = Tools.getTextLength(str)
                    if (l > 8) {
                        s?.delete(selectionStart - 1, selectionEnd)
                        val tempSelection = selectionEnd
                        editText?.text = s
                        editText?.setSelection(tempSelection)
                    }

                    var sr: String = s.toString()
                    if (sr != sr.toUpperCase() || sr.contains(" ")) {
                        sr = sr.replace(" ", "")
                        sr = sr.toUpperCase()
                        editText?.setText(sr)
                        editText?.setSelection(editText!!.text.length)
                    }
                    yesButton?.setBackgroundResource(R.drawable.eshare_input_ok_bg)
                    yesButton?.setOnClickListener {
                        call?.yesClick(this@MyInputDialog)
                    }
                }else{
                    yesButton?.setOnClickListener(null)
                    yesButton?.setBackgroundResource(R.drawable.eshare_input_ok_60_bg)
                }
            }
        })

        // 上次正确的投屏码
        /*  val pincode = UserPreferences.getInstance().getESharePinCode(ipAddress)
          if (pincode.isNotEmpty() && pincode.length == 8){
              editText?.setText(pincode)
          }*/
    }


    override fun onClick(p0: View?) {
        call?.yesClick(this)

        call?.noClick(this)
    }

    interface clickCallBack {

        fun yesClick(dialog: MyInputDialog)

        fun noClick(dialog: MyInputDialog)
    }


}
