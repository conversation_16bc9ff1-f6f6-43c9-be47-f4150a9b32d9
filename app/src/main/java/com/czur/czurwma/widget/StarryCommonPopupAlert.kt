package com.czur.czurwma.widget

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.StringUtils
import com.czur.czurwma.R

/**
 * 通用弹出dialog
 */
class StarryCommonPopupAlert : Dialog {
    val isChecked = false

    constructor(context: Context?, theme: Int) : super(context!!, theme) {}

    class Builder(private val context: Context) {
        private var message: String? = null
        private var title: String? = null
        private var btnPositiveTitle: String? = null
        private var contentsView: View? = null
        private var boldMsgStyle = false
        private var positiveListener: DialogInterface.OnClickListener? = null
        private var onDismissListener: DialogInterface.OnDismissListener? = null
        private var onKeyBackListener: DialogInterface.OnKeyListener? = null
        fun setMessage(message: String?): Builder {
            this.message = message
            return this
        }

        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        fun setPositiveTitle(title: String?): Builder {
            this.btnPositiveTitle = title
            return this
        }

        /**
         * msg的文字是否加粗
         */
        fun setBoldMsgStyle(boldMsgStyle: Boolean): Builder {
            this.boldMsgStyle = boldMsgStyle
            return this
        }

        fun setContentsView(contentsView: View?): Builder {
            this.contentsView = contentsView
            return this
        }

        fun setContentsView(resource: Int): Builder {
            contentsView = LayoutInflater.from(context).inflate(resource, null)
            return this
        }

        fun setOnPositiveListener(positiveListener: DialogInterface.OnClickListener?): Builder {
            this.positiveListener = positiveListener
            return this
        }

        fun setOnKeyBackListener(positiveListener: DialogInterface.OnKeyListener?): Builder {
            this.onKeyBackListener = positiveListener
            return this
        }

        fun setOnDismissListener(onDismissListener: DialogInterface.OnDismissListener?): Builder {
            this.onDismissListener = onDismissListener
            return this
        }

        fun create(): StarryCommonPopupAlert {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val dialog = StarryCommonPopupAlert(context, R.style.TransparentProgressDialog)
            val layout = commonCustomPopLayout(inflater, dialog)
            dialog.setContentView(layout)
            dialog.setCanceledOnTouchOutside(false)
            val params = dialog.window?.attributes
            params?.dimAmount = DIMMED_OPACITY
            return dialog
        }

        private fun commonCustomPopLayout(
            inflater: LayoutInflater,
            dialog: StarryCommonPopupAlert
        ): View {
            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            val layout = inflater.inflate(R.layout.starry_common_custom_popup_alert, null, false)
            val params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            dialog.addContentView(layout, params)
            val title = layout.findViewById<View>(R.id.title) as TextView
            val messageTv = layout.findViewById(R.id.message) as MediumBoldTextView
            val positiveBtn = layout.findViewById<View>(R.id.positive_button) as TextView
            messageTv.isBoldStyle = boldMsgStyle
            if (!StringUtils.isEmpty(this.message)) {
                messageTv.text = this.message + ""
            }
            if (!StringUtils.isEmpty(this.title)) {
                title.text = this.title + ""
            }
            if (!StringUtils.isEmpty(this.btnPositiveTitle)) {
                positiveBtn.text = this.btnPositiveTitle + ""
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener {
                    positiveListener?.onClick(
                        dialog,0
                    )
                }
            } else {
                positiveBtn.setOnClickListener { dialog.dismiss() }
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener)
            }

            if (onKeyBackListener != null) {
                dialog.setOnKeyListener(onKeyBackListener)
            }

            return layout
        }
    }

    companion object {
        const val DIMMED_OPACITY = 0.2f
    }
}