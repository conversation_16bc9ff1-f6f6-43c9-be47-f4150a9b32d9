package com.czur.czurwma.widget;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.fragment.app.DialogFragment;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.czurwma.R;

public class NoNetworkDialogFragment extends DialogFragment {
    public static final String TAG = "NoNetworkDialogFragment";
    private static final String KEY_TAG = "key_tag";
    private Dialog dialog = null;
    private boolean cancelable;

    private OnCancelListener cancelListener;
    private String tag;

    /**
     * @return
     */
    public static NoNetworkDialogFragment newInstance(String tag) {
        NoNetworkDialogFragment dialog = new NoNetworkDialogFragment();
        Bundle args = new Bundle();
        args.putString(KEY_TAG, tag);
        dialog.setArguments(args);
        return dialog;
    }

    /**
     * @param savedInstanceState
     * @return
     * @see DialogFragment#onCreateDialog(Bundle)
     */
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        if (getArguments() != null) {
            tag = getArguments().getString(KEY_TAG);
        }
        dialog = createLoadingDialog(getActivity());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        }
        dialog.setCancelable(cancelable);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    if (cancelListener != null) {
                        cancelListener.onCancel(dialog, tag);
                    }
                    return true;
                }
                return false;
            }
        });

        dialog.findViewById(R.id.tv_click_return).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                ActivityUtils.finishToActivity(AuraMateActivity.class, false);
            }
        });
        return dialog;
    }

    /**
     * @param cancelable
     * @see DialogFragment#setCancelable(boolean)
     */
    @Override
    public void setCancelable(boolean cancelable) {
        super.setCancelable(cancelable);
        this.cancelable = cancelable;
    }

    /**
     * @param cancelListener
     */
    public void setOnCancelListener(OnCancelListener cancelListener) {
        this.cancelListener = cancelListener;
    }

    /**
     * @see DialogFragment#dismiss()
     */
    @Override
    public void dismiss() {
        super.dismiss();
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    public interface OnCancelListener {
        void onCancel(DialogInterface dialog, String tag);
    }

    private Dialog createLoadingDialog(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_no_network, null);
        Animation bgAnim = AnimationUtils.loadAnimation(context, R.anim.dialog_fade_in);
        view.startAnimation(bgAnim);
        // 创建自定义样式dialog
        Dialog loadingDialog = new Dialog(context, R.style.TransparentProgressDialog);
        loadingDialog.setContentView(view);
        Window window = loadingDialog.getWindow();
        //获取对话框当前的参数值
        WindowManager.LayoutParams params = loadingDialog.getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.MATCH_PARENT;
        //参数为0到1之间。0表示完全透明，1就是不透明。按需求调整参数
        params.alpha = 1f;
        if (window != null) {
            window.setStatusBarColor(getResources().getColor(R.color.white));
            BarUtils.setStatusBarLightMode(window, true);
            window.setAttributes(params);
        }
        return loadingDialog;
    }
}

