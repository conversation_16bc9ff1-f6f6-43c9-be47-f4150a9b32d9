package com.czur.czurwma.livedatabus

import android.os.Build
import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logW
import java.util.concurrent.ConcurrentHashMap

/**
 * Android 16兼容的LiveDataBus实现
 * 避免使用反射访问LiveData内部字段
 */
class Android16LiveDataBus private constructor() {

    companion object {
        @JvmStatic
        fun get(): Android16LiveDataBus {
            return Holder.BUS
        }
    }

    private object Holder {
        val BUS = Android16LiveDataBus()
    }

    private val bus = ConcurrentHashMap<String, BusLiveData<Any>>()

    @Synchronized
    fun <T> with(key: String, type: Class<T>): BusLiveData<T> {
        if (!bus.containsKey(key)) {
            bus[key] = BusLiveData(key)
        }
        @Suppress("UNCHECKED_CAST")
        return bus[key] as BusLiveData<T>
    }

    inline fun <reified T> with(key: String): BusLiveData<T> {
        return with(key, T::class.java)
    }

    /**
     * Android 16兼容的BusLiveData实现
     */
    class BusLiveData<T>(private val key: String) : MutableLiveData<T>() {

        private val observerMap = ConcurrentHashMap<Observer<in T>, ObserverWrapper<T>>()

        @MainThread
        override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
            if (Build.VERSION.SDK_INT >= 36) { // Android 16
                // Android 16+ 使用新的安全实现
                observeForAndroid16(owner, observer)
            } else {
                // 低版本使用原有实现
                observeWithReflection(owner, observer)
            }
        }

        @MainThread
        override fun observeForever(observer: Observer<in T>) {
            if (Build.VERSION.SDK_INT >= 36) { // Android 16
                // Android 16+ 使用新的安全实现
                observeForeverForAndroid16(observer)
            } else {
                // 低版本使用原有实现
                observeForeverWithReflection(observer)
            }
        }

        @MainThread
        override fun removeObserver(observer: Observer<in T>) {
            val realObserver = observerMap.remove(observer) ?: observer
            super.removeObserver(realObserver)
        }

        /**
         * Android 16+ 的安全观察实现
         * 不使用反射，通过包装器控制初始值发送
         */
        private fun observeForAndroid16(owner: LifecycleOwner, observer: Observer<in T>) {
            val wrapper = ObserverWrapper(observer, false)
            observerMap[observer] = wrapper
            super.observe(owner, wrapper)
        }

        /**
         * Android 16+ 的安全永久观察实现
         */
        private fun observeForeverForAndroid16(observer: Observer<in T>) {
            val wrapper = ObserverWrapper(observer, false)
            observerMap[observer] = wrapper
            super.observeForever(wrapper)
        }

        /**
         * 低版本使用反射的实现（保持向后兼容）
         */
        private fun observeWithReflection(owner: LifecycleOwner, observer: Observer<in T>) {
            try {
                val wrapper = ObserverWrapper(observer, true)
                observerMap[observer] = wrapper
                super.observe(owner, wrapper)
                hook(wrapper)
            } catch (e: Exception) {
                logE("Android16LiveDataBus", "observeWithReflection failed: ${e.message}")
                // 降级到普通观察
                super.observe(owner, observer)
            }
        }

        /**
         * 低版本使用反射的永久观察实现
         */
        private fun observeForeverWithReflection(observer: Observer<in T>) {
            try {
                val wrapper = ObserverWrapper(observer, true)
                observerMap[observer] = wrapper
                super.observeForever(wrapper)
                hook(wrapper)
            } catch (e: Exception) {
                logE("Android16LiveDataBus", "observeForeverWithReflection failed: ${e.message}")
                // 降级到普通观察
                super.observeForever(observer)
            }
        }

        /**
         * 反射hook方法（仅用于低版本）
         */
        private fun hook(observer: ObserverWrapper<T>) {
            try {
                // 获取LiveData的mObservers字段
                val classLiveData = LiveData::class.java
                val fieldObservers = classLiveData.getDeclaredField("mObservers")
                fieldObservers.isAccessible = true
                val objectObservers = fieldObservers.get(this)
                
                val classObservers = objectObservers.javaClass
                val methodGet = classObservers.getDeclaredMethod("get", Object::class.java)
                methodGet.isAccessible = true
                val objectWrapperEntry = methodGet.invoke(objectObservers, observer)
                
                var objectWrapper: Any? = null
                if (objectWrapperEntry is Map.Entry<*, *>) {
                    objectWrapper = objectWrapperEntry.value
                }
                
                if (objectWrapper == null) {
                    logW("Android16LiveDataBus", "Wrapper is null, skip hook")
                    return
                }
                
                val classObserverWrapper = objectWrapper.javaClass.superclass
                val fieldLastVersion = classObserverWrapper?.getDeclaredField("mLastVersion")
                fieldLastVersion?.isAccessible = true
                
                // 获取LiveData的版本
                val fieldVersion = classLiveData.getDeclaredField("mVersion")
                fieldVersion.isAccessible = true
                val objectVersion = fieldVersion.get(this)
                
                // 设置wrapper的版本
                fieldLastVersion?.set(objectWrapper, objectVersion)
                
            } catch (e: Exception) {
                logE("Android16LiveDataBus", "hook failed: ${e.message}")
            }
        }

        /**
         * 发送数据
         */
        fun post(value: T) {
            setValue(value)
        }

        /**
         * 在后台线程发送数据
         */
        fun postValue(value: T) {
            super.postValue(value)
        }
    }

    /**
     * Observer包装器
     */
    private class ObserverWrapper<T>(
        private val observer: Observer<in T>,
        private val needsHook: Boolean
    ) : Observer<T> {
        
        private var hasReceived = false

        override fun onChanged(value: T) {
            if (needsHook) {
                // 低版本通过hook控制
                observer.onChanged(value)
            } else {
                // Android 16+ 通过包装器控制初始值
                if (!hasReceived) {
                    hasReceived = true
                    // 跳过初始值，只接收新值
                    return
                }
                observer.onChanged(value)
            }
        }
    }
}

/**
 * 扩展函数，方便使用
 */
inline fun <reified T> String.toLiveDataBus(): Android16LiveDataBus.BusLiveData<T> {
    return Android16LiveDataBus.get().with(this)
}

/**
 * 发送数据的扩展函数
 */
fun <T> String.postLiveDataBus(value: T) {
    Android16LiveDataBus.get().with<T>(this).post(value)
}
