package com.czur.czurwma

import android.annotation.SuppressLint
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import com.czur.czurutils.log.logI
import com.czur.czurwma.preferences.FirstPreferences
import com.czur.czurwma.utils.initializeInit
import com.czur.czurwma.widget.CloudCommonPopupConstants
import com.czur.czurwma.widget.PrivacyPopup
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@SuppressLint("CustomSplashScreen")
class SplashActivity : Activity() {
    var firstPreferences: FirstPreferences? = null
    var privacyPopup: PrivacyPopup? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.splash_activity)
        firstPreferences = FirstPreferences.getInstance(this)

        //首次进入需要显示隐私政策对话框
        if (firstPreferences?.isFirstEnterApp == true) {
            showPrivacyDialog()
        } else {
            init()
        }


    }

    private fun init() {
        CoroutineScope(Dispatchers.Main).launch {
            initializeInit(application)
            delay(1300)
            startActivity(Intent(this@SplashActivity, MainActivity::class.java))
            finish()

        }
    }


    //首次安装仅调用一次
    private fun showPrivacyDialog() {
        val builder: PrivacyPopup.Builder =
            PrivacyPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, which ->
            logI("negative_button click!")
            firstPreferences?.setIsFirstEnterApp(true)
            finish()
            dialog.dismiss()
        })
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            logI("positive_button click!")
            dialog.dismiss()
            firstPreferences?.setIsFirstEnterApp(false)

            init()
        })

        privacyPopup?.dismiss()
        privacyPopup = null

        privacyPopup = builder.create()
        privacyPopup?.show()
    }
}