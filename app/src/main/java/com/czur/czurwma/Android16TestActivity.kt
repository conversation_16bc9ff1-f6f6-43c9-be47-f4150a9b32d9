package com.czur.czurwma

import android.os.Bundle
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.activity.ComponentActivity
import com.czur.czurutils.log.logI
import com.czur.czurwma.utils.Android16AdaptationManager
import com.czur.czurwma.utils.Android16CompatUtils
import com.czur.czurwma.utils.AdaptiveLayoutUtils
import com.czur.czurwma.utils.EdgeToEdgeUtils
import com.czur.czurwma.utils.IntentSecurityUtils
import com.czur.czurwma.utils.PredictiveBackUtils

/**
 * Android 16适配测试Activity
 * 用于验证各项适配功能是否正常工作
 */
class Android16TestActivity : ComponentActivity() {

    private lateinit var resultTextView: TextView
    private lateinit var scrollView: ScrollView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 执行Android 16适配
        Android16AdaptationManager.adaptActivity(this)
        
        setupUI()
        runTests()
    }

    private fun setupUI() {
        setContentView(R.layout.activity_android16_test)
        
        resultTextView = findViewById(R.id.tv_test_result)
        scrollView = findViewById(R.id.scroll_view)
        
        // 应用Edge-to-Edge适配
        EdgeToEdgeUtils.applyEdgeToEdgeForScrollView(scrollView)
        
        // 设置测试按钮
        findViewById<Button>(R.id.btn_run_tests).setOnClickListener {
            runTests()
        }
        
        findViewById<Button>(R.id.btn_generate_report).setOnClickListener {
            generateReport()
        }
        
        findViewById<Button>(R.id.btn_test_reflection).setOnClickListener {
            testReflectionCompat()
        }
        
        findViewById<Button>(R.id.btn_test_intent).setOnClickListener {
            testIntentSecurity()
        }
    }

    private fun runTests() {
        val results = StringBuilder()
        results.appendLine("=== Android 16 适配测试结果 ===\n")
        
        // 1. 基本信息测试
        results.appendLine("1. 基本信息:")
        results.appendLine("   版本信息: ${Android16CompatUtils.getVersionInfo()}")
        results.appendLine("   是否Android 16+: ${Android16AdaptationManager.isAndroid16OrHigher()}")
        results.appendLine("   需要兼容模式: ${Android16CompatUtils.needsCompatMode()}\n")
        
        // 2. Edge-to-Edge测试
        results.appendLine("2. Edge-to-Edge测试:")
        results.appendLine("   已启用: ${EdgeToEdgeUtils.isEdgeToEdgeEnabled(this)}")
        results.appendLine("   状态栏高度: ${EdgeToEdgeUtils.getStatusBarHeight(this)}px")
        results.appendLine("   导航栏高度: ${EdgeToEdgeUtils.getNavigationBarHeight(this)}px\n")
        
        // 3. 预测性返回手势测试
        results.appendLine("3. 预测性返回手势测试:")
        results.appendLine("   支持预测性返回: ${PredictiveBackUtils.isPredictiveBackSupported()}")
        results.appendLine("   默认启用: ${PredictiveBackUtils.isPredictiveBackEnabledByDefault()}")
        results.appendLine("   返回手势信息: ${PredictiveBackUtils.getBackGestureInfo()}\n")
        
        // 4. 自适应布局测试
        results.appendLine("4. 自适应布局测试:")
        val screenInfo = AdaptiveLayoutUtils.getScreenInfo(this)
        results.appendLine("   屏幕信息: $screenInfo")
        results.appendLine("   是否大屏: ${screenInfo.isLargeScreen}")
        results.appendLine("   是否平板: ${screenInfo.isTablet}")
        results.appendLine("   推荐列数: ${AdaptiveLayoutUtils.getRecommendedColumnCount(this)}")
        results.appendLine("   推荐边距: ${AdaptiveLayoutUtils.getRecommendedMargin(this)}dp")
        results.appendLine("   应使用双窗格: ${AdaptiveLayoutUtils.shouldUseTwoPaneLayout(this)}\n")
        
        // 5. Intent安全测试
        results.appendLine("5. Intent安全测试:")
        results.appendLine("   Intent安全信息: ${IntentSecurityUtils.getIntentSecurityInfo()}")
        results.appendLine("   是否Android 16+: ${IntentSecurityUtils.isAndroid16OrHigher()}\n")
        
        // 6. 适配状态检查
        results.appendLine("6. 适配状态检查:")
        val adaptationStatus = Android16AdaptationManager.checkAdaptationStatus(this)
        results.appendLine("   完全适配: ${adaptationStatus.isFullyAdapted}")
        if (adaptationStatus.hasIssues()) {
            results.appendLine("   问题:")
            adaptationStatus.issues.forEach { results.appendLine("     - $it") }
        }
        if (adaptationStatus.hasWarnings()) {
            results.appendLine("   警告:")
            adaptationStatus.warnings.forEach { results.appendLine("     - $it") }
        }
        results.appendLine()
        
        // 7. 运行时检查
        results.appendLine("7. 运行时检查:")
        val runtimeCheck = Android16AdaptationManager.performRuntimeCheck(this)
        results.appendLine("   所有检查通过: ${runtimeCheck.isAllPassed()}")
        runtimeCheck.results.forEach { (check, passed) ->
            val status = if (passed) "✓" else "✗"
            results.appendLine("   $status $check: ${runtimeCheck.details[check]}")
        }
        results.appendLine()
        
        // 8. 适配建议
        val recommendations = Android16AdaptationManager.getAdaptationRecommendations(this)
        if (recommendations.isNotEmpty()) {
            results.appendLine("8. 适配建议:")
            recommendations.forEach { results.appendLine("   - $it") }
            results.appendLine()
        }
        
        results.appendLine("=== 测试完成 ===")
        
        resultTextView.text = results.toString()
        logI("Android16TestActivity", "Test results:\n${results}")
    }

    private fun generateReport() {
        val report = Android16AdaptationManager.generateAdaptationReport(this)
        resultTextView.text = report
        logI("Android16TestActivity", "Generated report:\n$report")
    }

    private fun testReflectionCompat() {
        val results = StringBuilder()
        results.appendLine("=== 反射兼容性测试 ===\n")
        
        try {
            // 测试进程名获取
            val processName = Android16CompatUtils.getProcessName()
            results.appendLine("进程名获取: ${if (processName.isNotEmpty()) "✓" else "✗"}")
            results.appendLine("进程名: $processName\n")
            
            // 测试AppOps检查
            val canCheckOps = Android16CompatUtils.checkOp(this, 0)
            results.appendLine("AppOps检查: ✓")
            results.appendLine("检查结果: $canCheckOps\n")
            
            // 测试获取OPS字段
            val opsFields = Android16CompatUtils.getAllOPSField(this)
            results.appendLine("OPS字段获取: ${if (opsFields.isNotEmpty()) "✓" else "✗"}")
            results.appendLine("字段数量: ${opsFields.size}\n")
            
            results.appendLine("反射兼容性测试通过 ✓")
            
        } catch (e: Exception) {
            results.appendLine("反射兼容性测试失败 ✗")
            results.appendLine("错误: ${e.message}")
        }
        
        resultTextView.text = results.toString()
    }

    private fun testIntentSecurity() {
        val results = StringBuilder()
        results.appendLine("=== Intent安全测试 ===\n")
        
        try {
            // 测试创建安全Intent
            val safeIntent = IntentSecurityUtils.createSafeExplicitIntent(
                this, 
                MainActivity::class.java
            )
            results.appendLine("安全Intent创建: ✓")
            results.appendLine("Action: ${safeIntent.action}")
            results.appendLine("Categories: ${safeIntent.categories}\n")
            
            // 测试Intent验证
            val validation = IntentSecurityUtils.validateIntentSecurity(safeIntent)
            results.appendLine("Intent验证: ${if (validation.isValid) "✓" else "✗"}")
            if (!validation.isValid) {
                validation.issues.forEach { results.appendLine("问题: $it") }
            }
            results.appendLine()
            
            // 测试Intent解析
            val canResolve = IntentSecurityUtils.canResolveIntent(this, safeIntent)
            results.appendLine("Intent解析: ${if (canResolve) "✓" else "✗"}\n")
            
            results.appendLine("Intent安全测试完成")
            
        } catch (e: Exception) {
            results.appendLine("Intent安全测试失败 ✗")
            results.appendLine("错误: ${e.message}")
        }
        
        resultTextView.text = results.toString()
    }
}
