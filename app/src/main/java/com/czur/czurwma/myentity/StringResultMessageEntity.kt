package com.czur.czurwma.myentity

import com.czur.czurwma.myenum.TransmitFileResultEnum

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2023/10/23
 */
class StringResultMessageEntity(
    code: Long = TransmitFileResultEnum.CODE_9527.code,
    byteStart: Long = 0L,
    content: String? = "",
) : ResultMessageEntity<String>(code, byteStart, content)


open class ResultMessageEntity<T>(
    open var code: Long = TransmitFileResultEnum.CODE_9527.code,
    open var byteStart: Long = 0L,
    open var content: T? = null,
)