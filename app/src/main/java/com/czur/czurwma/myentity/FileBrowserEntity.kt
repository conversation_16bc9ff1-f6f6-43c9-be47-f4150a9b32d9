package com.czur.czurwma.myentity

import android.os.Parcel
import android.os.Parcelable
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.myenum.TransmitFileResultEnum


class FileBrowserEntity {
    var code: Long = TransmitFileResultEnum.CODE_9527.code
    var byteStart: Long = 0L
    var content: List<FileEntity>? = null


    data class FileEntity(
        var id : Long = -1L,// 本地数据库的id
        var resultEnumCode : Long = 200L,
        var absPath: String? = "",
        var fileType: String? = "",
        var name: String? = "",
        var lastModifyTime: Long = 0L,

        var extension: String? = "",
        var belongTo: String? = "",
        var fileSize: Long = 0L,
        var absPathWithSuffix: String? = "",

        var parentPath: String? = "",
        var pinyinName: String? = "",
        var dir: Boolean = false,


        var isSelected: Boolean = false,
        var status: UploadFileEnum = UploadFileEnum.DOWNLOADING, // download 下载   done 完成  fail 失败 pause 暂停
        var progress: Float = 0f, // 下载进度百分比
        var localFilePath : String = "",// 下载到本地的文件路径
        var localFileParentPath : String = "",// 下载到本地的文件的父文件路径
        var hasDownloadedChunkFilesSize : String = "0",// 已经下载的分片文件总大小

        var currentChunkDownloadSize : String = "0",// 当前分片文件已经下载的大小
        var currentChunkFileSize : String = "0",// 当前分片文件的总大小
        var currentChunkNumber : String = "0",// 当前分片文件的编号
        var allChunkFilesSize : String = "0",// 所有分片文件的总大小
        var totalChunkNumber : String = "1",// 所有分片文件的总数
        var thumbImageDataKey : String = "",// 缩略图地址


    ) : Parcelable {

        constructor(parcel: Parcel) : this(
            parcel.readLong(),
            parcel.readLong(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readLong(),
            parcel.readString(),
            parcel.readString(),
            parcel.readLong(),
            parcel.readString(),
            parcel.readString(),
            parcel.readString(),
            parcel.readByte() != 0.toByte(),
            parcel.readByte() != 0.toByte(),
            UploadFileEnum.values()[parcel.readInt()],
            parcel.readFloat(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString(),
            parcel.readString().toString()
        )

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeLong(id)
            parcel.writeLong(resultEnumCode)
            parcel.writeString(absPath)
            parcel.writeString(fileType)
            parcel.writeString(name)
            parcel.writeLong(lastModifyTime)
            parcel.writeString(extension)
            parcel.writeString(belongTo)
            parcel.writeLong(fileSize)
            parcel.writeString(absPathWithSuffix)
            parcel.writeString(parentPath)
            parcel.writeString(pinyinName)
            parcel.writeByte(if (dir) 1 else 0)
            parcel.writeByte(if (isSelected) 1 else 0)
            parcel.writeInt(status.ordinal)
            parcel.writeFloat(progress)
            parcel.writeString(localFilePath)
            parcel.writeString(localFileParentPath)
            parcel.writeString(hasDownloadedChunkFilesSize)
            parcel.writeString(currentChunkDownloadSize)
            parcel.writeString(currentChunkFileSize)
            parcel.writeString(currentChunkNumber)
            parcel.writeString(allChunkFilesSize)
            parcel.writeString(totalChunkNumber)
            parcel.writeString(thumbImageDataKey)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<FileEntity> {
            override fun createFromParcel(parcel: Parcel): FileEntity {
                return FileEntity(parcel)
            }

            override fun newArray(size: Int): Array<FileEntity?> {
                return arrayOfNulls(size)
            }
        }

    }
}


enum class BrowserFileType(var typeName: String) {
    ROOT("ROOT"),
    EXCEL("EXCEL"),
    DOC("DOC"),
    PDF("PDF"),
    IMAGE("IMAGE"),
    FOLDER("FOLDER"),
    PPT("PPT"),
    OTHER("OTHER"),
    AUDIO("AUDIO"),
    VIDEO("VIDEO"),
    DOCUMENT("DOCUMENT"),
    ZIP("ZIP"),
    APK("APK"),
    LOAD_INIT("LOAD_INIT"),
    MEET_RECORD("MEET_RECORD")
}