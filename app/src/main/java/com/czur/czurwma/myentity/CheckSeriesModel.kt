package com.czur.czurwma.myentity

//{"code":1000,"message":"success","data":
// [{"seriesId":"79","name":"Windows版本","supportArea":1,"platform":"Windows"},
// {"seriesId":"80","name":"<PERSON>版本","supportArea":1,"platform":"Mac"},
// {"seriesId":"81","name":"iOS版本","supportArea":1,"platform":"iOS"},
// {"seriesId":"82","name":"Android版本","supportArea":1,"platform":"Android"}]}
data class CheckSeriesModel(
    val code: Int,
    val `data`: List<Data>,
    val message: String
)

data class Data(
    val name: String,
    val platform: String,
    val seriesId: String,
    val supportArea: Int
)