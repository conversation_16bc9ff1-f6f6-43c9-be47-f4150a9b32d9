package com.czur.czurwma.myentity

import com.czur.czurwma.myentity.FileBrowserEntity.FileEntity
import com.czur.czurwma.myenum.TransmitFileResultEnum

/**
 * {
 * 	"code": 200,
 * 	"content": {
 * 		"rootGroup": [
 * 			[{
 * 				"rootKey": "Root-Local",
 * 				"needPwd": false
 * 			}, {
 * 				"rootKey": "Root-Download",
 * 				"needPwd": false
 * 			}, {
 * 				"rootKey": "Root-Share",
 * 				"needPwd": false
 * 			}],
 * 			[{
 * 				"rootKey": "Root-Meeting",
 * 				"needPwd": false
 * 			}, {
 * 				"rootKey": "Root-Picture",
 * 				"needPwd": false
 * 			}]
 * 		]
 * 	}
 * }
 */
 class RootFileListEntity {
    var code: Long = TransmitFileResultEnum.CODE_9527.code
    var byteStart: Long = 0L
    var content: String = ""

     data class RootGroupResponse(
         val rootGroup: List<List<RootGroupItem>>
     )

     data class RootGroupItem(
         val rootKey: String,
         val needPwd: Boolean,
         val name: String = ""
     )
}