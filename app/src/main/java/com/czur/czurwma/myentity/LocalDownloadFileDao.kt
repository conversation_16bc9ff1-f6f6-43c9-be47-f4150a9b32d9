package com.czur.czurwma.myentity

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query


/**
 * 本地下载文件的实体类
 */
@Dao
interface LocalDownloadFileDao {
        @Query("SELECT * FROM local_download_file")
    abstract fun getAll(): List<LocalDownloadFileEntity>

//    @Query("select * from local_download_file where localFilePath = :path")
//    suspend fun findByPath(path: String): LocalDownloadFileEntity

    @Insert(onConflict = OnConflictStrategy.REPLACE)//如果有冲突则替换
    abstract fun insert(localDownloadFileEntity: LocalDownloadFileEntity)

    @Query("DELETE FROM local_download_file WHERE id = :id")
    abstract fun delete(id: Long)
}