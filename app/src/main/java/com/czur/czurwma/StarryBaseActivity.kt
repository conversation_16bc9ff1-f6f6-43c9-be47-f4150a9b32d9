package com.czur.czurwma

import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import org.greenrobot.eventbus.EventBus

open class StarryBaseActivity : BaseActivity(), View.OnClickListener {
    protected var imgBack: ImageView? = null
    private var exitTime: Long = 0


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_base)
        imgBack = findViewById(R.id.user_back_btn)
        imgBack?.setOnClickListener(this)
        setPageTitle(R.string.eshare_title)

        // Android 16 预测性返回手势适配
        setupPredictiveBack()
    }

    protected fun setPageTitle(id: Int) {
        val title = findViewById<View>(R.id.user_title) as TextView
        title.setText(id)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    protected fun exitApp() {
        if (System.currentTimeMillis() - exitTime > 2000) {
            ToastUtils.showShort(R.string.confirm_exit)
            exitTime = System.currentTimeMillis()
        } else {
            ActivityUtils.finishAllActivities()
        }
    }

    protected fun backToHome() {
        if (System.currentTimeMillis() - exitTime > 2000) {
            ToastUtils.showShort(R.string.back_to_home)
            exitTime = System.currentTimeMillis()
        } else {
            moveTaskToBack(true)
        }
    }

    open fun onNetConnected() {}

    /**
     * 设置Android 16预测性返回手势
     */
    private fun setupPredictiveBack() {
        if (this is ComponentActivity && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val callback = object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    handleBackPressed()
                }
            }
            this.onBackPressedDispatcher.addCallback(this, callback)
        }
    }

    /**
     * 处理返回按键事件
     * 子类可以重写此方法来自定义返回行为
     */
    protected open fun handleBackPressed() {
        ActivityUtils.finishActivity(this)
    }

    override fun setStatusBarColor(color: Int) {
        BarUtils.setStatusBarColor(this, getColor(color))
    }
}