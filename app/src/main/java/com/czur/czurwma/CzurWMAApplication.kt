package com.czur.czurwma

import android.app.Activity
import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.multidex.MultiDexApplication
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.preferences.FirstPreferences
import com.czur.czurwma.utils.initializeInit
import com.czur.czurwma.viewmodel.EShareApplicationViewModel
import java.util.UUID

class CzurWMAApplication : MultiDexApplication() {

    private var eshareViewModel: EShareApplicationViewModel? = null

    companion object {
        var myApplication: CzurWMAApplication? = null
    }
    var currentActivity: Activity? = null

    override fun onCreate() {
        super.onCreate()
        myApplication = this
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                // 当一个新的Activity创建时，保存这个Activity
                currentActivity = activity
            }

            override fun onActivityStarted(p0: Activity) {

            }

            override fun onActivityResumed(p0: Activity) {
            }

            override fun onActivityPaused(p0: Activity) {
            }

            override fun onActivityStopped(p0: Activity) {
            }

            override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
            }

            override fun onActivityDestroyed(p0: Activity) {
            }
        })
        val uniqueID = UUID.randomUUID().toString()
        val firstPreferences = FirstPreferences.getInstance(this)
//        eshareViewModel =
//            ViewModelProvider.AndroidViewModelFactory(this).create(EShareApplicationViewModel::class.java)
        ESharePreferences.init(this)
        if (ESharePreferences.getInstance().uuid.isNullOrEmpty()) {
            ESharePreferences.getInstance().uuid = uniqueID
        }


        if (firstPreferences?.isFirstEnterApp != true) {
            initializeInit(this)
        }


    }

    private val emo: EShareApplicationViewModel by lazy {
        ViewModelProvider.AndroidViewModelFactory(this)
            .create(EShareApplicationViewModel::class.java)
    }

    fun getEshareViewModel1(): EShareApplicationViewModel {
        return emo
    }

}