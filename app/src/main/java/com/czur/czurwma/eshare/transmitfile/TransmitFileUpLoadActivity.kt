package com.czur.czurwma.eshare.transmitfile

import android.app.Activity
import android.content.ClipData
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ServiceUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logI
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.common.TransmitFileEntity
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.eshare.EShareActivity
import com.czur.czurwma.eshare.adapter.TransmitFileAdapter
import com.czur.czurwma.service.EShareHeartBeatService
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.utils.NetConnectListenerUtil
import com.czur.czurwma.utils.dp2px
import com.czur.czurwma.utils.launch
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.viewmodel.TransmitFileUploadViewModel
import com.czur.czurwma.widget.FileListItemDecoration
import com.czur.starry.device.file.server.TransmitFileHttpClient
import com.eshare.api.EShareAPI
import com.eshare.api.IDevice
import kotlinx.coroutines.Dispatchers
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class TransmitFileUpLoadActivity : StarryBaseActivity(), NetConnectListenerUtil.NetStateListener {

    private val applicationViewModel by lazy {
        (applicationContext as CzurWMAApplication).getEshareViewModel1()
    }

    private val viewModel by lazy {
        ViewModelProvider(this)[TransmitFileUploadViewModel::class.java]
    }

    private val mExecutorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor()
    }

    private val mDeviceManager: IDevice by lazy {
        EShareAPI.init(this).device()
    }

    private val user_title by lazy {
        findViewById<TextView>(R.id.user_title)
    }

    private val file_list_rv by lazy {
        findViewById<RecyclerView>(R.id.file_list_rv)
    }

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    private val done_tv by lazy {
        findViewById<TextView>(R.id.done_tv)
    }

    private val empty_tips_tv by lazy {
        findViewById<TextView>(R.id.empty_tips_tv)
    }
    private val add_file_iv by lazy {
        findViewById<ImageView>(R.id.add_file_iv)
    }


    private var fileList: MutableList<TransmitFileEntity> = mutableListOf()
    private var fileUris: MutableList<Uri>? = mutableListOf()

    lateinit var transmitFileAdapter: TransmitFileAdapter

    var reconnectEshareTime = 0L

    private val onBackPressCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            viewModel.stopUploadAndFinish(this@TransmitFileUpLoadActivity)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_transmit_upload_file)
        user_title.setText(R.string.eshare_transmit_file_title)
        reconnectEshareTime = System.currentTimeMillis() - 60000
        initRegister()
        initIntent()
        initView()
        initData()
        viewModel.startUpload(this)
    }

    private fun initRegister() {
        initNetListener()
        onBackPressedDispatcher.addCallback(this, onBackPressCallback);
    }

    private fun initIntent() {
//        CzurFileUtils.openDocumentToPick(selectFileResultLauncher)
        fileUris = intent.getParcelableArrayListExtra<Uri?>("fileList")?.toMutableList()
    }

    private fun initData() {
        launch {
            // 点击重连的时间,如果网络已经打开,立即重连,否则只处理30s之内的断开
            viewModel.reconnectEshareTimeVM.collect {
                if (!ServiceUtils.isServiceRunning(EShareHeartBeatService::class.java)) {
                    reConnectEshare()
                }
                reconnectEshareTime = it
            }
        }
        launch {
            // eshare连接状态监听
            //只处理30s之内用户主动连接操作(重新连接)
            EShareHeartBeatService.castStatusChanged.collect {
                logI("TransmitFileActivity_castStatusChanged=$it System.currentTimeMillis() - reconnectEshareTime = ${System.currentTimeMillis() - reconnectEshareTime}")
                if (it == 5 && System.currentTimeMillis() - reconnectEshareTime < 30000) {
                    reConnectEshare()
                }
            }
        }

        launch {
            // loading转圈dialog监听
            viewModel.displayProgressDialogFlow.collect {
                if (it) {//断开链接,关闭页面
                    showProgressDialog()
                } else {
                    hideProgressDialog()
                }
            }
        }

        launch {//刷新展示列表的数据刷新
            viewModel.fileDisplayListFlow.collect {
                viewModel.fileList = it
                val list = it.map { it1 ->
                    it1.copy()
                } as MutableList<TransmitFileEntity>
                updateUI(list)
            }
        }

        launch {//上传文件的进度监听,包括上传中的失败
            viewModel.finishUploadFilePathAndResult.collect { it ->
                logI("finishUploadFilePathAndResult = ${it.first} ${it.second}")
                when (it.second) {
                    TransmitFileResultEnum.CODE_200,
                    TransmitFileResultEnum.CODE_202 -> {
                        // CODE_200 正常
                        // CODE_202 校验码

                        //把所有100进度,还是uploading的变成done
                        // 根据设备端返回情况,进行处理
                        val mapList =
                            viewModel.fileList.map { it.copy() } as MutableList<TransmitFileEntity>;

                        mapList.find { itMap ->
                            itMap.progress == 100f && itMap.status == UploadFileEnum.UPLOADING
                        }?.let { itMapLet ->
                            itMapLet.status = UploadFileEnum.DONE_UPLOAD
                        }
                        viewModel.fileDisplayListFlow.emit(mapList)
                        viewModel.startUpload(this@TransmitFileUpLoadActivity)
                    }

                    TransmitFileResultEnum.CODE_210,
                    TransmitFileResultEnum.CODE_220,
                    TransmitFileResultEnum.CODE_230,
                    TransmitFileResultEnum.CODE_9527 -> {
                        // CODE_210 空间不足
                        // CODE_220 上传开关关闭

                        //网络错误,把所有文件都变成失败
                        val list = viewModel.fileList.map { it1 ->
                            it1.copy()
                        } as MutableList<TransmitFileEntity>
                        list.forEach { it1 ->
                            if (it1.status == UploadFileEnum.UPLOADING) {
                                it1.status = UploadFileEnum.FAIL
                            }
                        }
                        viewModel.fileDisplayListFlow.emit(list)
                        if (it.second == TransmitFileResultEnum.CODE_9527) {

                            viewModel.showReconnectUploadDialog(this@TransmitFileUpLoadActivity) { result ->
                                logI("showReconnectUploadDialog result = $result")
                                launch(Dispatchers.IO) {
                                    if (result) {
                                        viewModel.stopUpload()
                                        viewModel.displayProgressDialogFlow.emit(false)

                                        val list1 = viewModel.changeAllFileState(
                                            viewModel.fileList,
                                            UploadFileEnum.UPLOADING
                                        )
                                        viewModel.reconnectEshareTimeVM.emit(System.currentTimeMillis())
                                        viewModel.fileDisplayListFlow.emit(list1)
                                        viewModel.startUpload(this@TransmitFileUpLoadActivity)
                                    } else {
                                        logI("finishUploadFilePathAndResult CODE_9527")
                                        viewModel.stopUpload()
                                        viewModel.displayProgressDialogFlow.emit(false)
                                        viewModel.finishUploadFilePathAndResult.emit(
                                            Pair(
                                                it.first,
                                                TransmitFileResultEnum.CODE_9527
                                            )
                                        )
                                    }
                                }


                            }

                        } else {
                            launch(Dispatchers.Main) {
                                TransmitFileHttpClient.handleResultMessage(it.second.code)
                            }
                        }
                    }

                    TransmitFileResultEnum.CODE_205,
                    TransmitFileResultEnum.CODE_300,
                    TransmitFileResultEnum.CODE_400,
                    TransmitFileResultEnum.CODE_9528,
                    TransmitFileResultEnum.CODE_9529 -> {
                        // CODE_205 md5校验失败
                        // CODE_300 起始字节错误
                        // CODE_400 未知错误

                        // 接口内部错误,把这个变成失败, 尝试一下请求下一个
                        val list = viewModel.fileList.map { it1 ->
                            it1.copy()
                        } as MutableList<TransmitFileEntity>
                        list.find { it2 ->
                            it2.filePath == it.first
                        }?.let { it3 ->
                            if (it3.status != UploadFileEnum.DONE_UPLOAD
                                || it3.progress != 100f
                            ) {
                                it3.status = UploadFileEnum.FAIL
                            }
                        }

                        TransmitFileHttpClient.handleResultMessage(it.second.code)
                        viewModel.fileDisplayListFlow.emit(list)
                        viewModel.startUpload(this@TransmitFileUpLoadActivity)
                    }

                    else -> {
                        viewModel.startUpload(this@TransmitFileUpLoadActivity)
                    }
                }
            }
        }

        viewModel.initRefreshList(this@TransmitFileUpLoadActivity, fileUris)
    }


    private fun updateUI(list: MutableList<TransmitFileEntity>) {

        //如果有Downloading的文件,就不显示正在传输的文本
        val hasUploading = list.any { it1 ->
            it1.status == UploadFileEnum.UPLOADING
        }

        empty_tips_tv.visibility = if (hasUploading) View.VISIBLE else View.INVISIBLE
        done_tv.text =
            if (hasUploading) {
                resources.getString(R.string.transmit_cancel_upload_btn)
            } else {
                resources.getString(R.string.transmit_upload_done)
            }
        updateList(list)
    }

    private fun updateList(list: MutableList<TransmitFileEntity>) {
        transmitFileAdapter.setData(list)
    }

    private fun initView() {
        add_file_iv.visibility = View.VISIBLE
        add_file_iv.singleClick { CzurFileUtils.openDocumentToPick(
            selectFileResultLauncher
        ) }
        user_back_btn.singleClick {
            viewModel.stopUploadAndFinish(this@TransmitFileUpLoadActivity)
        }

        done_tv.singleClick {
            viewModel.stopUploadAndFinish(this@TransmitFileUpLoadActivity)
        }

        initAdapter()
    }


    private fun initAdapter() {
        transmitFileAdapter = TransmitFileAdapter()
        file_list_rv.layoutManager = LinearLayoutManager(this)
        file_list_rv.adapter = transmitFileAdapter
        file_list_rv.itemAnimator = null
        file_list_rv.addItemDecoration(FileListItemDecoration(dp2px(this, 14f)))
        transmitFileAdapter.setItemClickListener(object : TransmitFileAdapter.OnItemClickListener {
            override fun onDeleteClick(position: Int, filePath: String) {
                viewModel.deleteFile(
                    this@TransmitFileUpLoadActivity,
                    filePath,
                    transmitFileAdapter.getData()
                )
            }

            override fun onResumeClick(position: Int, filePath: String) {
                val list = transmitFileAdapter.getData()
                    .map { it.copy() } as MutableList<TransmitFileEntity>

                // 根据uri判断文件是否存在
                val fileStatus =
                    CzurFileUtils.isFileExists(this@TransmitFileUpLoadActivity, list[position].uri)
                if (fileStatus.isNotEmpty()) {
                    ToastUtils.showLong(fileStatus)
                    return
                }

                list.find { it.filePath == filePath }?.let {
                    it.status = UploadFileEnum.UPLOADING
                }
                launch {
                    viewModel.fileDisplayListFlow.emit(list)
                }
                viewModel.startUpload(this@TransmitFileUpLoadActivity)
            }

        })
    }

    private val selectFileResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                launch(Dispatchers.IO) {
                    val fileUriList = mutableListOf<Uri>()
                    val clipData: ClipData? = result.data?.clipData
                    if (clipData != null) {
                        for (i in 0 until clipData.itemCount) {
                            val uri = clipData.getItemAt(i).uri
                            fileUriList.add(uri)
                        }
                        viewModel.addFileUris(this@TransmitFileUpLoadActivity, fileUriList)
                    } else {
                        val uri: Uri? = result.data?.data
                        uri?.let { fileUriList.add(it) }
                        viewModel.addFileUris(this@TransmitFileUpLoadActivity, fileUriList)
                    }

                }

            }
        }

    private fun reConnectEshare() {
        logI("reConnectEshare 重连eshare")
        // 重连eshare
        launch {
            applicationViewModel.disconnectDevice()
            applicationViewModel.connectDeviceByAddress(applicationViewModel.currentIP) { it1 ->
                logI("reConnectEshare 重连eshare  $it1")

                hideProgressDialog()

                if (it1) {
                    launch {
                        EShareActivity.needIntentToFindDeviceAty = false
                    }

                } else {

                    launch {
                        applicationViewModel.disconnectDevice()
                        EShareActivity.needIntentToFindDeviceAty = true
                        EShareHeartBeatService.castStatusChanged.emit(5)
                    }

                }
            }
        }
    }

    private fun initNetListener() {
        NetConnectListenerUtil.init()
        NetConnectListenerUtil.addNetListener(this)
    }

    override fun onNetConnectedChange(isConnect: Boolean) {// 网络变化,检查wifi
//        val fileEntity = viewModel.fileList.firstOrNull {//是否以后正在上传的文件
//            it.status == UploadFileEnum.UPLOADING
//        }
//        logI("onNetConnectedChange isConnect = $isConnect fileEntity = $fileEntity")
//        if (fileEntity != null) {
//            checkTheWifiConnectionStatus()
//        }
    }

    // 检查wifi，检查以后,把列表变成failed 或者重新连接
    /**
     * 参数autoReconnect 是因为有的时候,点击了重连,ping不通,但是wifi恢复了
     */
//    private fun checkTheWifiConnectionStatus(autoReconnect: Boolean = false) {
//        launch(Dispatchers.Main) {
//            if (!NetworkUtils.isWifiConnected()) {
//                viewModel.stopAllUpload()
//                viewModel.showReconnectUploadDialog(this@TransmitFileActivity) { result ->
//                    launch(Dispatchers.IO) {
//                        if (result) {
//                            viewModel.restartUpload(this@TransmitFileActivity)
//                            viewModel.reconnectEshareTimeVM.emit(System.currentTimeMillis())
//                        } else {
//                            checkTheWifiConnectionStatus(true)
//                        }
//                    }
//                }
//            } else if (autoReconnect) {
//                withContext(Dispatchers.IO) {
//                    viewModel.restartUpload(this@TransmitFileActivity)
//                    viewModel.reconnectEshareTimeVM.emit(System.currentTimeMillis())
//                }
//
//                // 如果是连接的状态, 进行检测宜享是否连接, 设置可以自动连接的时间点
////                launch {//自动重连宜享(会有bug,如果远端未开启,使用重连后,再退出当前页面,调用eshareActivity的disconnect,会出错)
////                    viewModel.reconnectEshareTimeVM.emit(System.currentTimeMillis())
////                }
//            }
//
//        }
//
//    }

    override fun onResume() {
        super.onResume()
//        checkTheWifiConnectionStatus()
    }

    override fun onDestroy() {
        super.onDestroy()
        NetConnectListenerUtil.unregistNetListener(this)
    }

}