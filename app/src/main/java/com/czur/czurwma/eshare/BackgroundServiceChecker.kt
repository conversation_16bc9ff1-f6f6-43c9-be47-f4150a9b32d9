package com.czur.czurwma.eshare

import android.app.ActivityManager
import android.content.Context
import com.czur.czurutils.log.logI
import com.czur.czurwma.utils.AppClearUtils
import java.util.*

class BackgroundServiceChecker(private val context: Context) {
    private val timer = Timer()



    fun startChecking() {
        timer.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                val isServiceRunning = isForegroundServiceRunning()
                logI("BackgroundServiceChecker.isServiceRunning=${isServiceRunning}")
                if (!isServiceRunning) {
                    // 前台服务没有运行，执行相应操作
                    AppClearUtils.startScreenNotify()
                }
            }
        }, 0, 20000) // 每隔 20 秒钟检查一次
    }

    fun stopChecking() {
        timer.cancel()
    }

    private fun isForegroundServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningServices = activityManager.getRunningServices(Int.MAX_VALUE)
        for (service in runningServices) {
            if (service.service.className == "com.czur.czurwma.service.KeepConnectionAliveService") {
                return true
            }
        }
        return false
    }
}
