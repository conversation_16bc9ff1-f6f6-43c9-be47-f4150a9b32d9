package com.czur.czurwma.eshare

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.eshare.view.PaintController.Companion.isOpenPaint
import com.czur.czurwma.eshare.view.SwitchView
import com.czur.czurwma.service.EShareHeartBeatService
import com.czur.czurwma.utils.launch
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.MediumBoldTextView

/**
 * 画笔开关
 */
class EShareOpenPainActivity : StarryBaseActivity(), View.OnClickListener {
    private var sp: SharedPreferences? =null
    private val user_title by lazy {
        findViewById<MediumBoldTextView>(R.id.user_title)
    }
    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }
    private val eshare_paint_switch by lazy {
        findViewById<SwitchView>(R.id.eshare_paint_switch)
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.eshare_open_paint_fragment)

        if (sp == null) {
            sp = this!!.getSharedPreferences("brush", Context.MODE_PRIVATE)
        }
        initComponent()
    }

    private fun initComponent() {

        user_title?.setText(R.string.settings)

        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }
        eshare_paint_switch?.setDefaultSwitchState(sp!!.getBoolean("isShow",true))

        eshare_paint_switch?.setOnSwitchChange {isOn, fromClick ->
            launch {
                isOpenPaint.emit(isOn)
            }
            val spedit =   sp!!.edit()
            if (isOn){
                spedit.putBoolean("isShow", true)
                spedit.commit()
            }else{
                spedit.putBoolean("isShow", false)
                spedit.commit()
            }
        }

        launch {
            EShareHeartBeatService.castStatusChanged.collect {
                if (it==5)finish()
            }
        }

    }

}