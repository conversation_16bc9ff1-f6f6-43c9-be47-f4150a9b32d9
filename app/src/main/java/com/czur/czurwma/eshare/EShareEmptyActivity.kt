package com.czur.czurwma.eshare;

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.PermissionUtils
import com.czur.czurwma.common.EshareConstants

import com.czur.czurutils.log.logI
import com.czur.czurwma.R
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.utils.PermissionCallBack
import com.czur.czurwma.utils.PermissionUtil

class EShareEmptyActivity : AppCompatActivity() {
    val TAG = "EShareEmptyActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.starry_activity_blank)

        logI("${TAG}.onCreate")
//        if (FirstPreferences.getInstance(this).isFirstEnterApp) {
//            ToastUtils.showShort(R.string.first_enter_app_desc)
//            finish()
//            return
//        }

        initData(intent)
    }

    fun initData(intent: Intent?) {
        ActivityUtils.getActivityList()
        val type = intent?.getStringExtra(EshareConstants.ESHARE_EMPTY_TYPE)
            ?: EshareConstants.ESHARE_EMPTY_TYPE_NORMAL
        logI("initData.type=${type}")
//        AppClearUtils.startScreenNotify()
        checkESharePermission(type)
    }

    private fun checkPermissions(): Boolean {
        // 如果Android版本大于31
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Settings.canDrawOverlays(applicationContext)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
                    && PermissionUtils.isGranted(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            Settings.canDrawOverlays(applicationContext)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
        }
    }

    private fun checkESharePermission(type: String) {
        val context = applicationContext
        if (checkPermissions()) {
            //权限开启状态
            ESharePreferences.getInstance().moreBtnVisible = false
            ESharePreferences.getInstance().backBtnVisible = false

            val intent = Intent(this, EShareActivity::class.java)
            intent.putExtra(EshareConstants.ESHARE_EMPTY_TYPE, type)
            startActivity(intent)
            finish()
        } else { //权限关闭状态
            val explainStr = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.getString(R.string.eshare_mic_overlay_bt_permission_explain)
            } else {
                context.getString(R.string.eshare_mic_overlay_permission_explain)
            }
            PermissionUtil.checkPermissionWithDialog(
                context,
                context.getString(R.string.starry_popupwindow_title),
                explainStr,
                context.getString(R.string.go_setting),
                context.getString(R.string.starry_background_start_msg_cancel)
            ) {
                if (it != null) { //点击去设置
                    val permissionList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        arrayOf(
                            Manifest.permission.RECORD_AUDIO,
                            Manifest.permission.BLUETOOTH_CONNECT
                        )
                    } else {
                        arrayOf(Manifest.permission.RECORD_AUDIO)
                    }
                    PermissionUtil.useToolsRequestPermission(
                        permissionList,
                        object : PermissionCallBack {
                            override fun execute() {
                                if (!Settings.canDrawOverlays(context)) {
                                    LogUtils.i("${TAG}.checkPermission.当前无权限，请授权")
                                    ActivityUtils.startActivityForResult(
                                        this@EShareEmptyActivity,
                                        Intent(
                                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                            Uri.parse("package:" + context!!.packageName)
                                        ),
                                        EshareConstants.RESULT_CHECK_OVERLAYS_CODE
                                    )
                                }
                                finish()
                            }
                        }
                    )
                } else {//点击取消
                    finish()
                }
            }
        }
    }





}