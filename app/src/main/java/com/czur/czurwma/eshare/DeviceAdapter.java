package com.czur.czurwma.eshare;

import static com.blankj.utilcode.constant.TimeConstants.MIN;
import static com.blankj.utilcode.constant.TimeConstants.SEC;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.czur.czurwma.R;
import com.eshare.api.bean.Device;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * author : SHY
 * time   : 2020/08/14
 */
public class DeviceAdapter extends BaseAdapter {

    private Context mContext;
    private LinkedHashMap<String, ViewDevice> mDeviceMap;
    private List<Device> mDevices = new ArrayList<>();

    private long lastRefreshTime = 0;
    public long userFreshListTime = 0L;// 用户手动刷新,可以间隔500ms刷新一次,其他时间间隔2s刷新一次
    public long lastFreshListTime = 0L;// 用户手动刷新,可以间隔500ms刷新一次,其他时间间隔2s刷新一次

    public DeviceAdapter(Context context) {
        mContext = context;
        mDeviceMap = new LinkedHashMap<>();
    }

    public void clearAllDevices() {
        mDeviceMap.clear();
        updateUIList(false);
    }

    // 从列表移除该设备
    public void removeDevice(Device device) {
        if (mDeviceMap.containsKey(device.getIpAddress())) {
            mDeviceMap.remove(device.getIpAddress());
            updateUIList(false);
        }
    }

    // 从列表移除该设备
    public void removeDeviceAddress(String deviceIP) {
//        Log.i("Jason","mDeviceMap="+ new Gson().toJson(mDeviceMap));
//        Log.i("Jason","deviceIP="+ deviceIP);
        if (mDeviceMap.containsKey(deviceIP)) {
//            Log.i("Jason","mDeviceMap.containsKey(deviceIP)=true");
            mDeviceMap.remove(deviceIP);
            updateUIList(false);
        }
    }

    public void setDevices(List<Device> devices) {

        for (Device device : devices) {
            if (mDeviceMap.containsKey(device.getIpAddress())) {
                mDeviceMap.get(device.getIpAddress()).updateDevice(device);
            } else {
                mDeviceMap.put(device.getIpAddress(), new ViewDevice(device));
            }
        }
        long time = System.currentTimeMillis();
        logTagD("song", "setDevices" + time + "userFreshListTime=" + userFreshListTime + "lastFreshListTime=" + lastFreshListTime);
        if (time - userFreshListTime < 3 * SEC) {// 前10s内用户手动刷新,可以间隔500ms刷新一次,其他时间间隔2s刷新一次
            updateUIList(true);

        } else if (time - userFreshListTime < 10 * SEC && time - lastFreshListTime > 500) {//刷新间隔需要小于10
            updateUIList(true);
            lastFreshListTime = System.currentTimeMillis();
        } else {
            if (time - lastFreshListTime > 2 * SEC) {//刷新间隔需要小于10
                updateUIList(true);
                lastFreshListTime = System.currentTimeMillis();
            }
        }
    }

    /**
     * @param needWait 是否需要间隔的等待刷新,刷新太快会有卡顿,使用recyclerview会有显示不全的bug
     */
    private void updateUIList(boolean needWait) {

        mDevices.clear();
        for (ViewDevice vd : mDeviceMap.values()) {
            mDevices.add(vd.device);
        }
//        if (needWait && System.currentTimeMillis() - lastRefreshTime > 1000) {
//            lastRefreshTime = System.currentTimeMillis();
        // 2s刷新一次,太频繁会有卡顿
        notifyDataSetChanged();
//        }
    }

    /**
     * 检查设备是否超时
     */
    public void clearTimeOutDevices() {

        boolean hasClear = false;
        for (Iterator<Map.Entry<String, ViewDevice>> it = mDeviceMap.entrySet().iterator(); it.hasNext(); ) {
            Map.Entry<String, ViewDevice> item = it.next();
            ViewDevice vd = item.getValue();
            if (vd.isTimeOutDevice()) {
                // 删除超时的设备
                hasClear = true;
                it.remove();
            }
        }
        if (hasClear) {
            logI("DeviceAdapter.需要移除超时设备");
            updateUIList(false);
        }

        for (String ip : mDeviceMap.keySet()) {
            ViewDevice vd = mDeviceMap.get(ip);
            if (vd.isTimeOutDevice()) {
                mDeviceMap.remove(ip);
            }
        }
    }

    @Override
    public int getCount() {
        return mDeviceMap.size();
    }

    @Override
    public Device getItem(int position) {
        if (position >= mDeviceMap.size())
            return null;
        return mDevices.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.eshare_device_list, parent, false);
        TextView tv_name = (TextView) view.findViewById(R.id.device_name);
        TextView tv_ip = (TextView) view.findViewById(R.id.device_ip);
        Device info = mDevices.get(position);
        tv_name.setText(info.getName());
        tv_ip.setText(info.getIpAddress());
        return view;
    }

    private static class ViewDevice {
        // 超过30s没有更新的设备认为是不存在的设备
        private static final long TIME_OUT_THRESHOLD = 30 * 1000L;
        private long updateTime;
        private Device device;

        private boolean isTimeOutDevice() {
            return System.currentTimeMillis() - updateTime >= TIME_OUT_THRESHOLD;
        }

        public ViewDevice(Device device) {
            this.device = device;
            updateTime = System.currentTimeMillis();
        }

        private void updateDevice(Device device) {
            this.device = device;
            updateTime = System.currentTimeMillis();
        }
    }
}
