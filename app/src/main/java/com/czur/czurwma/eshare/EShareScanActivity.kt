package com.czur.czurwma.eshare

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.Vibrator
import android.text.format.Formatter
import android.view.View
import android.widget.ImageView
import androidx.core.app.ActivityCompat
import cn.bingoogolapple.qrcode.core.QRCodeView
import cn.bingoogolapple.qrcode.zxing.ZXingView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.utils.AppClearUtils
import com.czur.czurwma.utils.UrlParamsUtils
import java.util.*
import kotlin.concurrent.schedule

class EShareScanActivity : StarryBaseActivity(), QRCodeView.Delegate {

    companion object {
        private const val ERROR_CODE = "errorStr"
        private const val SUCCESS_CODE = "successStr"
    }

    private val zxingview by lazy {
        findViewById<ZXingView>(R.id.zxingview)
    }

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    private val user_more_btn by lazy {
        findViewById<ImageView>(R.id.user_more_btn)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_scan_zxing)

        zxingview?.setDelegate(this)
//        zxingview?.setType(BarcodeType.ONLY_QR_CODE, null)

        user_back_btn?.setOnClickListener {
            finish()
        }
        user_more_btn?.visibility = View.GONE

        //相机权限处理
        verifyCameraPermissions()

    }

    private val PERMISSIONS_CAMERA = arrayOf(Manifest.permission.CAMERA)
    private fun verifyCameraPermissions() {
        // Check if we have write permission
        val permission = ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA)

        if (permission != PackageManager.PERMISSION_GRANTED) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(
                this, PERMISSIONS_CAMERA, 1
            )
        }
    }

    override fun onStart() {
        super.onStart()
        // 打开后置摄像头开始预览，但是并未开始识别
        zxingview?.startCamera()
        // 显示扫描框，并且延迟0.5秒后开始识别
        zxingview?.startSpotAndShowRect()

    }

    override fun onResume() {
        super.onResume()
        // 打开后置摄像头开始预览，但是并未开始识别
        zxingview?.startCamera()
        // 显示扫描框，并且延迟0.5秒后开始识别
        zxingview?.startSpotAndShowRect()
    }

    override fun onStop() {
        // 关闭摄像头预览，并且隐藏扫描框
        zxingview?.stopCamera()
        super.onStop()
    }

    override fun onDestroy() {
        // 销毁二维码扫描控件
        zxingview?.onDestroy()
        super.onDestroy()
    }

    private fun vibrate() {
        val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        vibrator.vibrate(200)
    }

    override fun onScanQRCodeSuccess(result: String) {
        logI("onScanQRCodeSuccess.result:$result")
        vibrate()
        AppClearUtils.checkESharePermission(this, this, true) {
            if (it){
                //https://czur.com/cn/app/czur?************:8000&sn=0x103001bf&ssid=CPH22A2109000003&devicename=StarryHub%E6%98%AF%E6%98%AF%E6%98%AF&pincode=JOLITSOG&port=8121&iplist=************:************&password=changer007
//               https://c.czur.com/m/a/Upass9e84M#czur?**************:8000&sn=0x103001bf&ssid=CTH66B2503000001&devicename=7777&pincode=------&port=8121&iplist=**************:**************:**************:************&password=changer007&isAp=true

//        if (result.contains("https://czur.com/app/czur?")) {
                if (result.contains("pincode") && result.contains("iplist")) {
                    //{pincode=, password=, port=8121, devicename=EShare-3ph2,
                    // sn=0x3001bf, iplist=************, ssid=CZUR}
                    val res = UrlParamsUtils.getUrlParams(result)
                    logI("getUrlParams=${res}")
                    val pincode = res["pincode"] ?: ""
                    val device_name = res["devicename"] ?: ""
                    val devicesIp  = result.split("?")[1].split(":")[0]// 设备ip

                    val ssid = res["ssid"] ?: ""
                    val password = res["password"] ?: ""

                    logI(
                        "pincode=${pincode},device_name=${device_name}," +
                                "ssid=${ssid},password=${password},")

                    vibrate()

                    val intent = Intent()
                    intent.putExtra("DEVICE_ADRESS", devicesIp)
                    intent.putExtra("DEVICE_NAME", device_name)
                    intent.putExtra("DEVICE_PINCODE", pincode)
                    // 设备是否在线(ip地址和手机在同一网段)
                    intent.putExtra("DEVICE_SSID", ssid)
                    intent.putExtra("DEVICE_PASSWORD", password)

                    setResult(30, intent)

                } else {
                    ToastUtils.showLong(R.string.eshare_scan_error)

                }

                // 延迟调用
                Timer().schedule(300) {
                    finish()
                }
            }else{
                // 打开后置摄像头开始预览，但是并未开始识别
                zxingview?.startCamera()
                // 显示扫描框，并且延迟0.5秒后开始识别
                zxingview?.startSpotAndShowRect()
            }

        }

    }

    override fun onCameraAmbientBrightnessChanged(isDark: Boolean) {}

    var showTime = 0L
    override fun onScanQRCodeOpenCameraError() {
        //短时间内只弹出一次
        val currentTime = System.currentTimeMillis()
        if (currentTime - showTime > 1000) {
            showTime = currentTime
//            ToastUtils.showLong(R.string.can_not_open_camera)
        }

    }

}