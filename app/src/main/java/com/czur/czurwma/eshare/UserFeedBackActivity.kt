package com.czur.czurwma.eshare

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.RegexUtils
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logTagI
import com.czur.czurwma.BuildConfig
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.eshare.engine.Constants
import com.czur.czurwma.utils.Validator
import com.czur.czurwma.utils.uploadLog.UploadLogUtils
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import org.json.JSONObject
import java.io.IOException

/**
 * Created by Yz on 2019/3/14.
 * Email：<EMAIL>
 */
class UserFeedbackActivity : StarryBaseActivity(), View.OnClickListener {
    private val user_title by lazy {
        findViewById<TextView>(R.id.user_title)
    }
    private val commit_btn by lazy {
        findViewById<TextView>(R.id.commit_btn)
    }
    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }
    private val user_mail_edt by lazy {
        findViewById<EditText>(R.id.user_mail_edt)
    }
    private val user_feedback_edt by lazy {
        findViewById<EditText>(R.id.user_feedback_edt)
    }
    private var userFeedbackEdt: EditText? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarColor(this, resources.getColor(R.color.white), true);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        setContentView(R.layout.activity_user_feedback)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        user_title!!.setText(R.string.feedback)
    }

    private fun registerEvent() {
        commit_btn.setOnClickListener(this)
        user_back_btn.setOnClickListener(this)
        user_mail_edt.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (user_mail_edt.text.isNotEmpty() && user_feedback_edt.text.isNotEmpty()) {
                    commit_btn.isClickable = true
                    commit_btn.isEnabled = true
                    commit_btn.setTextColor(ColorUtils.getColor(R.color.red_de4d4d))
                } else {
                    commit_btn.isClickable = false
                    commit_btn.isEnabled = true
                    commit_btn.setTextColor(ColorUtils.getColor(R.color.gray_c4))
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
        user_feedback_edt.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (user_mail_edt.text.isNotEmpty() && user_feedback_edt.text.isNotEmpty()) {
                    commit_btn.isClickable = true
                    commit_btn.isEnabled = true
                    commit_btn.setTextColor(ColorUtils.getColor(R.color.red_de4d4d))
                } else {
                    commit_btn.isClickable = false
                    commit_btn.isEnabled = true
                    commit_btn.setTextColor(ColorUtils.getColor(R.color.gray_c4))
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
    }

    private fun feedback() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.no_connection_network)
            return
        }
        if (!RegexUtils.isEmail(user_mail_edt.text.toString())) {
            showMessage(R.string.login_alert_mail_error)
            return
        }
        // 获取手机型号
        val model = android.os.Build.MODEL
        // 获取手机厂商
        val brand = android.os.Build.BRAND

        val phone = model + "\n" + brand
        val email =
            if (Validator.isEmpty(user_mail_edt.text.toString())) "" else user_mail_edt.text.toString()
        val content = getString(R.string.app_name) + user_feedback_edt.text.toString()
        //Form表单格式的参数传递
        val jsonBody = JSONObject()
        jsonBody.put("platform", "Android")
        jsonBody.put("version", BuildConfig.VERSION_NAME)
        jsonBody.put("language", if (BuildConfig.IS_OVERSEAS) "海外" else "国内")
        jsonBody.put("content", "$email $phone $content")

        val requestBody = RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            jsonBody.toString()
        )

        val request = Request.Builder()
            .post(requestBody)//Post请求的参数传递
            .url(BuildConfig.FEEDBACK_URL)// 意见反馈
            .build()
        OkHttpClient().newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                logTagI(TAG, "e.message${e.message}")
            }

            @Throws(IOException::class)
            override fun onResponse(call: Call, response: Response) {
                //此方法运行在子线程中，不能在此方法中进行UI操作。
                val result = response.body!!.string()
                logTagI(TAG, "result${result}")

                ActivityUtils.finishActivity(this@UserFeedbackActivity)
                runOnUiThread { showMessage(R.string.commit_success) }
            }
        })
        logI("$email $phone $content")
        UploadLogUtils.uploadLogFile()

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
            R.id.commit_btn -> if (Validator.isNotEmpty(user_feedback_edt.text.toString())) {
                feedback()
            } else {
                showMessage(R.string.commit_empty)
            }

            else -> {
            }
        }
    }


}