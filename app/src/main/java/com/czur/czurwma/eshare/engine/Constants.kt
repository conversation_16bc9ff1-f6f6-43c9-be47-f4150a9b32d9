package com.czur.czurwma.eshare.engine

object Constants {
    // 认为设备下线的消息超时次数
    const val OFFLINE_COUNT = 15

    const val OFFLINE_WAIT_COUNT = 10//重连5次
    const val OFFLINE_WAIT_TIME = 12000 // 30秒

    const val RECONNECT_COUNT = 8

    // 心跳包间隔时间
    const val HEART_BEAT_PERIOD: Long = 1000
    //投屏状态
    const val KEY_CAST_STATE = "castState"
    //是否可投屏
    const val REPLYCASTREQUEST = "replyCastRequest"
    //几分屏
    const val MULTISCREEN = "multiScreen"

    const val REPLY_HEART_BEAT = "replyHeartbeat"

    /** 传文档 */
    const val DOCUMENT = 0
    /** 传图片 */
    const val PHOTO = 1
    /** 传音乐 */
    const val AUDIO = 2
    /** 传视频 */
    const val VIDEO = 3
    /** 遥控器 */
    const val CONTROL = 4
    /** 上传 APK */
    const val PACKAGE = 5
    /** 摄像头 */
    const val CAMERA = 6
    /** 传屏幕 */
    const val CAST = 7
    /**独占全屏*/
    const val FULLCAST = 8
    /**电视镜像*/
    const val MIRROR = 9
    /**取消独占全屏*/
    const val FULLCASTCANCEL = 10

    const val WMA_CN = "妙传"

    const val ROOT_LOCAL = "Root-Local"
    const val ROOT_DOWNLOAD = "Root-Download"
    const val ROOT_SHARE = "Root-Share"
    const val ROOT_MEETING = "Root-Meeting"
    const val ROOT_PICTURE = "Root-Picture"
    const val FULLCASTCANCEL_OTHER = ""

    const val CZUR_SHARE_FOLDER = "CzurShareDownload"//成者妙传download文件夹下的文件夹
    const val FILE_NOT_FOUND_EXCEPTION = "fileNOTFoundEXCEPTION"
}