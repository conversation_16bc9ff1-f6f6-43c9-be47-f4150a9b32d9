package com.czur.czurwma.eshare.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.StringUtils
import com.czur.czurwma.R
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.myentity.FileBrowserEntity
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.TransmitFileProgressBar
import com.czur.czurwma.widget.popup.BasePopup
import com.czur.czurwma.widget.popup.WPopParams
import com.czur.czurwma.widget.popup.WPopupDirection

class TransmitFileDownloadFileAdapter :
    RecyclerView.Adapter<TransmitFileDownloadFileAdapter.MyViewHolder>() {
    private val items: MutableList<FileBrowserEntity.FileEntity> = ArrayList()

    private class MyDiffCallback(
        private val oldItems: List<FileBrowserEntity.FileEntity>,
        private val newItems: List<FileBrowserEntity.FileEntity>
    ) : DiffUtil.Callback() {
        override fun getOldListSize(): Int {
            return oldItems.size
        }

        override fun getNewListSize(): Int {
            return newItems.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项是否代表相同的数据对象
            return oldItem.absPath == newItem.absPath
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项的内容是否相同（例如，内容是否完全相等）
            return oldItem.progress == newItem.progress
                    && oldItem.status == newItem.status
                    && oldItem.name == newItem.name
                    && oldItem.fileSize == newItem.fileSize
                    && oldItem.absPath == newItem.absPath
        }
    }

    fun setData(newItems: List<FileBrowserEntity.FileEntity>) {
        // 使用 DiffUtil 计算差异并更新数据集
        val diffResult = DiffUtil.calculateDiff(MyDiffCallback(items, newItems))
        items.clear()
        items.addAll(newItems)
        diffResult.dispatchUpdatesTo(this)
    }

    private var onItemClickListener: OnItemClickListener? = null
    fun setItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    fun getData(): MutableList<FileBrowserEntity.FileEntity> {
        return items
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        // 创建 ViewHolder
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.transmit_file_item_layout, parent, false)



        return MyViewHolder(view)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        // 绑定数据到 ViewHolder
        val item = items[position]
        holder.file_name_tv.text = item.name
        holder.delete_iv.singleClick {
            onItemClickListener?.onDeleteClick(position, item.absPath!!)
        }
        holder.resume_tv.singleClick {
            onItemClickListener?.onResumeClick(position, item.absPath!!)
        }

        when (item.status) {
            UploadFileEnum.DOWNLOADING -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.VISIBLE
                holder.resume_tv.visibility = View.GONE
                holder.done_iv.visibility = View.GONE
                holder.fail_tv.visibility = View.GONE
                holder.error_iv.visibility = View.GONE

                holder.progress_bar.setProgress(item.progress)
            }

            UploadFileEnum.DONE_DOWNLOAD -> {
                holder.delete_iv.visibility = View.GONE
                holder.progress_bar.visibility = View.GONE
                holder.resume_tv.visibility = View.GONE
                holder.done_iv.visibility = View.VISIBLE
                holder.fail_tv.visibility = View.GONE
                holder.error_iv.visibility = View.GONE
            }

            UploadFileEnum.FAIL -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.GONE
                holder.resume_tv.visibility = View.VISIBLE
                holder.done_iv.visibility = View.GONE
                holder.fail_tv.visibility = View.VISIBLE
                holder.error_iv.visibility = View.GONE
            }

            UploadFileEnum.PAUSE -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.GONE
                holder.resume_tv.visibility = View.VISIBLE
                holder.done_iv.visibility = View.GONE
                holder.error_iv.visibility = View.GONE

                holder.fail_tv.visibility = View.GONE
            }

            UploadFileEnum.NONE -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.VISIBLE
                holder.resume_tv.visibility = View.GONE
                holder.done_iv.visibility = View.GONE
                holder.fail_tv.visibility = View.GONE
                holder.error_iv.visibility = View.GONE

                holder.progress_bar.setProgress(item.progress)
            }
            UploadFileEnum.CAN_NOT_DOWNLOAD_EMPTY,
            UploadFileEnum.FILE_NOT_FOUND -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.GONE
                holder.resume_tv.visibility = View.GONE
                holder.done_iv.visibility = View.GONE
                holder.fail_tv.visibility = View.VISIBLE

                holder.error_iv.visibility = View.VISIBLE
                holder.error_iv.singleClick {
                    showErrorPopup(holder.error_iv,item.status)
                }
            }
            else -> {
                holder.delete_iv.visibility = View.VISIBLE
                holder.progress_bar.visibility = View.VISIBLE
                holder.resume_tv.visibility = View.GONE
                holder.done_iv.visibility = View.GONE
                holder.fail_tv.visibility = View.GONE
                holder.error_iv.visibility = View.GONE

                holder.progress_bar.setProgress(item.progress)
            }
        }

    }

    override fun getItemCount(): Int {
        return items.size
    }


    private fun showErrorPopup(view: View, status: UploadFileEnum): BasePopup {
        val customPopup = BasePopup(
            WPopParams(
                R.layout.baselib_pop_tip,
                ActivityUtils.getTopActivity(),
                false,
                cancelable = true,
                width = ViewGroup.LayoutParams.WRAP_CONTENT,
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )
        val tv = customPopup.getContentView() as TextView
        tv.text = when (status) {
            UploadFileEnum.CAN_NOT_DOWNLOAD_EMPTY -> {
                StringUtils.getString(R.string.transmit_can_not_download_empty_tips)
            }
            UploadFileEnum.FILE_NOT_FOUND -> {
                StringUtils.getString(R.string.transmit_not_find_file_tips)
            }

            UploadFileEnum.UPLOADING -> {""}
            UploadFileEnum.DONE_UPLOAD -> {""}
            UploadFileEnum.FAIL -> {""}
            UploadFileEnum.PAUSE -> {""}
            UploadFileEnum.LARGE_SIZE_ERROR -> {""}
            UploadFileEnum.NONE -> {""}
            UploadFileEnum.DOWNLOADING -> {""}
            UploadFileEnum.DONE_DOWNLOAD -> {""}
        }
        customPopup.defaultMargin = 10

        customPopup.dismissListener = {
        }
        customPopup.showAtDirectionByViewAlignRight(view, WPopupDirection.BOTTOM, 20)
        view.findViewTreeLifecycleOwner()?.lifecycle?.let { customPopup.bindLifecycle(it) }

        return customPopup
    }


    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val file_name_tv: TextView // 文件名称
        val progress_bar: TransmitFileProgressBar // 下载进度
        val resume_tv: TextView // 继续下载
        val fail_tv: TextView // 失败文字
        val delete_iv: ImageView // 删除
        val done_iv: ImageView // 已经完成
        val error_iv: ImageView // 异常

        init {
            file_name_tv = itemView.findViewById<TextView>(R.id.file_name_tv)
            progress_bar = itemView.findViewById<TransmitFileProgressBar>(R.id.progress_bar)
            resume_tv = itemView.findViewById<TextView>(R.id.resume_tv)
            fail_tv = itemView.findViewById<TextView>(R.id.fail_tv)
            delete_iv = itemView.findViewById<ImageView>(R.id.delete_iv)
            done_iv = itemView.findViewById<ImageView>(R.id.done_iv)
            error_iv = itemView.findViewById<ImageView>(R.id.error_iv)
        }

    }

    interface OnItemClickListener {
        fun onDeleteClick(position: Int, filePath: String)
        fun onResumeClick(position: Int, filePath: String)
    }

}