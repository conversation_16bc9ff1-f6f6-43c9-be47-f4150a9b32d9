package com.czur.czurwma.eshare.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.StringUtils
import com.czur.czurwma.R
import com.czur.czurwma.myentity.BrowserFileType
import com.czur.czurwma.myentity.RootFileListEntity
import com.czur.czurwma.utils.ObservableList
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.popup.BasePopup
import com.czur.czurwma.widget.popup.WPopParams
import com.czur.czurwma.widget.popup.WPopupDirection

class FileRootBrowserAdapter : RecyclerView.Adapter<FileRootBrowserAdapter.MyViewHolder>() {
    private val items: MutableList<RootFileListEntity.RootGroupItem> = mutableListOf()
    var selectedItems = ObservableList<RootFileListEntity.RootGroupItem>()

    //    private val selectedItems: MutableList<RootFileListEntity.RootGroupItem> = mutableListOf()
//    var selectedItems = ObservableList<RootFileListEntity.RootGroupItem>()

    private class MyDiffCallback(
        private val oldItems: List<RootFileListEntity.RootGroupItem>,
        private val newItems: List<RootFileListEntity.RootGroupItem>
    ) : DiffUtil.Callback() {
        override fun getOldListSize(): Int {
            return oldItems.size
        }

        override fun getNewListSize(): Int {
            return newItems.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项是否代表相同的数据对象
            return oldItem.rootKey == newItem.rootKey
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项的内容是否相同（例如，内容是否完全相等）
//            return oldItem.absPath == newItem.absPath
//                    && oldItem.fileSize == newItem.fileSize
//                    && oldItem.fileType == newItem.fileType
//                    && oldItem.absPathWithSuffix == newItem.absPathWithSuffix
//                    && oldItem.parentPath == newItem.parentPath
//                    && oldItem.pinyinName == newItem.pinyinName
//                    && oldItem.name == newItem.name
            return false
        }
    }

    fun setData(newItems: MutableList<RootFileListEntity.RootGroupItem>) {

        // 使用 DiffUtil 计算差异并更新数据集
        val diffResult = DiffUtil.calculateDiff(MyDiffCallback(items, newItems))
        items.clear()
        items.addAll(newItems)
        cleanSelectList()
        diffResult.dispatchUpdatesTo(this)
    }

    private var onItemClickListener: OnItemClickListener? = null
    fun setItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    fun getData(): MutableList<RootFileListEntity.RootGroupItem> {
        return items
    }

    fun cleanSelectList() {
        selectedItems.clear()
    }

    fun getSelectList(): MutableList<RootFileListEntity.RootGroupItem> {
        return selectedItems
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        // 创建 ViewHolder
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_root_file_browser, parent, false)
        return MyViewHolder(view)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        // 绑定数据到 ViewHolder
        val item = items[position]

        if (item.rootKey == "Root-Line") {
            holder.lineView.visibility = View.VISIBLE
            holder.localFileCl.visibility = View.GONE
            holder.itemView.singleClick {

            }
        } else {
            holder.lineView.visibility = View.GONE
            holder.localFileCl.visibility = View.VISIBLE
            holder.fileNameTv.text = item.name
            when (item.rootKey) {
                "Root-Local" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_local_files)
                }

                "Root-Download" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_download_files)
                }

                "Root-Share" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_czur_wma)
                }

                "Root-Meeting" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_metting_record_files)
                }

                "Root-Picture" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_photo_files)
                }

                "Root-Encrypt" -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_encrypt)
                }

                else -> {
                    holder.iconIv.setImageResource(R.mipmap.ic_file_placeholder)


                }
            }

            holder.itemView.singleClick {
                onItemClickListener?.onItemClick(position, item)
            }
        }

    }

    override fun getItemCount(): Int {
        return items.size
    }


    private fun initPopup(view: View): BasePopup {
        val customPopup = BasePopup(
            WPopParams(
                R.layout.baselib_pop_tip,
                ActivityUtils.getTopActivity(),
                false,
                cancelable = true,
                width = ViewGroup.LayoutParams.WRAP_CONTENT,
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            )
        )
        val tv = customPopup.getContentView() as TextView
        tv.text = StringUtils.getString(R.string.transmit_large_file_size)
        customPopup.defaultMargin = 10

        customPopup.dismissListener = {
        }
        customPopup.showAtDirectionByViewAlignRight(view, WPopupDirection.BOTTOM, 20)
        view.findViewTreeLifecycleOwner()?.lifecycle?.let { customPopup.bindLifecycle(it) }

        return customPopup
    }


    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val localFileCl: ConstraintLayout // 文件名称
        val iconIv: ImageView // 文件类型
        val fileNameTv: TextView // 文件选择框
        val lineView: View

        init {
            localFileCl = itemView.findViewById<ConstraintLayout>(R.id.local_file_cl)
            iconIv = itemView.findViewById<ImageView>(R.id.icon_iv)
            fileNameTv = itemView.findViewById<TextView>(R.id.file_name_tv)
            lineView = itemView.findViewById<View>(R.id.line_view)
        }

    }

    interface OnItemClickListener {
        fun onItemClick(position: Int, fileEntity: RootFileListEntity.RootGroupItem)
    }

}