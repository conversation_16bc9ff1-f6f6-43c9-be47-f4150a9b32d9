package com.czur.czurwma.eshare.view

import android.content.Context
import android.content.SharedPreferences
import android.graphics.PixelFormat
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.*
import android.widget.ImageView
import android.widget.RadioGroup
import android.widget.TextView
import com.blankj.utilcode.util.ColorUtils.getColor
import com.czur.czurwma.R
import com.eshare.api.utils.DisplayUtils
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow

class PlaceHolderLayerController(context: Context) {

    companion object {
        val isOpenPaint = MutableSharedFlow<Boolean>()
    }

    private var mContext: Context? = context
    private var mWM: WindowManager? = null
    private var sp: SharedPreferences? = null
    private var placeHolderTv: TextView? = null
    private var paintCanvas: View? = null
    private var penMenu: View? = null
    private var mCanvasWMParams: WindowManager.LayoutParams? = null

    private var job: Job? = null
    var isPenShow = false


    init {
        init()
        job = MainScope().launch {
            isOpenPaint.collect {
                if (it) {
                    showPaint()
                } else {
                    hidePaint()
                }
            }
        }
    }

    private fun init() {
        mWM = mContext?.getSystemService(Context.WINDOW_SERVICE) as WindowManager?

        //(存储节点文件名称,读写方式)
        if (sp == null) {
            sp = mContext?.getSharedPreferences("brush", Context.MODE_PRIVATE)
        }
        val isShow = sp?.getBoolean("isShow", true)

        if (isShow == true) {
            showPaint()
        } else {
            hidePaint()
        }

    }


    private fun showPaint() {
        Log.i("PaintController", "PaintController.showPaint")
        isPenShow = true
        if (paintCanvas == null && penMenu == null) {
            initCanvas()
        }

        if (paintCanvas != null) {
            paintCanvas?.post { paintCanvas?.visibility = View.VISIBLE }
        }
        if (penMenu != null) {
            penMenu?.post { penMenu?.visibility = View.VISIBLE }
        }
    }

    private fun hidePaint() {
        isPenShow = false
        Log.i("PaintController", "PaintController.hidePaint")
        if (paintCanvas != null) {
            paintCanvas?.post { paintCanvas?.visibility = View.GONE }
        }
        if (penMenu != null) {
            penMenu?.post { penMenu?.visibility = View.GONE }
        }
    }

    public fun initCanvas() {
        if (paintCanvas == null && penMenu == null) {
            paintCanvas = LayoutInflater.from(mContext).inflate(R.layout.place_holder_float_window, null)
            placeHolderTv = paintCanvas?.findViewById(R.id.place_holder_tv)
            mCanvasWMParams = WindowManager.LayoutParams()
            mCanvasWMParams?.width = 1
            mCanvasWMParams?.height = 1
            //初始化坐标
            mCanvasWMParams?.x = 0
            mCanvasWMParams?.y = 0
            mCanvasWMParams?.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mCanvasWMParams?.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                mCanvasWMParams?.type = WindowManager.LayoutParams.TYPE_PHONE
            }
            mWM?.addView(paintCanvas, mCanvasWMParams)

            placeHolderTv?.post { placeHolderTv?.visibility = View.VISIBLE }
        }
    }
}