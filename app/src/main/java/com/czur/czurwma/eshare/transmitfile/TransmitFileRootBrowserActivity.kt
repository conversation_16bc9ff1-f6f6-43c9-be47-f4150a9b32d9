package com.czur.czurwma.eshare.transmitfile

import android.content.Intent
import android.graphics.PorterDuff
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.common.EshareConstants
import com.czur.czurwma.common.EshareConstants.PWD_ERROR_406
import com.czur.czurwma.common.EshareConstants.TRANSMIT_PWD_ALLOW_WRONG_TIMES
import com.czur.czurwma.common.EshareConstants.TRANSMIT_PWD_LOCKED_TIME
import com.czur.czurwma.eshare.adapter.FileRootBrowserAdapter
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.myentity.RootFileListEntity
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.preferences.ESharePreferences
import com.czur.czurwma.service.EShareHeartBeatService
import com.czur.czurwma.utils.launch
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.TransmitFileHttpClient
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


/**
 * 文件浏览器
 */
class TransmitFileRootBrowserActivity : StarryBaseActivity() {
    private val applicationViewModel by lazy {
        (application as CzurWMAApplication).getEshareViewModel1()
    }
    private val userBackBtn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    private val userTitle by lazy {
        findViewById<TextView>(R.id.user_title)
    }


    private val rootFileRv by lazy {
        findViewById<RecyclerView>(R.id.root_file_rv)
    }
    private val fileBrowserFolderStatusTv by lazy {
        findViewById<TextView>(R.id.file_browser_folder_status_tv)
    }
    private val fileBrowserFolderStatusIv by lazy {
        findViewById<ImageView>(R.id.file_browser_folder_status_iv)
    }
    private val eshareTopBarBgRl by lazy {
        findViewById<RelativeLayout>(R.id.eshare_layout_top_bar_rl)
    }
    private val fileBrowserBgRl by lazy {
        findViewById<ConstraintLayout>(R.id.file_browser_bg_rl)
    }

    val adapter: FileRootBrowserAdapter = FileRootBrowserAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_file_browser)
        initRegister()
        initView()
        initData()


    }

    private fun initRegister() {

        launch {
            EShareHeartBeatService.castStatusChanged.collect {
                if (it == 5){
                    rootFileRv.visibility = View.GONE
                    fileBrowserFolderStatusTv.visibility = View.VISIBLE
                    fileBrowserFolderStatusIv.visibility = View.VISIBLE
                    eshareTopBarBgRl.background = null
                    fileBrowserBgRl.background = null
                    eshareTopBarBgRl.setBackgroundColor(resources.getColor(R.color.white))
                    fileBrowserBgRl.setBackgroundColor(resources.getColor(R.color.white))
                    userBackBtn.setColorFilter(R.color.black_22, PorterDuff.Mode.SRC_IN)
                    userTitle.setTextColor(resources.getColor(R.color.black_2a))
                    setStatusBarColor(R.color.white)
                    BarUtils.setStatusBarLightMode(this@TransmitFileRootBrowserActivity, true)
                    fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_net_error)
                    fileBrowserFolderStatusTv.setText(R.string.eshare_error_net_str)
                }
            }
        }
    }

    private fun initView() {
        userTitle.setText(R.string.eshare_transmit_files_download_str)//取文件
        userBackBtn.singleClick {
            finish()
        }
    }

    private fun initData() {

        launch(Dispatchers.IO) {
            val rootFileListEntity =
                TransmitFileHttpClient.getRootList(applicationViewModel.currentIP)
            var rootGroupItemList = mutableListOf<RootFileListEntity.RootGroupItem>()
            if (rootFileListEntity.code == TransmitFileResultEnum.CODE_200.code) {
                // 新版本 展示新的根目录列表
                val gson = Gson()
                rootGroupItemList = formatListData(
                    rootFileListEntity.content ?: RootFileListEntity.RootGroupResponse(listOf())
                )
            } else {
                // 老版本 展示老的根目录列表
                rootGroupItemList = makeFixedRootGroupItemList()
            }
            withContext(Dispatchers.Main) {

                LinearLayoutManager(this@TransmitFileRootBrowserActivity).apply {
                    rootFileRv.layoutManager = this
                }

                adapter.setItemClickListener(object : FileRootBrowserAdapter.OnItemClickListener {
                    override fun onItemClick(
                        position: Int,
                        fileEntity: RootFileListEntity.RootGroupItem
                    ) {
                        if (fileEntity.needPwd) {
                            if (!NetworkUtils.isConnected()) {// 网络断开后
                                ToastUtils.showLong(R.string.eshare_error_net_str)
                                return
                            }
                            launch(Dispatchers.IO) {
                                val starryTransmitSwitchStatus =
                                    TransmitFileHttpClient.getStarryTransmitSwitchStatus(
                                        applicationViewModel.currentIP
                                    )
                                val handleResultMessage =
                                    TransmitFileHttpClient.handleResultMessage(
                                        starryTransmitSwitchStatus.code,
                                        true
                                    )
                                if (!handleResultMessage){
                                    return@launch
                                }
                                withContext(Dispatchers.Main) {
                                    if (starryTransmitSwitchStatus.content?.enableShareFile == true) {
                                        var time = 0L
                                        // 如果开启了开关,就弹窗
                                        val encryptLocked = ESharePreferences.getInstance()
                                            .getEncryptLockedTime(applicationViewModel.currentDevice)
                                        if (encryptLocked.isNotEmpty()){
                                            time = System.currentTimeMillis() - encryptLocked.toLong()
                                        }
                                        if (time < TRANSMIT_PWD_LOCKED_TIME && time != 0L) {// 已经锁定
                                            TransmitFileDialogManager.showEncryptLockedDialog(
                                                applicationViewModel.currentDevice,
                                                this@TransmitFileRootBrowserActivity
                                            )
                                        } else {//没锁定
                                            launch {
                                                val encryptPwd = ESharePreferences.getInstance()
                                                    .getEncryptPwd(applicationViewModel.currentDevice)
                                                var showEncryptPwdDialog = false
                                                if (encryptPwd.isNotEmpty()) {//保存着密码

                                                    val checkEncryptPwd =
                                                        checkEncryptPwd(
                                                            encryptPwd,
                                                            fileEntity,
                                                            true
                                                        )

                                                    if (!checkEncryptPwd) {//没有密码正常弹窗,有密码
                                                        showEncryptPwdDialog = true
                                                    }

                                                } else {
                                                    showEncryptPwdDialog = true
                                                }

                                                if (showEncryptPwdDialog) {
                                                    TransmitFileDialogManager.showEncryptPwdDialog(
                                                        this@TransmitFileRootBrowserActivity
                                                    ) { pwd ->
                                                        if (!NetworkUtils.isConnected()) {// 网络断开后
                                                            ToastUtils.showLong(R.string.eshare_error_net_str)
                                                            return@showEncryptPwdDialog
                                                        }
                                                        if (pwd.isNotEmpty()) {
                                                            launch {
                                                                checkEncryptPwd(pwd, fileEntity)
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        // 没开启开关,toast提示
                                        ToastUtils.showLong(R.string.encrypt_transmit_switch_off)
                                    }
                                }
                            }


                        } else {

                            launch(Dispatchers.IO){
                                if (TransmitFileHttpClient.checkServer(applicationViewModel.currentIP)){
                                    intentToChildBrowserActivity(fileEntity)
                                }else{
                                    TransmitFileHttpClient.handleResultMessage(TransmitFileResultEnum.CODE_9527.code,true)
                                }
                            }
                        }
                    }


                })
                rootFileRv.adapter = adapter

                adapter.setData(rootGroupItemList)
            }

        }

    }

    private suspend fun checkEncryptPwd(
        pwd: String,
        fileEntity: RootFileListEntity.RootGroupItem,
        justCheck: Boolean = false
    ): Boolean {
        return withContext(Dispatchers.IO) {
            val resultEntity = TransmitFileHttpClient.checkEncryptPwd(
                applicationViewModel.currentIP,
                pwd,
                fileEntity.rootKey
            )
            val result = resultEntity.content?.checkRes == true
            if (resultEntity.code == TransmitFileResultEnum.CODE_200.code
                || resultEntity.code == TransmitFileResultEnum.CODE_406.code){
                if (result) {
                    ESharePreferences.getInstance()
                        .setEncryptLockedTime(applicationViewModel.currentDevice, 0)
                    ESharePreferences.getInstance()
                        .setPwdWrongTimes(applicationViewModel.currentDevice, 0)
                    ESharePreferences.getInstance()
                        .setEncryptPwd(applicationViewModel.currentDevice, pwd)
                    withContext(Dispatchers.Main) {
                        intentToChildBrowserActivity(fileEntity, pwd)
                    }
                } else {

                    ESharePreferences.getInstance()
                        .setEncryptPwd(applicationViewModel.currentDevice, "")
                    if (justCheck) {// 只是检查的话,不走后面的弹窗等逻辑,只返回结果
                        return@withContext result
                    }
                    val pwdWrongTimesStr = ESharePreferences.getInstance()
                        .getPwdWrongTimes(applicationViewModel.currentDevice)

                    var pwdWrongTimes = 0
                    if (pwdWrongTimesStr.isNotEmpty()) {
                        pwdWrongTimes = pwdWrongTimesStr.toInt()
                    }
                    pwdWrongTimes += 1
                    ESharePreferences.getInstance()
                        .setPwdWrongTimes(applicationViewModel.currentDevice, pwdWrongTimes)
                    if (pwdWrongTimes >= TRANSMIT_PWD_ALLOW_WRONG_TIMES
                    ) {
                        ESharePreferences.getInstance().setEncryptLockedTime(
                            applicationViewModel.currentDevice,
                            System.currentTimeMillis()
                        )
                        withContext(Dispatchers.Main) {
                            TransmitFileDialogManager.showEncryptLockedDialog(
                                applicationViewModel.currentDevice,
                                this@TransmitFileRootBrowserActivity
                            )
                        }

                        return@withContext result
                    } else {
                        val pwdWrongTimes1 = ESharePreferences.getInstance()
                            .getPwdWrongTimes(applicationViewModel.currentDevice)
                        var pwdWrongTimes2 = 0
                        if (pwdWrongTimes1.isNotEmpty()) {
                            pwdWrongTimes2 = pwdWrongTimes1.toInt()
                        }
                        withContext(Dispatchers.Main) {
                            ToastUtils.showLong(
                                getString(
                                    R.string.encrypt_pwd_error,
                                    (TRANSMIT_PWD_ALLOW_WRONG_TIMES - pwdWrongTimes2).toString()
                                )
                            )
                        }
                    }

                }
            }else{
                TransmitFileHttpClient.handleResultMessage(resultEntity.code,true)
            }


            result
        }
    }

    private fun intentToChildBrowserActivity(
        entity: RootFileListEntity.RootGroupItem,
        pwd: String = ""
    ) {
        val intent = Intent(this, TransmitChildFileBrowserActivity::class.java)
        intent.putExtra("rootType", entity.rootKey)
        intent.putExtra("fileName", entity.name)
        intent.putExtra("needPwd", entity.needPwd)
        intent.putExtra("pwd", pwd)
        startActivity(intent)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(updateEvent: String) {
        if (updateEvent == "allDownloadDone") {
            finish()
        }
    }

    private fun makeFixedRootGroupItemList(): MutableList<RootFileListEntity.RootGroupItem> {
        val rootGroupItemList = mutableListOf<RootFileListEntity.RootGroupItem>()
        rootGroupItemList.add(
            RootFileListEntity.RootGroupItem(
                "Root-Local",
                false,
                getString(R.string.local_files)
            )
        )
        rootGroupItemList.add(
            RootFileListEntity.RootGroupItem(
                "Root-Download",
                false,
                getString(R.string.download_files)
            )
        )
        rootGroupItemList.add(
            RootFileListEntity.RootGroupItem(
                "Root-Share",
                false,
                getString(R.string.eshare_title)
            )
        )
        rootGroupItemList.add(RootFileListEntity.RootGroupItem("Root-Line", false, ""))
        rootGroupItemList.add(
            RootFileListEntity.RootGroupItem(
                "Root-Meeting",
                false,
                getString(R.string.meeting_record)
            )
        )
        rootGroupItemList.add(
            RootFileListEntity.RootGroupItem(
                "Root-Picture",
                false,
                getString(R.string.photo)
            )
        )
        return rootGroupItemList
    }

    // 把其中的RootGroupItem数据转换到一个list中
    private fun formatListData(rootGroupResponse: RootFileListEntity.RootGroupResponse): MutableList<RootFileListEntity.RootGroupItem> {
        val rootGroupList = rootGroupResponse.rootGroup
        val rootGroupItemList = mutableListOf<RootFileListEntity.RootGroupItem>()
        for (index in rootGroupList.indices) {
            val rootGroup = rootGroupList[index]
            for (rootGroupItem in rootGroup) {
                if (rootGroupItem.rootKey == "Root-Encrypt" && !rootGroupItem.needPwd) {
                    continue
                } else {
                    rootGroupItemList.add(rootGroupItem)
                }

            }
            if (index != rootGroupList.size - 1) {
                rootGroupItemList.add(RootFileListEntity.RootGroupItem("Root-Line", false))
            }
        }

        return rootGroupItemList
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}