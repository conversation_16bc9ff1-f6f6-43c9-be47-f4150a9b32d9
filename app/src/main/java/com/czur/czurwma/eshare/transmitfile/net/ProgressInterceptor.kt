package com.czur.czurwma.eshare.transmitfile.net

import com.czur.starry.device.file.server.ProgressResponseBody
import okhttp3.Interceptor
import okhttp3.Response

class ProgressInterceptor(private val progressListener: ProgressResponseBody.ProgressListener) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalResponse = chain.proceed(chain.request())
        return originalResponse.newBuilder()
            .body(ProgressResponseBody(originalResponse.body!!, progressListener))
            .build()
    }
}
