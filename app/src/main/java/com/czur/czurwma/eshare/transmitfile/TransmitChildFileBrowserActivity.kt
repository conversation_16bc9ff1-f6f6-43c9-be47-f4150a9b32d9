package com.czur.czurwma.eshare.transmitfile

import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.NetworkUtils
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.BuildConfig
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.common.EshareConstants
import com.czur.czurwma.common.NetUrls.CLIENT_API_VERSION
import com.czur.czurwma.eshare.adapter.FileBrowserAdapter
import com.czur.czurwma.eshare.adapter.FileBrowserTabAdapter
import com.czur.czurwma.eshare.receiver.CastStateListener
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.myentity.BrowserFileType
import com.czur.czurwma.myentity.FileBrowserEntity
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.service.EShareHeartBeatService
import com.czur.czurwma.utils.ListObserver
import com.czur.czurwma.utils.launch
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.TransmitFileHttpClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.ArrayBlockingQueue
import kotlin.coroutines.suspendCoroutine
import kotlin.math.abs


/**
 * 文件浏览器
 */
class TransmitChildFileBrowserActivity : StarryBaseActivity() {

    var parentFilePath = ""
    var parentFileName = ""
    var needPwd = false
    var pwd = ""
    var fileTabHistoryList = mutableListOf<FileBrowserEntity.FileEntity>()
    var fileDataList = arrayListOf<FileBrowserEntity.FileEntity>()//总数据表

    private val userBackBtn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }


    private val userTitle by lazy {
        findViewById<TextView>(R.id.user_title)
    }

    private val fileBrowserTabRv by lazy {
        findViewById<RecyclerView>(R.id.file_browser_tab_rv)
    }

    private val fileBrowserRv by lazy {
        findViewById<RecyclerView>(R.id.file_browser_rv)
    }

    private val applicationViewModel by lazy {
        (applicationContext as CzurWMAApplication).getEshareViewModel1()
    }

    private val cancelTv by lazy {
        findViewById<TextView>(R.id.cancel_tv)
    }

    private val startTv by lazy {
        findViewById<TextView>(R.id.start_tv)
    }
    private val fileBrowserFolderStatusTv by lazy {
        findViewById<TextView>(R.id.file_browser_folder_status_tv)
    }
    private val fileBrowserFolderStatusIv by lazy {
        findViewById<ImageView>(R.id.file_browser_folder_status_iv)
    }
    private lateinit var transmitFileBrowserAdapter: FileBrowserAdapter
    private lateinit var transmitFileBrowserTabAdapter: FileBrowserTabAdapter
    private var castStatus = 0


    private var lastSixPosition = 0
    private var lastSixScrollTime = 0L
    private var previousFirstVisPosition = 0
    private var fastScrollTime = 0L

    private var fileEntityQueue: ArrayBlockingQueue<FileBrowserEntity.FileEntity>? = null
    private var isFastScroll = false

    private var threadLaunch: Job? = null
    private var mCastStateListener: CastStateListener? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_bg_gray_f5)
//        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_child_file_browser)
        initRegister()
        initIntent()
        initView()
        initAdapter()
        initData()
    }

    private fun initRegister() {


        EventBus.getDefault().register(this)
        launch {
            EShareHeartBeatService.castStatusChanged.collect {
                castStatus = it
                if (castStatus == 5) {
                    val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
                    fileList.add(
                        FileBrowserEntity.FileEntity(
                            resultEnumCode = TransmitFileResultEnum.CODE_9527.code
                        )
                    )
                    launch {
                        handleFileList(fileList)
                    }
                }
            }
        }

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 如果你希望执行默认的后退操作，可以调用 isEnabled = false
                pressBackBtn()
                isEnabled = true
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    private fun initIntent() {
        parentFilePath = intent.getStringExtra("rootType").toString()
        parentFileName = intent.getStringExtra("fileName").toString()
        needPwd = intent.getBooleanExtra("needPwd", false)
        pwd = intent.getStringExtra("pwd").toString()

    }

    private fun initView() {
        fileBrowserRv.itemAnimator = null
        userTitle.text = parentFileName
        userBackBtn.setOnClickListener {
            onBackPressed()
        }

        if (BuildConfig.IS_OVERSEAS) {
            // 海外需要字体字号减小
            startTv.textSize = 13f
            cancelTv.textSize = 13f
        }

        startTv.setOnClickListener {
            if (transmitFileBrowserAdapter.getSelectList().isEmpty()) {
                return@setOnClickListener
            }

            val intent = Intent(this, TransmitFileDownloadActivity::class.java)
            intent.putParcelableArrayListExtra(
                "downloadList",
                ArrayList<Parcelable>(transmitFileBrowserAdapter.getSelectList())
            )
            intent.putExtra("needPwd", needPwd)
            intent.putExtra("pwd", pwd)
            startActivity(intent)
            finish()
        }

        cancelTv.setOnClickListener {
            finish()
        }
    }

    private fun initData() {
        startThumbThread()

        launch {
            transmitFileBrowserAdapter.selectedItems.setListener(object :
                ListObserver<FileBrowserEntity.FileEntity> {
                override fun onAdd(element: FileBrowserEntity.FileEntity) {
                }

                override fun onRemove(element: FileBrowserEntity.FileEntity) {
                }

                override fun onChange(element: FileBrowserEntity.FileEntity?) {
                    if (transmitFileBrowserAdapter.getSelectList().isEmpty()) {
                        startTv.isEnabled = false
                        startTv.background = AppCompatResources.getDrawable(
                            this@TransmitChildFileBrowserActivity,
                            R.drawable.eshare_circle_dark_blue_6dp
                        )
                    } else {
                        startTv.isEnabled = true
                        startTv.background = AppCompatResources.getDrawable(
                            this@TransmitChildFileBrowserActivity,
                            R.drawable.eshare_circle_color_blue_6dp
                        )
                    }
                }
            })
        }
        showProgressDialog()
        launch(Dispatchers.IO) {

            val dataList =
                TransmitFileHttpClient.getFileList(
                    parentFilePath,
                    applicationViewModel.currentIP,
                    needPwd,
                    pwd
                )
            fileDataList =
                ArrayList<FileBrowserEntity.FileEntity>(dataList)
            cleanFilesQueue()
            fileEntityQueue?.offer(FileBrowserEntity.FileEntity())
            fileEntityQueue = if (dataList.isEmpty()) {
                ArrayBlockingQueue(1)
            } else {
                ArrayBlockingQueue(dataList.size)
            }
            if (handleFileList(dataList) && CLIENT_API_VERSION.isNotEmpty()) {
                delay(300)
                if (dataList.isNotEmpty() && CLIENT_API_VERSION.isNotEmpty()) {
                    fileBrowserRv.post {
                        val layoutManager = fileBrowserRv.layoutManager as LinearLayoutManager
                        val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                        addImagesThumbs(0, lastVisibleItemPosition)
                    }
                }
            }

        }
    }

    private fun addImagesThumbs(
        startPosition: Int,
        endPosition: Int
    ) {
        if (startPosition < 0 || endPosition < 0) {
            return
        }
        fileEntityQueue?.clear()

        // 为了视觉效果 显示最后一个再加上一个的位置
        var scrollEndPosition = endPosition + 1
        if (scrollEndPosition > fileDataList.size - 1) {
            scrollEndPosition = endPosition
        }
        for (i in startPosition..scrollEndPosition) {
            val fileEntity = fileDataList[i]
            if (fileEntity.thumbImageDataKey.isNotEmpty()) {
                continue
            }

            <EMAIL>?.offer(fileEntity)
        }
    }

    private fun loadImagesThumbs(
        fileEntity: FileBrowserEntity.FileEntity
    ) {
        launch(Dispatchers.IO) {
            if (fileEntity.fileType == BrowserFileType.IMAGE.typeName) {
                val dataKey = TransmitFileHttpClient.getThumbImageUrl(
                    applicationViewModel.currentIP,
                    fileEntity.absPath!!,
                    fileEntity
                )
                fileDataList.find {
                    it.absPath == fileEntity.absPath
                            && it.fileSize == fileEntity.fileSize
                            && it.lastModifyTime == fileEntity.lastModifyTime
                }?.let {
                    it.thumbImageDataKey = dataKey
                }
                transmitFileBrowserAdapter.refreshData(fileDataList)
            }

        }
    }

    private fun initAdapter() {
        fileBrowserRv.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)

        // 判断最后一项到第一项,然后请求这之间的所有图片接口
        // 滚动的时候就一直判断最后一项到第一项,
        // (1.判断的过程中需要看是否已经请求过此条数据,
        // 2.滑动停止了应该先清空队列,再请求接口)
        fileBrowserRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                // 1开始滑动,停止所有,2惯性滑动,0停止滑动
                if (newState == 0 && isFastScroll) {
                    isFastScroll = false
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                    val firstVisPosition = layoutManager!!.findFirstVisibleItemPosition()
                    val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
                    cleanFilesQueue()
                    addImagesThumbs(firstVisPosition, lastVisibleItemPosition)
                    previousFirstVisPosition = firstVisPosition

                }

            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {

                val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                val firstVisPosition = layoutManager!!.findFirstVisibleItemPosition()
                val lastVisibleItemPosition = layoutManager!!.findLastVisibleItemPosition()
                // 快于200ms/6个为暂停加载界限
                if (abs(firstVisPosition - lastSixPosition) > 6) {
                    lastSixPosition = firstVisPosition
                    val currentScrollTime = System.currentTimeMillis() - lastSixScrollTime
                    if (currentScrollTime < 250) {
                        fastScrollTime = System.currentTimeMillis()
                        isFastScroll = true
                        cleanFilesQueue()
                    }
                    lastSixScrollTime = System.currentTimeMillis()
                } else {
                    if (previousFirstVisPosition != firstVisPosition) {
                        if (System.currentTimeMillis() - fastScrollTime > 300) {
                            isFastScroll = false
                            addImagesThumbs(firstVisPosition, lastVisibleItemPosition)
                        }
                        previousFirstVisPosition = firstVisPosition

                    }
                }
            }
        })

        transmitFileBrowserAdapter = FileBrowserAdapter(this, applicationViewModel.currentIP)
        fileBrowserRv.adapter = transmitFileBrowserAdapter
        transmitFileBrowserAdapter.setItemClickListener(object :
            FileBrowserAdapter.OnItemClickListener {
            override fun onItemClick(position: Int, fileEntity: FileBrowserEntity.FileEntity) {
                if (fileEntity.fileType == BrowserFileType.FOLDER.typeName) {
                    loadFolderList(fileEntity) {
                        loadFolderTabList(fileEntity)
                    }
                }
            }

        })

        fileTabHistoryList.clear()
        fileTabHistoryList.add(
            FileBrowserEntity.FileEntity(
                absPath = parentFilePath,
                name = parentFileName
            )
        )
        fileBrowserTabRv.layoutManager =
            androidx.recyclerview.widget.LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
        transmitFileBrowserTabAdapter = FileBrowserTabAdapter()
        fileBrowserTabRv.adapter = transmitFileBrowserTabAdapter
        transmitFileBrowserTabAdapter.setData(fileTabHistoryList)
        transmitFileBrowserTabAdapter.setItemClickListener(object :
            FileBrowserTabAdapter.OnItemClickListener {
            override fun onItemClick(entity: FileBrowserEntity.FileEntity) {
                // 如果点击的是当前页面的tab,增加loading,然后清除所有数据后,再刷新
                loadFolderList(entity) {
                    loadFolderTabList(entity)
                }
            }
        })

    }


    private fun loadFolderTabList(fileEntity: FileBrowserEntity.FileEntity) {

        launch(Dispatchers.IO) {
            fileTabHistoryList.find { it.absPath == fileEntity.absPath }?.let {
                fileTabHistoryList =
                    fileTabHistoryList.subList(0, fileTabHistoryList.indexOf(it) + 1)
            } ?: run {
                fileTabHistoryList.add(fileEntity)
            }

            withContext(Dispatchers.Main) {
                transmitFileBrowserTabAdapter.setData(fileTabHistoryList)
                userTitle.text = fileTabHistoryList[fileTabHistoryList.size - 1].name
                // 添加数据时有动画效果的滚动到最后一个
                fileBrowserTabRv.smoothScrollToPosition(fileTabHistoryList.size - 1)
            }
        }
    }

    private fun loadFolderList(
        fileEntity: FileBrowserEntity.FileEntity,
        handleFinish: (Boolean) -> Unit = {}
    ) {

        showProgressDialog()
        launch(Dispatchers.IO){
            val dataList =
                TransmitFileHttpClient.getFileList(
                    fileEntity.absPath!!,
                    applicationViewModel.currentIP, needPwd, pwd
                )
            cleanFilesQueue()
            fileEntityQueue?.offer(FileBrowserEntity.FileEntity())
            fileEntityQueue = if (dataList.isEmpty()) {
                ArrayBlockingQueue(1)
            } else {
                ArrayBlockingQueue(dataList.size)
            }
            fileDataList =
                ArrayList<FileBrowserEntity.FileEntity>(dataList)
            val handleFileList = handleFileList(dataList)
            if (fileDataList.isNotEmpty() && CLIENT_API_VERSION.isNotEmpty()) {
                val lastVisibleItemPosition = if (fileDataList.size > 25) {
                    25
                } else {
                    fileDataList.size - 1
                }
                fileBrowserRv.post {
                    addImagesThumbs(0, lastVisibleItemPosition)
                }
            }
            lastSixPosition = 0
            lastSixScrollTime = 0L

            handleFinish.invoke(handleFileList)
        }
    }

    private suspend fun handleFileList(fileList: ArrayList<FileBrowserEntity.FileEntity>): Boolean {
        return suspendCoroutine { continuation ->
            launch(Dispatchers.Main) {
                hideProgressDialog(true)
                fileBrowserTabRv.visibility = View.VISIBLE
                cancelTv.visibility = View.VISIBLE
                startTv.visibility = View.VISIBLE
            }
            if (fileList.isNotEmpty()) {
                when (fileList[0].resultEnumCode) {
                    TransmitFileResultEnum.CODE_200.code -> {
                        // 正常
                        launch(Dispatchers.Main) {
                            transmitFileBrowserAdapter.setData(fileList)
                            fileBrowserFolderStatusTv.visibility = View.GONE
                            fileBrowserFolderStatusIv.visibility = View.GONE
                            if (fileList.isNotEmpty()) {
                                fileBrowserRv.post {
                                    fileBrowserRv.scrollToPosition(0)
                                }
                            }
                        }
                        continuation.resumeWith(Result.success(true))
                        return@suspendCoroutine
                    }

                    TransmitFileResultEnum.CODE_220.code -> {//关闭了文件传输开关
                        launch(Dispatchers.Main) {
                            transmitFileBrowserAdapter.setData(arrayListOf())
                            fileBrowserFolderStatusTv.visibility = View.VISIBLE
                            fileBrowserFolderStatusIv.visibility = View.VISIBLE
                            cancelTv.visibility = View.GONE
                            startTv.visibility = View.GONE

                            fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_transmit_file_switch)
                            fileBrowserFolderStatusTv.setText(R.string.eshare_transmit_file_dialog_retry_title)

                        }
                        // 文件传输开关关闭
                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }

                    TransmitFileResultEnum.CODE_405.code -> {
                        // 文件上锁
                        launch(Dispatchers.Main) {
                            transmitFileBrowserAdapter.setData(arrayListOf())
                            fileBrowserFolderStatusTv.visibility = View.VISIBLE
                            fileBrowserFolderStatusIv.visibility = View.VISIBLE
                            cancelTv.visibility = View.GONE
                            startTv.visibility = View.GONE

                            fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_folder_locked)
                            fileBrowserFolderStatusTv.setText(R.string.browser_folder_locked_tips)
                        }

                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }

                    TransmitFileResultEnum.CODE_404.code -> {
                        // 文件丢失的情况, 直接刷新到最初始的页面
                        launch(Dispatchers.Main) {
                            initAdapter()
                            initData()
                        }

                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }

                    TransmitFileResultEnum.CODE_406.code -> {
                        launch(Dispatchers.Main) {
                            TransmitFileDialogManager.showEncryptStarryChangePwdDialog(true)
                        }
                        return@suspendCoroutine
                    }

                    else -> {//报错
                        launch(Dispatchers.Main) {
                            transmitFileBrowserAdapter.setData(arrayListOf())

                            fileBrowserFolderStatusTv.visibility = View.VISIBLE
                            fileBrowserFolderStatusIv.visibility = View.VISIBLE
                            cancelTv.visibility = View.GONE
                            startTv.visibility = View.GONE

                            if (!NetworkUtils.isConnected()
                                || fileList[0].resultEnumCode == TransmitFileResultEnum.CODE_9527.code
                            ) {
                                fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_net_error)
                                fileBrowserFolderStatusTv.setText(R.string.eshare_error_net_str)
                            } else {// 无文件
                                fileBrowserTabRv.visibility = View.INVISIBLE
                                fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_empty_folder)
                                fileBrowserFolderStatusTv.setText(R.string.browser_no_file_tips)

                            }

                        }
                        continuation.resumeWith(Result.success(false))
                        return@suspendCoroutine
                    }
                }

            } else {
                launch(Dispatchers.Main) {
                    transmitFileBrowserAdapter.setData(arrayListOf())

                    fileBrowserFolderStatusTv.visibility = View.VISIBLE
                    fileBrowserFolderStatusIv.visibility = View.VISIBLE
                    cancelTv.visibility = View.GONE
                    startTv.visibility = View.GONE

                    fileBrowserTabRv.visibility = View.INVISIBLE
                    fileBrowserFolderStatusIv.setImageResource(R.mipmap.ic_empty_folder)
                    fileBrowserFolderStatusTv.setText(R.string.browser_no_file_tips)
                }
            }

            continuation.resumeWith(Result.success(false))
        }
    }

    private fun pressBackBtn() {
        // 看看是不是最后一层,如果是,结束页面,不是的话查找上一层的tab
        if (fileTabHistoryList.size == 1) {
            finish()
        } else {
            selectPreviousTab()
        }
    }

    // 返回到上一层tab
    private fun selectPreviousTab() {
        val lastTab = fileTabHistoryList[fileTabHistoryList.size - 2]
        loadFolderList(lastTab) {
            loadFolderTabList(lastTab)
        }
    }


    private fun cleanFilesQueue() {
        fileEntityQueue?.clear()
    }

    private fun startThumbThread() {
        fileBrowserRv.post {
            launch(Dispatchers.IO) {
                while (true) {
                    if (isFastScroll) {
                        delay(100)
                        continue
                    } else {
                        val entity = fileEntityQueue?.take()
                        if (entity != null) {
                            loadImagesThumbs(entity)
                        }
                        delay(60)
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(updateEvent: EventBusEvent) {
        val event = updateEvent.event
        when (event) {
            EshareConstants.PWD_ERROR_406 -> {
                finish()
            }
        }
    }

}