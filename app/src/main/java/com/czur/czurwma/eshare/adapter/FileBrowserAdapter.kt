package com.czur.czurwma.eshare.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.StringUtils
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import com.czur.czurwma.R
import com.czur.czurwma.common.NetUrls
import com.czur.czurwma.eshare.transmitfile.DiskLruCacheManager
import com.czur.czurwma.eshare.transmitfile.TransmitChildFileBrowserActivity
import com.czur.czurwma.myentity.BrowserFileType
import com.czur.czurwma.myentity.FileBrowserEntity
import com.czur.czurwma.utils.ObservableList
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.IconImageView
import com.czur.czurwma.widget.popup.BasePopup
import com.czur.czurwma.widget.popup.WPopParams
import com.czur.czurwma.widget.popup.WPopupDirection
import com.czur.starry.device.file.server.TransmitFileHttpClient.successPort
import okhttp3.HttpUrl
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull

class FileBrowserAdapter(
    transmitChildFileBrowserActivity: TransmitChildFileBrowserActivity,
    currentIP: String
) : RecyclerView.Adapter<FileBrowserAdapter.MyViewHolder>() {
    private var context: Context? = transmitChildFileBrowserActivity
    private var currentIP: String? = currentIP
    var selectedItems = ObservableList<FileBrowserEntity.FileEntity>()

    //    private val selectedItems: MutableList<FileBrowserEntity.FileEntity> = mutableListOf()
//    var selectedItems = ObservableList<FileBrowserEntity.FileEntity>()

    private val itemCallback = object : DiffUtil.ItemCallback<FileBrowserEntity.FileEntity>() {
        override fun areItemsTheSame(
            oldItem: FileBrowserEntity.FileEntity,
            newItem: FileBrowserEntity.FileEntity
        ): Boolean = oldItem.absPath == newItem.absPath

        override fun areContentsTheSame(
            oldItem: FileBrowserEntity.FileEntity,
            newItem: FileBrowserEntity.FileEntity
        ): Boolean {
            return oldItem.absPath == newItem.absPath
                    && oldItem.thumbImageDataKey == newItem.thumbImageDataKey
        }
    }
    private val differ = AsyncListDiffer(this, itemCallback)

    fun setData(newItems: ArrayList<FileBrowserEntity.FileEntity>) {
        val list1 = newItems.map { it1 ->
            it1.copy()
        }
        cleanSelectList()
        differ.submitList(list1)
    }

    fun refreshData(newItems: ArrayList<FileBrowserEntity.FileEntity>) {
        val list1 = newItems.map { it1 ->
            it1.copy()
        }
        differ.submitList(list1)
    }

    private var onItemClickListener: OnItemClickListener? = null
    fun setItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    fun cleanSelectList() {
        selectedItems.clear()
    }

    fun getSelectList(): MutableList<FileBrowserEntity.FileEntity> {
        return selectedItems
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        // 创建 ViewHolder
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_browser_file_layout, parent, false)
        return MyViewHolder(view)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        // 绑定数据到 ViewHolder
        val item = differ.currentList[position]
        holder.file_name_tv.text = item.name

        if (item.fileType == "IMAGE") {
            if (item.thumbImageDataKey.isNotEmpty()) {
                val readDiskLruCache =
                    DiskLruCacheManager.readDiskLruCache(item.thumbImageDataKey)
                Glide.with(context!!)
                    .load(readDiskLruCache)
                    .placeholder(R.mipmap.file_icon_pic)
                    .error(R.mipmap.file_icon_pic)
                    .into(holder.file_type_iv)
            } else {
                Glide.with(context!!)
                    .load(R.mipmap.file_icon_pic)
                    .into(holder.file_type_iv)
            }
        } else {
            holder.file_type_iv.changeIconByEntity(item)
        }
        if (item.fileType == BrowserFileType.FOLDER.typeName) {
            holder.arrow_iv.visibility = View.VISIBLE
            holder.selected_iv.visibility = View.GONE
        } else {
            holder.arrow_iv.visibility = View.GONE
            holder.selected_iv.visibility = View.VISIBLE
        }
        selectedItems.find { it.absPath == item.absPath && it.fileSize == item.fileSize }?.let {
            holder.selected_iv.setImageResource(R.mipmap.ic_selected)
        } ?: run {
            holder.selected_iv.setImageResource(R.mipmap.ic_deselected)
        }

        holder.itemView.singleClick {
            if (item.fileType == BrowserFileType.FOLDER.typeName) {
                onItemClickListener?.onItemClick(position, item)
            } else {
                selectedItems.find { it.absPath == item.absPath && it.fileSize == item.fileSize }
                    ?.let {
                        holder.selected_iv.setImageResource(R.mipmap.ic_deselected)
                        selectedItems.remove(it)
                    } ?: run {
                    holder.selected_iv.setImageResource(R.mipmap.ic_selected)
                    selectedItems.add(item)
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val file_name_tv: TextView // 文件名称
        val file_type_iv: IconImageView // 文件类型
        val selected_iv: ImageView // 文件选择框
        val arrow_iv: ImageView

        init {
            file_name_tv = itemView.findViewById<TextView>(R.id.file_name_tv)
            file_type_iv = itemView.findViewById<IconImageView>(R.id.file_type_iv)
            selected_iv = itemView.findViewById<ImageView>(R.id.selected_iv)
            arrow_iv = itemView.findViewById<ImageView>(R.id.arrow_iv)
        }

    }

    interface OnItemClickListener {
        fun onItemClick(position: Int, fileEntity: FileBrowserEntity.FileEntity)
    }

}