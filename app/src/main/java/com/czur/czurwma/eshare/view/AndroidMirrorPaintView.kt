package com.czur.czurwma.eshare.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View

class AndroidMirrorPaintView: View {
    private val DEFAULT_PAINT_FLAGS = (Paint.FILTER_BITMAP_FLAG
            or Paint.DITHER_FLAG)
    private var mPaint: Paint? = null
    private var mBitmap: Bitmap? = null
    private var mCanvas: Canvas? = null
    private var mPath: Path? = null
    private var mBitmapPaint: Paint? = null
    private var Pwidth = 1
    private var Pheight = 1
    private var mX = 0f
    private var mY:Float = 0f
    private val TOUCH_TOLERANCE = 4f

    constructor(context: Context) : super(context) {
        init(context!!)
    }
    constructor(context: Context,attrs:AttributeSet) : super(context,attrs) {
        init(context!!)
    }
    constructor(context: Context,attrs:AttributeSet, def: Int) : super(context,attrs,def) {
        init(context!!)
    }


    private fun init(context: Context) {
        mPaint = Paint(DEFAULT_PAINT_FLAGS)
        mPaint!!.setAntiAlias(true)
        mPaint!!.setDither(true)
        mPaint!!.setStyle(Paint.Style.STROKE)
        mPaint!!.setStrokeJoin(Paint.Join.ROUND)
        mPaint!!.setStrokeCap(Paint.Cap.ROUND)
        mPaint!!.setStrokeWidth(10f)
        if (mBitmap != null && !mBitmap!!.isRecycled()) mBitmap!!.recycle()
        mBitmap = Bitmap.createBitmap(Pwidth, Pheight, Bitmap.Config.ARGB_8888)
        mCanvas = Canvas(mBitmap!!)
        mPath = Path()
        mBitmapPaint = Paint(DEFAULT_PAINT_FLAGS)
    }

    fun setColor(color: Int) {
        mPaint!!.color = color
    }

    fun reset() {
        if (mBitmap != null && !mBitmap!!.isRecycled()) mBitmap!!.recycle()
        if (width<= 0 || height <= 0){
            mBitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888)
            mCanvas = Canvas(mBitmap!!)
        }else{
        mBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        mCanvas = Canvas(mBitmap!!)
        }
        mPath!!.reset()
        invalidate()
    }

    fun touch_move(x: Float, y: Float) {
        val dx = Math.abs(x - mX)
        val dy = Math.abs(y - mY)
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            mPath!!.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2)
            mX = x
            mY = y
        }
    }

    fun touch_up() {
        mPath!!.lineTo(mX, mY)
        mCanvas!!.drawPath(mPath!!, mPaint!!)
        mPath!!.reset()
    }

    private fun touch_start(x: Float, y: Float) {
        mPath!!.reset()
        mPath!!.moveTo(x, y)
        mX = x
        mY = y
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        Pwidth = w
        Pheight = h
        reset()
    }

    override fun onDraw(canvas: Canvas) {
        canvas!!.drawBitmap(mBitmap!!, 0f, 0f, mBitmapPaint)
        canvas.drawPath(mPath!!, mPaint!!)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        val x = event!!.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> touch_start(x, y)
            MotionEvent.ACTION_MOVE -> {
                touch_move(x, y)
                invalidate()
            }
            MotionEvent.ACTION_UP -> {
                touch_up()
                invalidate()
            }
        }
        return true
    }
}