package com.czur.czurwma.eshare.view

import android.content.Context
import android.content.SharedPreferences
import android.graphics.PixelFormat
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.*
import android.widget.ImageView
import android.widget.RadioGroup
import com.blankj.utilcode.util.ColorUtils.getColor
import com.czur.czurwma.R
import com.eshare.api.utils.DisplayUtils
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow

class PaintController : View.OnClickListener, View.OnTouchListener {

    companion object {
        val isOpenPaint = MutableSharedFlow<Boolean>()
    }

    private var mContext: Context? = null
    private var mWM: WindowManager? = null
    private var sp: SharedPreferences? = null
    private var penColor: Int? = 0
    private var clickPenDownTime: Long = 0
    private var screenWidth: Int? = 0
    private var screenHeight = 0
    private var penCurrentY = 0f
    private var penCurrentX = 0f
    private var paintView: AndroidMirrorPaintView? = null
    private var penStartX: Float? = null
    private var penStartY: Float? = null
    private var paintCanvas: View? = null
    private var penMenu: View? = null
    private var radioGroup: RadioGroup? = null
    private var mPenBtn: View? = null
    private var mPenIcon: ImageView? = null
    private var mSelectColor: View? = null
    private var mCanvasWMParams: WindowManager.LayoutParams? = null
    private var mPenMenuWMParams: WindowManager.LayoutParams? = null

    //	判断是否是轻触，大于该值表示为移动
    private var tapThreshold = 0
    private var mPixel: View? = null
    private var job: Job? = null
    private var job2: Job? = null
//    private var origentationListener : OrientationEventListener? =null

    //	判断是否是点击，大于该值为长按
    private var clickTimeout: Long = 0

    //颜色板状态是否撤销
    private var isdrawing = false

    var lastFlag = 0
    var isPenShow = false

    //画笔颜色是否显示
    var isShowColorPen = false

    constructor(context: Context) {
        mContext = context
        init()
        job = MainScope().launch {
            isOpenPaint.collect {
                if (it) {
                    showPaint()
                } else {
                    hidePaint()
                }
            }
        }
    }

    private fun init() {
        mWM = mContext?.getSystemService(Context.WINDOW_SERVICE) as WindowManager?

        //(存储节点文件名称,读写方式)
        if (sp == null) {
            sp = mContext?.getSharedPreferences("brush", Context.MODE_PRIVATE)
        }
        val isShow = sp?.getBoolean("isShow", true)

        if (isShow == true) {
            showPaint()
        } else {
            hidePaint()
        }

    }


    private fun showPaint() {
        Log.i("PaintController", "PaintController.showPaint")
        isPenShow = true
        if (paintCanvas == null && penMenu == null) {
            initCanvas()
            initPaint()
            initSensor()
        }

        if (paintCanvas != null) {
            paintCanvas?.post { paintCanvas?.visibility = View.VISIBLE }
        }
        if (penMenu != null) {
            penMenu?.post { penMenu?.visibility = View.VISIBLE }
        }
        resetPainting()
    }

    private fun hidePaint() {
        isPenShow = false
        Log.i("PaintController", "PaintController.hidePaint")
        if (paintCanvas != null) {
            paintCanvas?.post { paintCanvas?.visibility = View.GONE }
        }
        if (penMenu != null) {
            penMenu?.post { penMenu?.visibility = View.GONE }
        }
    }

    private fun initCanvas() {

        paintCanvas = LayoutInflater.from(mContext).inflate(R.layout.pen_for_android_mirror, null)
        paintView = paintCanvas?.findViewById(R.id.paint_view)
        mCanvasWMParams = WindowManager.LayoutParams()

        mCanvasWMParams?.format = PixelFormat.RGBA_8888
        mCanvasWMParams?.width = ViewGroup.LayoutParams.WRAP_CONTENT
        mCanvasWMParams?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        mCanvasWMParams?.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mCanvasWMParams?.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            mCanvasWMParams?.type = WindowManager.LayoutParams.TYPE_PHONE
        }
        mWM?.addView(paintCanvas, mCanvasWMParams)

        paintCanvas?.post { paintCanvas?.visibility = View.GONE }
        paintView?.post { paintView?.visibility = View.GONE }
    }

    private fun initPaint() {

        refrushScreeen()

        penMenu = LayoutInflater.from(mContext).inflate(R.layout.pen_menu_for_mirror_layout, null)
        mPenBtn = penMenu?.findViewById(R.id.pen_layout)
        mPenIcon = penMenu?.findViewById(R.id.pen_iv)
        mSelectColor = penMenu?.findViewById(R.id.select_layout)
        radioGroup = penMenu?.findViewById(R.id.color_layout)
        mPenMenuWMParams = WindowManager.LayoutParams()
        mPenMenuWMParams?.flags =
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mPenMenuWMParams?.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            mPenMenuWMParams?.type = WindowManager.LayoutParams.TYPE_PHONE
        }
        mPenMenuWMParams?.format = PixelFormat.RGBA_8888 //背景透明
        mPenMenuWMParams?.gravity = Gravity.LEFT or Gravity.TOP
        //初始化坐标
        mPenMenuWMParams?.x = screenWidth!!
        mPenMenuWMParams?.y = screenHeight * 3 / 5
        mPenMenuWMParams?.width = ViewGroup.LayoutParams.WRAP_CONTENT
        mPenMenuWMParams?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        mWM?.addView(penMenu, mPenMenuWMParams)



        mPenBtn?.setOnTouchListener(this)
        mPenBtn?.setOnClickListener(this)
        mSelectColor?.setOnClickListener(this)

        penMenu?.findViewById<View>(R.id.red)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.white)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.black)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.green)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.yello)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.cblue)?.setOnClickListener(this)
        penMenu?.findViewById<View>(R.id.blue)?.setOnClickListener(this)

        tapThreshold = mContext?.let { ViewConfiguration.get(it).scaledTouchSlop }!!
        clickTimeout = ViewConfiguration.getTapTimeout().toLong()
    }

    fun initSensor() {
        var lastFlag = false
        var firstFlag = true
        var isLand = false
        job2 = MainScope().launch {
            while (isActive) {
                delay(500)
                val angele = mWM?.defaultDisplay?.rotation
                when (angele) {
                    Surface.ROTATION_90,
                    Surface.ROTATION_270 -> {
                        if (isLand) continue
                        refrushScreeen(true)
                        updatePenMenu()
                        isLand = true
                    }

                    Surface.ROTATION_0,
                    Surface.ROTATION_180 -> {
                        if (!isLand) continue
                        if (firstFlag == lastFlag) continue
                        refrushScreeen()
                        updatePenMenu()
                        isLand = false
                    }
                }
            }
        }
    }


    fun refrushScreeen(isLand: Boolean = false) {
        val metrics = DisplayMetrics()
        mWM?.defaultDisplay?.getMetrics(metrics)
        if (isLand) {
            screenWidth = metrics.heightPixels
            screenHeight = metrics.widthPixels
        } else {
            screenWidth = metrics.widthPixels
            screenHeight = metrics.heightPixels
        }
    }

    fun updatePenMenu() {
        mPenMenuWMParams?.x = screenWidth!!
        mPenMenuWMParams?.y = screenHeight * 3 / 5
        mWM?.updateViewLayout(penMenu, mPenMenuWMParams)

    }

    fun getBoolean(ctx: Context, key: String, default: Boolean): Boolean {

        //(存储节点文件名称,读写方式)
        if (sp == null) {
            sp = ctx.getSharedPreferences("brush", Context.MODE_PRIVATE)
        }
        return sp!!.getBoolean(key, default)
    }


    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        val id = v?.id
        if (id == R.id.pen_layout) {
            penCurrentX = Math.abs(event!!.rawX)
            penCurrentY = Math.abs(event.rawY)
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    penStartX = event.x
                    penStartY = event.y
                    clickPenDownTime = System.currentTimeMillis()
                }

                MotionEvent.ACTION_MOVE -> {
                    if (Math.abs(event.x - penStartX!!) >= tapThreshold
                        || Math.abs(event.y - penStartY!!) >= tapThreshold
                    ) {
                        mPenMenuWMParams?.x = Math.abs(penCurrentX - penStartX!!).toInt()
                        mPenMenuWMParams?.y = Math.abs(penCurrentY - penStartY!!).toInt()
                        mWM?.updateViewLayout(penMenu, mPenMenuWMParams)
                    }
                }

                MotionEvent.ACTION_UP -> {
                    if (Math.abs(event.x - penStartX!!) < tapThreshold
                        && Math.abs(event.y - penStartY!!) < tapThreshold
                    ) {
                        return false
                    } else {

                        if (mPenMenuWMParams!!.x >= screenWidth!! / 2) {
                            mPenMenuWMParams!!.x = screenWidth!!
                        } else {
                            mPenMenuWMParams!!.x = 20
                        }

                        mPenMenuWMParams?.y = Math.abs(penCurrentY - penStartY!!).toInt()
                        mWM?.updateViewLayout(penMenu, mPenMenuWMParams)
                        return true
                    }
                }
            }
        }
        return false
    }

    override fun onClick(v: View?) {
        val i = v!!.id
        when (i) {
            R.id.pen_layout -> {
                if (isdrawing) {
                    resetPainting()
                } else {
                    if (isShowColorPen) {
                        resetPainting()
                    } else {
                        mPenIcon?.setBackgroundResource(R.mipmap.white_back_icon)
                        showColorPens()
                    }

                }
            }

            R.id.black -> {
                updateColorUI(R.id.black)
                penColor = getColor(R.color.black)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.white -> {
                updateColorUI(R.id.white)
                penColor = getColor(R.color.white)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.red -> {
                updateColorUI(R.id.red)
                penColor = getColor(R.color.red_d54146)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.green -> {
                updateColorUI(R.id.green)
                penColor = getColor(R.color.green_8ae5b1)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.yello -> {
                updateColorUI(R.id.yello)
                penColor = getColor(R.color.yellow_faec94)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.cblue -> {
                updateColorUI(R.id.cblue)
                penColor = getColor(R.color.blue_c)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.blue -> {
                updateColorUI(R.id.blue)
                penColor = getColor(R.color.blue_a)
                paintView?.setColor(penColor!!)
                hideColorPens()
            }

            R.id.select_layout -> {
                if (isShowColorPen) {
                    hideColorPens()
                } else {
                    showColorPens()
                }

            }
        }
    }


    private fun showColorPens() {
        isShowColorPen = true
        radioGroup?.visibility = View.VISIBLE
        mPenIcon?.setBackgroundResource(R.mipmap.icon_draw_cancel)
        hideSelectButton()
    }

    private fun hideColorPens() {
        isShowColorPen = false
        radioGroup?.visibility = View.GONE
    }

    //显示墨盘按钮
    private fun showSelectButton() {
        mSelectColor?.findViewById<View>(R.id.select_layout)?.visibility = View.VISIBLE
    }

    private fun hideSelectButton() {
        mSelectColor?.findViewById<View>(R.id.select_layout)?.visibility = View.GONE
    }

    private fun updateColorUI(flag: Int) {

        isdrawing = true
        penMenu?.findViewById<View>(flag)?.layoutParams?.width = DisplayUtils.dip2px(mContext, 25f)
        penMenu?.findViewById<View>(flag)?.layoutParams?.height = DisplayUtils.dip2px(mContext, 25f)

        if (lastFlag != 0 && lastFlag != flag) {
            penMenu?.findViewById<View>(lastFlag)?.layoutParams?.width =
                DisplayUtils.dip2px(mContext, 20f)
            penMenu?.findViewById<View>(lastFlag)?.layoutParams?.height =
                DisplayUtils.dip2px(mContext, 20f)
        }
        lastFlag = flag
        mCanvasWMParams?.width = ViewGroup.LayoutParams.MATCH_PARENT
        mCanvasWMParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
        mWM?.updateViewLayout(paintCanvas, mCanvasWMParams)
        paintCanvas?.post { paintCanvas?.visibility = View.VISIBLE }
        paintView?.post { paintView?.visibility = View.VISIBLE }
        showSelectButton()
    }

    private fun resetPainting() {
        Log.i("PaintController", "PaintController.resetPainting")
        paintView?.reset()
        paintCanvas?.post { paintCanvas?.visibility = View.GONE }
        paintView?.post { paintView?.visibility = View.GONE }
        hideColorPens()
        mPenIcon?.setBackgroundResource(R.mipmap.icon_pen_def)
        isdrawing = false
        hideSelectButton()
        if (lastFlag != 0) {
            radioGroup?.clearCheck()
            penMenu?.findViewById<View>(lastFlag)?.layoutParams?.width =
                DisplayUtils.dip2px(mContext, 20f)
            penMenu?.findViewById<View>(lastFlag)?.layoutParams?.height =
                DisplayUtils.dip2px(mContext, 20f)
        }


    }


    fun close() {
        Log.i("PaintController", "PaintController.close")
        try {
            job?.cancel()
            job2?.cancel()
            mWM?.removeViewImmediate(penMenu)
            mWM?.removeViewImmediate(paintCanvas)
//            if (origentationListener != null)
//                origentationListener!!.disable()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun show() {
        Log.i("PaintController", "PaintController.show")
//        showPaint()
        isPenShow = true
        if (paintCanvas == null && penMenu == null) {
            initCanvas()
            initPaint()
            initSensor()
        }

        if (paintCanvas != null) {
            paintCanvas?.post { paintCanvas?.visibility = View.VISIBLE }
        }
        if (penMenu != null) {
            penMenu?.post { penMenu?.visibility = View.VISIBLE }
        }
    }
}