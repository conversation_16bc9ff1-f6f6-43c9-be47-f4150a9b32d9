package com.czur.czurwma.eshare.transmitfile

import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.czurutils.log.logI
import com.czur.czurwma.CzurWMAApplication
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.common.UploadFileEnum
import com.czur.czurwma.myentity.FileBrowserEntity
import com.czur.czurwma.myenum.TransmitFileResultEnum
import com.czur.czurwma.eshare.adapter.TransmitFileDownloadFileAdapter
import com.czur.czurwma.eshare.engine.Constants
import com.czur.czurwma.utils.CzurFileUtils
import com.czur.czurwma.utils.dp2px
import com.czur.czurwma.utils.launch
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.viewmodel.TransmitFileDownloadViewModel
import com.czur.czurwma.widget.FileListItemDecoration
import com.czur.czurwma.widget.TransmitFileDialogManager
import com.czur.starry.device.file.server.TransmitFileHttpClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import java.io.File


/**
 * 下载文件列表页面
 */
class TransmitFileDownloadActivity : StarryBaseActivity() {

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    private val user_title by lazy {
        findViewById<TextView>(R.id.user_title)
    }

    private val file_list_rv by lazy {
        findViewById<RecyclerView>(R.id.file_list_rv)
    }

    private val empty_tips_tv by lazy {
        findViewById<TextView>(R.id.empty_tips_tv)
    }

    private val show_more_history_tv by lazy {
        findViewById<TextView>(R.id.show_more_history_tv)
    }

    private val done_tv by lazy {
        findViewById<TextView>(R.id.done_tv)
    }
    private val open_file_browser_iv by lazy {
        findViewById<ImageView>(R.id.open_file_browser_iv)
    }

    private val viewModel by lazy {
        ViewModelProvider(this)[TransmitFileDownloadViewModel::class.java]
    }

    private val applicationViewModel by lazy {
        (applicationContext as CzurWMAApplication).getEshareViewModel1()
    }

    private var fileEntityList: ArrayList<FileBrowserEntity.FileEntity> = arrayListOf()

    private lateinit var transmitFileAdapter: TransmitFileDownloadFileAdapter

    var reconnectEshareTime = 0L
    var needPwd = false
    var pwd = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.eshare_common_bg)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.eshare_activity_transmit_download_file)
        launch {

//            val uploadFileLive: LiveData<List<LocalDownloadFileEntity>>? =
//                AppDatabase.getDatabase(this@TransmitFileDownloadActivity).localDownloadFileDao().getAll()
//
//
//            // 监听uploadFileLive
//            uploadFileLive?.observe(this@TransmitFileDownloadActivity) {
//                logTagI("song123", "uploadFileLive = ${it.size}")
//            }


        }
        reconnectEshareTime = System.currentTimeMillis() - 60000
        initRegister()
        initIntent()
        initView()
        initData()
        viewModel.startDownload(this)
    }

    private fun initRegister() {
        launch {
            // loading转圈dialog监听
            viewModel.displayProgressDialogFlow.collect {
                if (it) {//断开链接,关闭页面
                    showProgressDialog()
                } else {
                    hideProgressDialog()
                }
            }
        }
        launch(Dispatchers.IO) {
            viewModel.fileDisplayListFlow.collect {items ->
                val list = items.map { it.copy() }
                
                viewModel.fileList = ArrayList(list)
                withContext(Dispatchers.Main) {
                    updateUI(ArrayList(list))
                }
            }
        }


        launch {//上传文件的进度监听,包括上传中的失败
            viewModel.finishDownloadFilePathAndResult.collect { it ->
                logI("finishUploadFilePathAndResult = path ${it.first} resultCode ${it.second}")
                when (it.second) {
                    TransmitFileResultEnum.CODE_200,
                    TransmitFileResultEnum.CODE_202 -> {
                        // CODE_200 正常
                        // CODE_202 校验码

                        //把所有100进度,还是uploading的变成done
                        // 根据设备端返回情况,进行处理
                        val mapList =
                            viewModel.fileList.map { it.copy() } as MutableList<FileBrowserEntity.FileEntity>;

                        mapList.find { itMap ->
                            itMap.absPath == it.first
                        }?.let {
                            // 更新已下载的文件大小
                            it.hasDownloadedChunkFilesSize =
                                (it.hasDownloadedChunkFilesSize.toLong() + it.currentChunkDownloadSize.toLong()).toString()
                            it.currentChunkNumber = (it.currentChunkNumber.toLong() + 1).toString()

                            if (it.hasDownloadedChunkFilesSize == it.allChunkFilesSize
                                && it.currentChunkNumber == it.totalChunkNumber
                            ) {
                                //说明已经下载完毕了
                                it.status = UploadFileEnum.DONE_DOWNLOAD
//                                launch(Dispatchers.IO) {
//                                    database.localDownloadFileDao().insert(
//                                        LocalDownloadFileEntity(
//                                            0,
//                                            localFilePath = it.absPath!!,
//                                            localParentPath = it.localFileParentPath!!,
//                                            fileName = it.name!!
//                                        )
//                                    )
//                                }

                                //下载完毕需要合并文件
                                val fileList = viewModel.findMergeFilesList(
                                    this@TransmitFileDownloadActivity,
                                    it
                                )
                                CzurFileUtils.mergeFiles(
                                    this@TransmitFileDownloadActivity,
                                    fileList,
                                    it.name!!
                                )
                                logI("合并文件完成")
                            }

                            viewModel.fileDisplayListFlow.emit(mapList)
                            viewModel.initRefreshFileList(mapList)
                            logI("重新开始下载")
                            viewModel.startDownload(this@TransmitFileDownloadActivity)
                        }


                    }

                    TransmitFileResultEnum.CODE_210,
                    TransmitFileResultEnum.CODE_220,
                    TransmitFileResultEnum.CODE_230,
                    TransmitFileResultEnum.CODE_405,
                    TransmitFileResultEnum.CODE_406,
                    TransmitFileResultEnum.CODE_9527 -> {
                        // CODE_210 空间不足
                        // CODE_220 上传开关关闭

                        //网络错误,把所有文件都变成失败
                        val list = viewModel.fileList.map { it1 ->
                            it1.copy()
                        } as MutableList<FileBrowserEntity.FileEntity>
                        list.forEach { it1 ->
                            if (it1.status == UploadFileEnum.DOWNLOADING) {
                                it1.status = UploadFileEnum.FAIL
                            }
                        }
                        viewModel.fileDisplayListFlow.emit(list)
                        if (it.second == TransmitFileResultEnum.CODE_9527) {

                            viewModel.showReconnectDownloadDialog(this@TransmitFileDownloadActivity) { result ->
                                logI("finishDownloadFilePathAndResult result = $result")
                                launch(Dispatchers.IO) {
                                    if (result) {
                                        viewModel.stopDownload()
                                        viewModel.displayProgressDialogFlow.emit(false)

                                        val list1 = viewModel.changeAllFileState(
                                            viewModel.fileList,
                                            UploadFileEnum.DOWNLOADING
                                        )

                                        viewModel.reconnectEshareTimeVM.emit(System.currentTimeMillis())
                                        viewModel.fileDisplayListFlow.emit(list1)
                                        viewModel.startDownload(this@TransmitFileDownloadActivity)
                                    } else {
                                        logI("finishUploadFilePathAndResult CODE_9527")
                                        viewModel.stopDownload()
                                        viewModel.displayProgressDialogFlow.emit(false)
                                        viewModel.finishDownloadFilePathAndResult.emit(
                                            Pair(
                                                it.first,
                                                TransmitFileResultEnum.CODE_9527
                                            )
                                        )
                                    }
                                }


                            }

                        } else if (it.second == TransmitFileResultEnum.CODE_406) {
                            launch(Dispatchers.Main) {
                                TransmitFileHttpClient.handleResultMessage(
                                    it.second.code,
                                    activity = this@TransmitFileDownloadActivity,
                                    needGoToRoot = true
                                )
                            }
                        } else {
                            launch(Dispatchers.Main) {
                                TransmitFileHttpClient.handleResultMessage(it.second.code)
                            }
                        }
                    }

                    TransmitFileResultEnum.CODE_205,
                    TransmitFileResultEnum.CODE_300,
                    TransmitFileResultEnum.CODE_400,
                    TransmitFileResultEnum.CODE_9528,
                    TransmitFileResultEnum.CODE_9529 -> {
                        // CODE_205 md5校验失败
                        // CODE_300 起始字节错误
                        // CODE_400 未知错误

                        // 接口内部错误,把这个变成失败, 尝试一下请求下一个
                        val list = viewModel.fileList.map { it1 ->
                            it1.copy()
                        } as MutableList<FileBrowserEntity.FileEntity>
                        list.find { it2 ->
                            it2.absPath == it.first
                        }?.let { it3 ->
                            if (it3.status != UploadFileEnum.DONE_UPLOAD
                                || it3.progress != 100f
                            ) {
                                it3.status = UploadFileEnum.FAIL
                            }
                        }

                        TransmitFileHttpClient.handleResultMessage(it.second.code)
                        viewModel.fileDisplayListFlow.emit(list)
                        viewModel.startDownload(this@TransmitFileDownloadActivity)
                    }

                    TransmitFileResultEnum.CODE_404 -> {
                        // 文件不存在
                        // 继续下载下一个,这个变成叹号
                        val list = viewModel.fileList.map { it1 ->
                            it1.copy()
                        } as MutableList<FileBrowserEntity.FileEntity>
                        list.find { it2 ->
                            it2.absPath == it.first
                        }?.let { it3 ->
                            if (it3.status != UploadFileEnum.DONE_DOWNLOAD
                                || it3.progress != 100f
                            ) {
                                it3.status = UploadFileEnum.FILE_NOT_FOUND
                            }
                        }

                        TransmitFileHttpClient.handleResultMessage(it.second.code)
                        viewModel.fileDisplayListFlow.emit(list)
                        viewModel.startDownload(this@TransmitFileDownloadActivity)

                    }

                    else -> {
                        viewModel.startDownload(this@TransmitFileDownloadActivity)
                    }
                }
            }
        }

    }

    private fun initIntent() {
        needPwd = intent.getBooleanExtra("needPwd", false)
        pwd = intent.getStringExtra("pwd").toString()
        viewModel.needPwd = needPwd
        viewModel.pwd = pwd
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableArrayListExtra(
                "downloadList",
                FileBrowserEntity.FileEntity::class.java
            )?.let {
                fileEntityList.addAll(formatData(it))
            }
        } else {
            (intent.getParcelableArrayListExtra<FileBrowserEntity.FileEntity>("downloadList") as? ArrayList<FileBrowserEntity.FileEntity>)?.let {
                fileEntityList.addAll(formatData(it))
            }

        }

    }

    private fun formatData(fileEntities: java.util.ArrayList<FileBrowserEntity.FileEntity>): MutableList<FileBrowserEntity.FileEntity> {
        // 如果有文件大小为0的文件,则不改变status为CAN_NOT_DOWNLOAD_EMPTY
        val list = fileEntities.map { it1 ->
            it1.copy()
        } as MutableList<FileBrowserEntity.FileEntity>
        list.forEach {
            if (it.fileSize == 0L) {
                it.status = UploadFileEnum.CAN_NOT_DOWNLOAD_EMPTY
            }
        }
        return list
    }

    private fun initData() {
        CzurFileUtils.createDownloadCzurWMAFolder(this)
        val file = File(filesDir, Constants.CZUR_SHARE_FOLDER)
        file.deleteRecursively()
        updateUI(fileEntityList)
        viewModel.initRefreshFileList(fileEntityList)
    }

    private fun initView() {

        open_file_browser_iv.visibility = View.VISIBLE

        user_title.setText(R.string.eshare_transmit_files_download_str)//取文件
        user_back_btn.singleClick {
            viewModel.stopUploadAndFinish(this@TransmitFileDownloadActivity)
        }

        done_tv.singleClick {
            EventBus.getDefault().post("allDownloadDone")
            viewModel.stopUploadAndFinish(this@TransmitFileDownloadActivity)
        }

//        show_more_history_tv.singleClick {
//            launch(Dispatchers.IO) {
//                val list = transmitFileAdapter.getData().map { it.copy() }
//                val list1 = mutableListOf<FileBrowserEntity.FileEntity>()
//                list1.addAll(list)
//                database.localDownloadFileDao().getAll().forEach {
//                    list1.add(
//                        FileBrowserEntity.FileEntity(
//                            id = it.id,
//                            status = UploadFileEnum.DONE_DOWNLOAD,
//                            absPath = it.localFilePath,
//                            name = it.fileName
//                        )
//                    )
//                }
//                transmitFileAdapter.setData(list1)
//            }
//        }

        val path: String? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // 对于Android 10及以上版本，您应在您的应用专属目录中获取路径
            "sdcard/Download "
        } else {
            // 对于Android 10以下版本，您可以获取公共下载目录的路径
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
        }

        open_file_browser_iv.singleClick {
            val str = getString(
                R.string.transmit_download_path_tip,
                "${path}/${Constants.CZUR_SHARE_FOLDER}"
            )
            ToastUtils.showLong(str)
        }

        initAdapter()
    }


    private fun initAdapter() {
        transmitFileAdapter = TransmitFileDownloadFileAdapter()
        file_list_rv.layoutManager = LinearLayoutManager(this)
        file_list_rv.setHasFixedSize(true)//假设每个item高度都一样,避免重复计算高度
        file_list_rv.adapter = transmitFileAdapter
        file_list_rv.itemAnimator = null
        file_list_rv.addItemDecoration(FileListItemDecoration(dp2px(this, 14f)))
        file_list_rv.setItemViewCacheSize(20)
//        transmitFileAdapter.setHasStableIds(true)
        transmitFileAdapter.setItemClickListener(object :
            TransmitFileDownloadFileAdapter.OnItemClickListener {
            override fun onDeleteClick(position: Int, filePath: String) {
                viewModel.deleteDownloadFile(
                    this@TransmitFileDownloadActivity,
                    filePath,
                    transmitFileAdapter.getData()
                )

            }

            override fun onResumeClick(position: Int, filePath: String) {
                val list = transmitFileAdapter.getData()
                    .map { it.copy() } as MutableList<FileBrowserEntity.FileEntity>

                //刷新文件的ui
                list.find { it.absPath == filePath }?.let {
                    it.status = UploadFileEnum.DOWNLOADING
                }
                launch {
                    viewModel.fileDisplayListFlow.emit(list)
                    delay(500)
                    viewModel.startDownload(this@TransmitFileDownloadActivity)
                }
            }

        })
    }


    private fun updateUI(list: List<FileBrowserEntity.FileEntity>) {

        //如果有Downloading的文件,就不显示正在传输的文本
        val hasDownloading = list.any { it1 ->
            it1.status == UploadFileEnum.DOWNLOADING
        }

        empty_tips_tv.visibility = if (hasDownloading) View.VISIBLE else View.INVISIBLE
        done_tv.text =
            if (hasDownloading) {
                resources.getString(R.string.transmit_cancel_download_btn)
            } else {
                resources.getString(R.string.transmit_download_done)
            }
        updateList(list)
    }

    private fun updateList(list: List<FileBrowserEntity.FileEntity>) {
        launch(Dispatchers.Main) {
            transmitFileAdapter.setData(list)
        }
    }

}