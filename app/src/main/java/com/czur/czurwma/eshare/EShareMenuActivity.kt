package com.czur.czurwma.eshare


import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.czurwma.AboutActivity
import com.czur.czurwma.BuildConfig
import com.czur.czurwma.R
import com.czur.czurwma.StarryBaseActivity
import com.czur.czurwma.WebViewActivity
import com.czur.czurwma.common.EshareConstants
import com.czur.czurwma.eventbusevent.EventBusEvent
import com.czur.czurwma.utils.singleClick
import com.czur.czurwma.widget.CloudCommonDialog
import com.czur.czurwma.widget.MediumBoldTextView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Calendar

/**
 * 更多页面
 */
class EShareMenuActivity : StarryBaseActivity(), View.OnClickListener {
    private var commonPopup: CloudCommonDialog? = null

    private val user_title by lazy {
        findViewById<MediumBoldTextView>(R.id.user_title)
    }
    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }

    //用户协议
    private val user_menu_about_rl by lazy {
        findViewById<RelativeLayout>(R.id.user_menu_about_rl)
    }

    //用户协议
    private val user_menu_privacy_rl by lazy {
        findViewById<RelativeLayout>(R.id.user_menu_privacy_rl)
    }

    //隐私政策
    private val user_menu_privacy_rl_privacy by lazy {
        findViewById<RelativeLayout>(R.id.user_menu_privacy_rl_privacy)
    }

    //个人信息收集清单
    private val user_menu_privacy_rl_info by lazy {
        findViewById<RelativeLayout>(R.id.user_menu_privacy_rl_info)
    }

    //第三方共享信息清单
    private val user_menu_privacy_rl_share by lazy {
        findViewById<RelativeLayout>(R.id.user_menu_privacy_rl_share)
    }
    private val update_new_version_tv by lazy {
        findViewById<TextView>(R.id.update_new_version_tv)
    }
    private val feedback_rl by lazy {
        findViewById<RelativeLayout>(R.id.feedback_rl)
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarColor(this, resources.getColor(R.color.white), true);
        BarUtils.setStatusBarLightMode(getWindow(), true);

        setContentView(R.layout.eshare_activity_menu_home)
        EventBus.getDefault().register(this)

        initComponent()

    }

    private fun initComponent() {
        user_title?.setText(R.string.title_more)

        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        // 关于
        user_menu_about_rl?.singleClick {
            val intent = Intent(this, AboutActivity::class.java)
            ActivityUtils.startActivity(intent)
        }

        // 用户协议
        user_menu_privacy_rl?.singleClick {
            val intent = Intent(this, WebViewActivity::class.java)
            intent.putExtra("title", getString(R.string.user_privacy_user))
            val cTime = Calendar.getInstance().timeInMillis
//            intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT1 + cTime + "")
            intent.putExtra("url", BuildConfig.TERMS_URL)
            ActivityUtils.startActivity(intent)
        }


        // 隐私政策
        user_menu_privacy_rl_privacy?.singleClick {
            val intent = Intent(this, WebViewActivity::class.java)
            intent.putExtra("title", getString(R.string.user_privacy_privacy))
            val cTime = Calendar.getInstance().timeInMillis
//            intent.putExtra("url", CZURConstants.PRIVACY_AGREEMENT)
            intent.putExtra("url", BuildConfig.PRIVACY_AGREEMENT)
            ActivityUtils.startActivity(intent)
        }

        user_menu_privacy_rl_info?.singleClick {
        }

        user_menu_privacy_rl_share?.singleClick {
        }

        feedback_rl?.singleClick {
            ActivityUtils.startActivity(UserFeedbackActivity::class.java)
        }

        val stickyEvent = EventBus.getDefault().getStickyEvent(EventBusEvent::class.java)
        if (stickyEvent != null) {
            onEvent(stickyEvent)
        } else {
            update_new_version_tv.visibility = View.GONE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(updateEvent: EventBusEvent) {
        val event = updateEvent.event
        when (event) {
            EshareConstants.APP_UPDATE_HAS_UPDATE_INFO -> {
                update_new_version_tv.visibility = View.VISIBLE
            }

            EshareConstants.APP_UPDATE_NO_UPDATE_INFO -> {
                update_new_version_tv.visibility = View.GONE
            }


        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}