package com.czur.czurwma.eshare.transmitfile

import android.content.Context
import android.os.Environment
import com.jakewharton.disklrucache.DiskLruCache
import java.io.File

// 硬盘缓存管理类
object DiskLruCacheManager {
    lateinit var diskLruCache: DiskLruCache

    @JvmStatic
    fun init(context: Context) {
        val cacheDir = getDiskCacheDir(context, "TransmitFileListThumbImageCache");
        val appVersion = 1;
        val valueCount = 1;
        diskLruCache = DiskLruCache.open(cacheDir, appVersion, valueCount, 10 * 1024 * 1024);
    }

    // 获取私有文件目录
    private fun getDiskCacheDir(context: Context, uniqueName: String): File {
        val cachePath =
            if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState() || !Environment.isExternalStorageRemovable()) {
                context.externalCacheDir!!.path
            } else {
                context.cacheDir.path
            }
        return File(cachePath + File.separator + uniqueName)
    }

    // key的组成逻辑:文件size+文件最后一次修改时间+文件名
    @JvmStatic
    fun readDiskLruCache(key: String): ByteArray? {
        val cleanString = cleanString(key)
        val file = diskLruCache.get(cleanString)
        if (file != null) {
            val fileInputStream = file.getInputStream(0)
            return fileInputStream.readBytes()
        }
        return null
    }

    @JvmStatic
    fun checkDiskLruCache(key: String): String {
        val cleanString = cleanString(key)
        val file = diskLruCache.get(cleanString)
        if (file != null) {
            return cleanString
        }
        return ""
    }

    fun cleanString(input: String): String {
        val regex = Regex("[^a-z0-9_-]")
        val cleaned = regex.replace(input, "")
        return if (cleaned.length > 64) {
            cleaned.substring(0, 64)
        } else {
            cleaned
        }
    }


    // key的组成逻辑:文件size+文件最后一次修改时间+文件名
    @JvmStatic
     fun writeDiskLruCache(key: String, bitmapBytes: ByteArray?):String {
        val cleanString = cleanString(key)

        val editor = diskLruCache.edit(cleanString);
        if (editor != null && bitmapBytes != null) {
            val os = editor.newOutputStream(0);
            // 写入数据的逻辑，例如图片的字节数据
            os.write(bitmapBytes);
            os.close();
            editor.commit();
            return cleanString
        } else {
            diskLruCache.remove(cleanString); // 如果编辑失败，可能文件已经被删除
            return ""
        }
    }
}