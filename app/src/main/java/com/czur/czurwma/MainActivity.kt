package com.czur.czurwma

import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import com.blankj.utilcode.util.ActivityUtils
import com.czur.czurwma.eshare.EShareActivity
import com.czur.czurwma.utils.AppClearUtils
import com.czur.czurwma.utils.singleClick
import com.scwang.smart.refresh.layout.SmartRefreshLayout

class MainActivity : StarryBaseActivity() {

    private val user_back_btn by lazy {
        findViewById<ImageView>(R.id.user_back_btn)
    }
    private val scan_btn by lazy {
        findViewById<ImageView>(R.id.scan_btn)
    }
    private val refresh_layout by lazy {
        findViewById<SmartRefreshLayout>(R.id.refresh_layout)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.eshare_activity_find_device)
        ActivityUtils.startActivity(EShareActivity::class.java)
//                checkPermission();
//        AppClearUtils.checkESharePermission(this, this, true){
//            finish()
//        }
        user_back_btn.visibility = View.GONE
        scan_btn.visibility = View.VISIBLE

        scan_btn?.singleClick {
            checkPermission()
        }

        //下拉刷新
        refresh_layout?.apply {
            setEnableOverScrollDrag(false)
            setEnableOverScrollBounce(false)
            setEnableRefresh(true)
            setEnableNestedScroll(false)
        }?.setOnRefreshListener {
            checkPermission()
            it.finishRefresh(true)
        }

        finish()
    }

    override fun onResume() {
        super.onResume()
        checkPermission()
    }


    override fun onBackPressed() {
        super.onBackPressed()

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            exitApp()
            true
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

    }

    private fun checkPermission() {
        AppClearUtils.checkESharePermission(this, this, true) {
            if (it){
                ActivityUtils.startActivity(EShareActivity::class.java)
                finish()
            }

        }
    }


}



