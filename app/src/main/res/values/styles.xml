<resources>
    <!--BaseActivity全屏主题-->
    <!--BaseActivity全屏主题-->
    <!--BaseActivity全屏主题-->

    <style name="CZURAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorAccent">@color/edittext_cursor_blue_color</item>
    </style>

    <!--闪屏页主题-->

    <!-- 主页RadioButton-->


    <!-- 登录页面底部弹出框-->

    <style name="TranslucentTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- 登录页面底部弹出框动画-->


    <!-- 错误坐姿popmenu -->

    <!--对话框样式-->

    <!-- Starry Meeting -->

    <!-- member list bottom button style-->

    <style name="eshare_bottom_line" >
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="eshare_bottom_pannel" >
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/starryMargin100</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@drawable/eshare_pannel_bg</item>
        <item name="android:padding">@dimen/starryMargin10</item>
    </style>

    <!--  添加新设备  -->

    <style name="paint_color" >
        <item name="android:layout_width">20dp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_margin">@dimen/starryMargin10</item>
        <item name="android:layout_centerInParent">true</item>
    </style>

    <!--闪屏页主题-->
    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="animScaleY">
        <item name="android:windowEnterAnimation">@anim/anim_scale_y_in</item>
        <item name="android:windowExitAnimation">@anim/anim_scale_y_out</item>
    </style>

    <style name="animAlpha">
        <item name="android:windowEnterAnimation">@anim/anim_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/anim_alpha_out</item>
    </style>

    <style name="CustomCheckboxStyle" parent="@android:style/Widget.CompoundButton.CheckBox">

        <!-- 可以添加其他样式属性 -->
    </style>
</resources>
