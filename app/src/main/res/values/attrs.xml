<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ColorArcProgressBar">
        <attr name="back_color" format="color" />
        <attr name="front_color1" format="color" />
        <attr name="front_color2" format="color" />
        <attr name="front_color3" format="color" />
        <attr name="back_width" format="dimension" />
        <attr name="front_width" format="dimension" />
        <attr name="arcWidth" format="dimension" />
        <attr name="total_engle" format="integer" />
        <attr name="current_value" format="float" />
        <attr name="max_value" format="float" />

    </declare-styleable>
    <declare-styleable name="MagicProgressCircle">
        <attr name="mpc_percent" format="float" />
        <attr name="mpc_stroke_width" format="dimension" />
        <attr name="mpc_start_color" format="color" />
        <attr name="mpc_end_color" format="color" />
        <!-- 背景环的颜色 -->
        <attr name="mpc_default_color" format="color" />
        <!-- 圆弧: 结尾是否覆盖开始 -->
        <attr name="mpc_foot_over_head" format="boolean" />
    </declare-styleable>

    <declare-styleable name="SlideLockView">
        <attr name="lock_drawable" format="reference" />
        <attr name="lock_radius" format="dimension|reference" />
        <attr name="lock_tips_tx" format="string|reference" />
        <attr name="locl_tips_tx_size" format="dimension|reference" />
        <attr name="lock_tips_tx_color" format="color|reference" />
    </declare-styleable>
    <declare-styleable name="SlideView">
        <attr name="sv_slideBackgroundColor" format="color" />
        <attr name="sv_buttonBackgroundColor" format="color" />
        <attr name="sv_buttonImage" format="reference" />
        <attr name="sv_strokeColor" format="color" />
        <attr name="sv_slideText" format="string" />
        <attr name="sv_slideTextColor" format="color" />
        <attr name="sv_animateSlideText" format="boolean" />
        <attr name="sv_slideTextSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="wave">
        <attr name="waveNumber" format="integer" />
        <attr name="minRadius" format="float" />
        <attr name="maxRadius" format="float" />
        <attr name="waveInterval" format="float" />
        <attr name="waveColor" format="color" />
        <attr name="BgColor" format="color" />
        <attr name="waveAlpha" format="integer" />
        <!--speed越小,速度越慢-->
        <attr name="speed" format="float" />
    </declare-styleable>


    <declare-styleable name="RoundedRectProgressBar">
        <attr name="backColor" format="color" />
        <attr name="barColor" format="color" />
    </declare-styleable>
    <declare-styleable name="AuraMateProgressBar">
        <attr name="lineWidth" format="dimension" />
        <attr name="backColors" format="color" />
        <attr name="barColors" format="color" />
    </declare-styleable>

    <declare-styleable name="HeadCropImageView">
        <attr name="cropMaskColor" format="color" />
        <attr name="cropBorderColor" format="color" />
        <attr name="cropBorderWidth" format="dimension" />
        <attr name="cropFocusWidth" format="dimension" />
        <attr name="cropFocusHeight" format="dimension" />
        <attr name="cropStyle" format="enum">
            <enum name="rectangle" value="0" />
            <enum name="circle" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="RingProgressBar">
        <attr name="ringColor" format="color" />
        <attr name="ringProgressColor" format="color" />
        <attr name="ringWidth" format="dimension" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="max" format="integer" />
        <attr name="textIsShow" format="boolean" />
        <attr name="progress" format="integer" />
        <attr name="ringPadding" format="dimension" />
        <attr name="style">
            <enum name="STROKE" value="0" />
            <enum name="FILL" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="RippleAnimationView">
        <attr name="ripple_anim_scale" format="float" />
        <attr name="ripple_anim_color" format="color" />
        <attr name="ripple_anim_amount" format="integer" />
        <attr name="ripple_anim_duration" format="integer" />
        <attr name="ripple_anim_radius" format="dimension" />
        <attr name="ripple_anim_strokeWidth" format="dimension" />
        <attr name="ripple_anim_type" format="enum">
            <enum name="fillRipple" value="0" />
            <enum name="strokeRipple" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="ProgressButton">
        <attr name="progress_btn_tv" format="string" />
        <attr name="progress_btn_color" format="color" />
        <attr name="progress_btn_background" format="reference" />
    </declare-styleable>


    <declare-styleable name="BubbleSeekBar">
        <attr name="bsb_min" format="float|reference" />
        <!-- min < max, default: 0.0f -->
        <attr name="bsb_max" format="float|reference" />
        <!-- min < max, default: 100.0f -->
        <attr name="bsb_progress" format="float|reference" />
        <!-- real time progress value, default: min -->
        <attr name="bsb_is_float_type" format="boolean" />
        <!-- support for float type -->
        <attr name="bsb_track_size" format="dimension|reference" />
        <!--
       height of right-track(on the right of thumb), default: 2dp
       -->
        <!--
       height of left-track(on the left of thumb), default: 2dp higher than right-track's height
       -->
        <attr name="bsb_second_track_size" format="dimension|reference" />
        <attr name="bsb_thumb_radius" format="dimension|reference" />
        <!--
       radius of thumb, default: 2dp higher than left-track's height
       -->
        <!--
       radius of thumb when be dragging, default: 2 times of left-track's height
       -->
        <attr name="bsb_thumb_radius_on_dragging" format="float|reference" />
        <attr name="bsb_track_color" format="color|reference" />
        <!--
       color of right-track, default: R.color.colorPrimary
       -->
        <attr name="bsb_second_track_color" format="color|reference" />
        <!-- color of left-track, default: R.color.colorAccent -->
        <attr name="bsb_thumb_color" format="color|reference" />
        <!--
       color of thumb, default: same as left-track's color
       -->
        <attr name="bsb_section_count" format="integer|reference" />
        <!-- shares of whole progress(max - min), default: 10 -->
        <attr name="bsb_show_section_mark" format="boolean" />
        <!-- show demarcation points or not, default: false -->
        <attr name="bsb_auto_adjust_section_mark" format="boolean" />
        <!--
       auto scroll to the nearest section_mark or not, default: false
       -->
        <attr name="bsb_show_section_text" format="boolean" />
        <!-- show section-text or not, default: false -->
        <attr name="bsb_section_text_size" format="dimension|reference" />
        <!-- text size of section-text, default: 14sp -->
        <attr name="bsb_section_text_color" format="color|reference" />
        <!--
       text color of section-text, default: same as right-track's color
       -->
        <!--
       text position of section-text relative to track, sides, bottom_sides, below_section_mark, default: sides
       -->
        <attr name="bsb_section_text_position">
            <enum name="sides" value="0" />
            <enum name="bottom_sides" value="1" />
            <enum name="below_section_mark" value="2" />
        </attr>
        <attr name="bsb_section_text_interval" format="integer" />
        <!-- the interval of two section-text, default: 1 -->
        <attr name="bsb_show_thumb_text" format="boolean" />
        <!--
       show real time progress-text under thumb or not, default: false
       -->
        <attr name="bsb_thumb_text_size" format="dimension|reference" />
        <!-- text size of progress-text, default: 14sp -->
        <attr name="bsb_thumb_text_color" format="color|reference" />
        <!--
       text color of progress-text, default: same as left-track's color
       -->
        <attr name="bsb_show_progress_in_float" format="boolean" />
        <!--
       show bubble-progress in float or not, default: false
       -->
        <attr name="bsb_touch_to_seek" format="boolean" />
        <!--
       touch anywhere on track to quickly seek, default: false
       -->
        <attr name="bsb_seek_step_section" format="boolean" />
        <!--
       seek one step by one section, the progress is discrete, default: false
       -->
        <attr name="bsb_seek_by_section" format="boolean" />
        <!--
       seek by section, the progress may not be linear, default: false
       -->
        <attr name="bsb_bubble_color" format="color|reference" />
        <!--
       color of bubble, default: same as left-track's color
       -->
        <attr name="bsb_bubble_text_size" format="dimension|reference" />
        <!-- text size of bubble-progress, default: 14sp -->
        <attr name="bsb_bubble_text_color" format="color|reference" />
        <!-- text color of bubble-progress, default: #ffffffff -->
        <attr name="bsb_anim_duration" format="integer" />
        <!-- duration of animation, default: 200ms -->
        <attr name="bsb_always_show_bubble" format="boolean" />
        <!-- bubble shows all time, default: false -->
        <attr name="bsb_always_show_bubble_delay" format="integer" />
        <!--
       the delay duration before bubble shows all the time, default: 200ms
       -->
        <attr name="bsb_hide_bubble" format="boolean" />
        <!-- hide bubble, default: false -->
        <attr name="bsb_rtl" format="boolean" />
        <!-- right to left, default: false -->


        <attr name="bsb_thumb_fill_color" format="color|reference" />
        <attr name="bsb_section_first_color" format="color|reference" />
        <attr name="bsb_section_second_color" format="color|reference" />
        <!-- 拖动圆填充的颜色-->
        <attr name="android:enabled" />
    </declare-styleable>


    <declare-styleable name="MaxHeightRecyclerView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="RoundProgress">
        <attr name="bgColor" format="color" />
        <attr name="roundWidth" format="dimension" />
        <attr name="textColor1" format="color" />
        <attr name="textSize1" format="dimension" />
        <attr name="maxProgress" format="integer" />
        <attr name="textIsDisplayable" format="boolean" />
        <attr name="lineColor" format="color" />
    </declare-styleable>

    <declare-styleable name="downloadProgressBar">
        <attr name="dptextsize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SearchEditView">
        <attr name="search_imagewidth" format="float|reference" />
        <attr name="search_textColor" format="color|reference" />
        <attr name="search_textSize" format="color|reference" />
        <!-- 拖动圆填充的颜色-->
        <attr name="android:enabled" />
    </declare-styleable>

    <declare-styleable name="AutoEditText">
        <attr name="android:text" />
        <attr name="android:hint" />
        <attr name="android:inputType" />
        <attr name="maxLength" format="integer" />
        <attr name="minLength" format="integer" />
        <attr name="tipOver" format="string" />
        <attr name="tipShort" format="string" />
        <attr name="tipEmpty" format="string" />
        <attr name="banEmoji" format="boolean" />
        <attr name="rightIconSrc" format="reference" />
        <attr name="rightIconClick" format="string" />
    </declare-styleable>

    <declare-styleable name="LoadingButton">
        <attr name="pbText" format="string" />
        <attr name="pbLoadingText" format="string" />
        <attr name="pbTextSize" format="dimension" />
        <attr name="pbTextColor" format="color|reference" />
        <attr name="pbProgressColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="CircleView">
        <attr name="circleColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="BaseLib_WordItem">
        <attr name="baselib_bg_color" format="color" />
        <attr name="baselib_tv_color" format="color" />
    </declare-styleable>

    <declare-styleable name="HeadImageView">
        <!--   文字缩放比例     -->
        <attr name="text_scale" format="float" />
        <!--   头像的背景    -->
        <attr name="head_img_theme">
            <enum name="def" value="1" />
            <enum name="white" value="2" />
        </attr>
    </declare-styleable>


    <declare-styleable name="ESCheckBox">
        <attr name="checkedImage" format="reference" />
        <attr name="uncheckedImage" format="reference" />
        <attr name="defaultCheckStatus" format="boolean" />
    </declare-styleable>
</resources>