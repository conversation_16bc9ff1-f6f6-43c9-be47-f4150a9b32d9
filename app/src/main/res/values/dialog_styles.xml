<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Dialog -->
    <style name="TransparentDialog" parent="android:Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>

    <style name="TransparentProgressDialog" parent="TransparentDialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

</resources>
