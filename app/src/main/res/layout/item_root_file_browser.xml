<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/local_file_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/icon_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_local_files"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/file_name_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:layout_marginEnd="45dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/local_files"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imageView3"
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/line_view"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:background="@color/white" />
</LinearLayout>