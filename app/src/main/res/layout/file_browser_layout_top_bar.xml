<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starry_top_bar_height"
    android:background="@color/transparent">


    <ImageView
        android:id="@+id/user_back_btn"
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="@dimen/starryMargin40"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:src="@mipmap/eshare_top_back"
        app:tint="@color/black_22" />


    <TextView
        android:id="@+id/user_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:ellipsize="end"
        android:maxEms="10"
        android:maxLines="1"
        android:text="@string/eshare_title"
        android:textColor="@color/black_22"
        android:textSize="@dimen/starryTopTitleSize" />


    <ImageView
        android:id="@+id/user_more_btn"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:background="@mipmap/starry_more_btn"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/scan_btn"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="60dp"
        android:background="@mipmap/eshare_scan"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/add_file_iv"
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="@dimen/starryMargin40"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="15dp"
        android:paddingHorizontal="5dp"
        android:paddingVertical="10dp"
        android:src="@mipmap/ic_add_file"
        android:visibility="gone" />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_top_bar_done_btn"
        android:textColor="@color/starry_top_btn_blue"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="gone" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_top_bar_delete_btn"
        android:textColor="@color/starry_delete_red"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="gone" />

    <ImageView
        android:layout_width="@dimen/starryMargin35"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin8"
        android:src="@mipmap/starry_company_contact_list_detail"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/red_pont_fl"
        android:layout_width="5dp"
        android:layout_height="5dp"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/eshare_circle_color_red_point"
        android:visibility="gone" />
</RelativeLayout>

