<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starryMargin50"
    android:orientation="horizontal"
    android:background="@drawable/eshare_btn_bg"
    android:padding="@dimen/starryMargin5"
    android:gravity="center_vertical">

    <ImageView
        android:layout_width="@dimen/starryMargin20"
        android:layout_height="@dimen/starryMargin20"
        android:layout_marginStart="@dimen/starryMargin10"
        android:src="@mipmap/eshare_list_icon"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/starryMargin10"
        android:orientation="vertical"
        android:layout_weight="1">

        <TextView
        android:id="@+id/device_name"
        android:textColor="@color/eshare_text_color"
        android:textSize="@dimen/starryFontSize18"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        tools:text="FELIX"
        />

        <TextView
        android:id="@+id/device_ip"
        android:textColor="@color/eshare_text_color"
        android:textSize="@dimen/starryFontSize12"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="*************"
        />

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:textColor="@color/eshare_text_color"
        android:textSize="@dimen/starryFontSize18"
        android:gravity="center"
        android:text="@string/eshare_connect"
        android:layout_marginEnd="@dimen/starryMargin10"
        />

</LinearLayout>