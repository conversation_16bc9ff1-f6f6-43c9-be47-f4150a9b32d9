<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="14sp"
                tools:text="提示" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="13sp"
                tools:text="内容" />


        </RelativeLayout>

        <EditText
            android:id="@+id/edt"
            android:layout_width="210dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/dialog_rec_1_bg_with_white_with_stroke"
            android:maxLength="20"
            android:paddingStart="3dp"
            android:singleLine="true"
            android:textColor="@color/black_22"
            android:textSize="12sp"
            android:visibility="visible"
            android:textColorHint="@color/starry_title_gray"
            android:textCursorDrawable="@drawable/edittext_cursor_blue"  />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5" />
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/title_black_color"
                android:textSize="@dimen/starryDlgBtnSize"
                tools:text="取消"
                android:textStyle="bold"/>

            <View
                android:id="@+id/middle_line"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e5" />

            <TextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textColor="@color/blue_positive_button"
                android:textSize="@dimen/starryDlgBtnSize"
                tools:text="确定"
                android:textStyle="bold"/>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>