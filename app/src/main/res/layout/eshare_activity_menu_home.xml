<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gary_f9"
        >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:layout_marginTop="0.5dp"
            android:background="@color/gary_ff"
            >

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_user"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_privacy"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="visible">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_privacy"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/feedback_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="visible">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/feedback"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/user_menu_about_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="visible">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_about"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />

            <com.czur.czurwma.widget.MediumBoldTextView
                android:id="@+id/update_new_version_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/starryMargin40"
                android:text="@string/update_apk_new_version"
                android:textColor="@color/red_update"
                android:textSize="@dimen/starryFontSize16"
                android:visibility="gone"/>
            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_info"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="gone">


            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/user_privacy_info"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="20dp"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/user_menu_privacy_rl_share"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="gone">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/user_privacy_share"
                android:textColor="@color/black_22"
                android:textSize="15sp" />
            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>
</RelativeLayout>

