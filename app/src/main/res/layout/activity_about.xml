<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gray_f8f8f8">

    <ImageView
        android:id="@+id/imageView2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:scaleType="fitEnd"
        android:src="@mipmap/logo_bg_3x"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />


    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="41dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/about_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <TextView
            android:id="@+id/about_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/user_about"
            android:textColor="@color/account_title"
            android:textSize="18sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <LinearLayout
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/imageView2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/czur_icon"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:background="@mipmap/app_logo_round"/>

        <LinearLayout
            android:layout_marginTop="@dimen/starryMargin20"
            android:id="@+id/linearLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="2dp"
                android:text="@string/about_info"
                android:textColor="@color/gray_39"
                android:textSize="15sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/app_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/gray_39"
                android:textSize="15sp"
                android:textStyle="bold" />
        </LinearLayout>


        <RelativeLayout
            android:layout_marginTop="@dimen/starryMargin20"
            android:id="@+id/about_update_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingBottom="@dimen/dp_5"
            android:visibility="gone">

            <TextView
                android:id="@+id/about_new_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/update_apk_msg"
                android:textColor="@color/red_update"
                android:textSize="14sp"
                android:textStyle="bold"/>

            <View
                android:id="@+id/underline"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                android:layout_below="@id/about_new_version"
                android:layout_alignStart="@id/about_new_version"
                android:layout_alignLeft="@id/about_new_version"
                android:layout_alignEnd="@id/about_new_version"
                android:layout_alignRight="@id/about_new_version"
                android:background="@color/red_update" />


        </RelativeLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>


