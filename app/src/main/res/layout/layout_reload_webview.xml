<?xml version="1.0" encoding="utf-8"?>


<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/reload_webview_rl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:layout_width="90dp"
            android:layout_height="64dp"
            android:src="@mipmap/webview_error_icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="31.5dp"
            android:text="@string/network_error"
            android:textColor="@color/black_22"
            android:textSize="15sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/check_network"
            android:textColor="@color/gray_65"
            android:textSize="12sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/reload_btn"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:layout_marginTop="31.5dp"
            android:background="@drawable/btn_rec_5_bg_webview"
            android:gravity="center"
            android:text="@string/reload"
            android:textColor="@color/gray_93"
            android:textSize="15sp"
            android:textStyle="bold" />


    </LinearLayout>
</RelativeLayout>
