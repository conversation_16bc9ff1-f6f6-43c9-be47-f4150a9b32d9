<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gray_f5f5f5">

    <include
        android:id="@+id/top_bar"
        layout="@layout/file_browser_layout_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/file_browser_tab_rv"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:background="@color/transparent"
        app:layout_constraintTop_toBottomOf="@id/top_bar"
        tools:ignore="MissingConstraints" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/file_browser_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:layout_marginBottom="20dp"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toTopOf="@id/cancel_tv"
        app:layout_constraintTop_toBottomOf="@id/file_browser_tab_rv" />

    <ImageView
        android:id="@+id/file_browser_folder_status_iv"
        android:layout_width="70dp"
        android:layout_height="60dp"
        android:src="@mipmap/ic_empty_folder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.43"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/file_browser_folder_status_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/browser_folder_locked_tips"
        android:textColor="@color/gray_39"
        android:textSize="15sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/file_browser_folder_status_iv" />

    <TextView
        android:id="@+id/cancel_tv"
        android:layout_width="130dp"
        android:layout_height="35dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/eshare_circle_color_grey_6dp"
        android:gravity="center"
        android:textSize="15sp"
        android:text="@string/cancel_btn"
        android:textColor="@color/eshare_common_bg"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/start_tv"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/start_tv"
        android:layout_width="130dp"
        android:layout_height="35dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/eshare_circle_dark_blue_6dp"
        android:gravity="center"
        android:text="@string/start_download_btn"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/cancel_tv" />
</androidx.constraintlayout.widget.ConstraintLayout>