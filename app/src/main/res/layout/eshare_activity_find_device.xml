<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/logo_background"
    android:fitsSystemWindows="true"
    >

    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_top_bar" />

    <LinearLayout
        android:id="@+id/eshare_search_ll"
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_below="@+id/eshare_layout_top_bar_rl"
        android:layout_height="@dimen/starryMargin40"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:layout_marginTop="@dimen/starryMargin10"
        android:visibility="gone"
        >

        <EditText
            android:id="@+id/eshare_pincode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:background="@drawable/eshare_btn_bg"
            android:hint="@string/eshare_input_address"
            android:paddingStart="@dimen/starryMargin10"
            android:maxLength="15"
            android:singleLine="true"
            android:textCursorDrawable="@drawable/eshare_cursor_color_white"
            android:textColor="@color/eshare_text_color"
            android:textColorHint="@color/eshare_text_gray"
            android:imeOptions="actionGo"
            android:textSize="@dimen/starryFontSize16" />

        <ImageView
            android:id="@+id/eshare_connect"
            android:layout_width="@dimen/starryMargin40"
            android:layout_height="@dimen/starryMargin40"
            android:padding="@dimen/starryMargin10"
            android:background="@drawable/eshare_btn_bg"
            android:src="@mipmap/eshare_arrow"
            android:layout_marginStart="@dimen/starryMargin5" />

        <ImageView
            android:id="@+id/eshare_scan"
            android:layout_width="@dimen/starryMargin40"
            android:layout_height="@dimen/starryMargin40"
            android:padding="@dimen/starryMargin10"
            android:background="@drawable/eshare_btn_bg"
            android:src="@mipmap/eshare_scan"
            android:layout_marginStart="@dimen/starryMargin5" />

    </LinearLayout>



    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/eshare_search_ll"
        android:adjustViewBounds="true"
        android:paddingBottom="@dimen/starryMargin10">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:gravity="center"
            app:srlDrawableMarginRight="-10dp"
            app:srlTextFailed="@string/starry_reflash_fail"
            app:srlTextFinish="@string/starry_reflash_success"
            app:srlAccentColor="@color/white"
            app:srlTextLoading=""
            app:srlTextNothing=""
            app:srlTextPulling=""
            app:srlTextRefreshing=""
            app:srlTextRelease=""
            app:srlTextUpdate="" />

        <ListView
            android:id="@+id/lv_devices_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:divider="@color/transparent"
            android:paddingBottom="@dimen/starryMargin10"
            android:scrollbars="none" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/eshare_scaning_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingBottom="@dimen/starryMargin50"
        android:layout_centerInParent="true">

        <ImageView
            android:layout_width="@dimen/starryMargin80"
            android:layout_height="@dimen/starryMargin80"
            android:src="@mipmap/eshare_logo_scan" />

        <TextView
            android:id="@+id/eshare_scaning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/starryMargin20"
            android:text="@string/eshare_scaning"
            android:textColor="@color/eshare_text_gray"
            android:textSize="@dimen/starryFontSize16" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="@dimen/starryMargin20"
            android:text="@string/eshare_scaning_remind_wifi"
            android:textColor="@color/white"
            android:textSize="@dimen/starryFontSize16" />


    </LinearLayout>

    <include
        layout="@layout/starry_no_network_layout"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:layout_below="@+id/eshare_layout_top_bar_rl"
        android:visibility="gone"
        />


</RelativeLayout>