<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/gray_f5f5f5"
    android:paddingStart="22dp"
    android:paddingTop="10dp"
    android:paddingEnd="22dp"
    android:paddingBottom="10dp">

    <com.czur.czurwma.widget.IconImageView
        android:id="@+id/file_type_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="45dp"
        android:textColor="@color/black_22"
        android:textSize="16sp"
        android:ellipsize="middle"
        android:singleLine="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.015"
        app:layout_constraintStart_toEndOf="@id/file_type_iv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="文件名34534534534534453455675675675675675675675673453" />

    <ImageView
        android:id="@+id/arrow_iv"
        android:layout_width="8dp"
        android:layout_height="14dp"
        android:layout_marginEnd="5dp"
        android:rotation="180"
        android:src="@mipmap/eshare_top_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/black_22" />

    <ImageView
        android:id="@+id/selected_iv"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginEnd="5dp"
        android:src="@mipmap/ic_deselected"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>