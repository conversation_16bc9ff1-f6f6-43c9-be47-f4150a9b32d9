<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingTop="6dp"
    android:paddingBottom="6dp"
    android:paddingEnd="6dp">


    <ImageView
        android:id="@+id/arrow_iv"
        android:layout_width="8dp"
        android:layout_height="14dp"
        android:rotation="180"
        android:src="@mipmap/eshare_top_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/gray_c0" />

    <TextView
        android:id="@+id/file_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:background="@drawable/eshare_circle_color_grey_20dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        android:maxLines="1"
        android:ellipsize="middle"
        android:maxWidth="130dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/arrow_iv"
        app:layout_constraintTop_toTopOf="parent"

        tools:text="文件名13" />

</androidx.constraintlayout.widget.ConstraintLayout>