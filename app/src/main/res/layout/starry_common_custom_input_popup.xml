<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/eshare_circle_color_white_20dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/eshare_dialog_code_tips"
                android:textColor="@color/black_22"
                android:textSize="15sp"
                tools:text="请输入传输校验码" />

            <com.czur.czurwma.widget.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="7dp"
                android:gravity="center"
                android:text="@string/eshare_dialog_check_in_starry"
                android:textColor="@color/black_22"
                android:textSize="12sp" />


        </RelativeLayout>

        <EditText
            android:id="@+id/edt"
            android:layout_width="230dp"
            android:layout_height="30dp"
            android:background="@drawable/eshare_circle_color_gray_ed"
            android:gravity="center"
            android:inputType="text"
            android:maxLength="20"
            android:paddingStart="3dp"
            android:singleLine="true"
            android:textColor="@color/black_22"
            android:textColorHint="@color/black_22"
            android:textCursorDrawable="@drawable/edittext_cursor_blue"
            android:textSize="12sp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/error_code_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:textSize="8sp"
            android:textStyle="bold"
            android:textColor="@color/red_d54146"
            android:text="@string/eshare_transmit_file_dialog_code_wrong"
            android:visibility="gone"
            />

        <!-- button -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:layout_marginBottom="10dp">

            <TextView
                android:id="@+id/negative_button"
                android:layout_width="105dp"
                android:layout_height="30dp"
                android:background="@drawable/eshare_circle_color_gray_ed"
                android:gravity="center"
                android:textColor="@color/title_black_color"
                android:textSize="12sp"
                android:text="@string/starry_common_dialog_no2"
                app:layout_constraintEnd_toStartOf="@+id/positive_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="取消" />


            <TextView
                android:id="@+id/positive_button"
                android:layout_width="105dp"
                android:layout_height="30dp"
                android:background="@drawable/eshare_circle_color_blue_5dp"
                android:gravity="center"
                android:text="@string/starry_common_dialog_ok"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/negative_button"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="确定" />


        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="45dp"-->
        <!--            android:orientation="horizontal">-->

        <!--            <TextView-->
        <!--                android:id="@+id/negative_button"-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_weight="1"-->
        <!--                android:gravity="center"-->
        <!--                android:textColor="@color/title_black_color"-->
        <!--                android:textSize="@dimen/starryDlgBtnSize"-->
        <!--                tools:text="取消"-->
        <!--                android:textStyle="bold"/>-->

        <!--            <View-->
        <!--                android:id="@+id/middle_line"-->
        <!--                android:layout_width="0.5dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:background="@color/gray_e5" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/positive_button"-->
        <!--                android:layout_width="0dp"-->
        <!--                android:layout_height="match_parent"-->
        <!--                android:layout_weight="1"-->
        <!--                android:gravity="center"-->
        <!--                android:text=""-->
        <!--                android:textColor="@color/blue_positive_button"-->
        <!--                android:textSize="@dimen/starryDlgBtnSize"-->
        <!--                tools:text="确定"-->
        <!--                android:textStyle="bold"/>-->
        <!--        </LinearLayout>-->
    </LinearLayout>
</RelativeLayout>