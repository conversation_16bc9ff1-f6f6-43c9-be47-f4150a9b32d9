<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starry_top_bar_height"
    android:background="@color/gary_ff">

    <ImageView
        android:id="@+id/user_back_btn"
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="@dimen/starryMargin40"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:src="@mipmap/login_back_icon" />
    <ImageView
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="@dimen/starryMargin30"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin20"
        android:padding="@dimen/starryMargin8"
        android:background="@drawable/circle_with_gray_f2"
        android:src="@mipmap/login_back_icon"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        >

        <ImageView
            android:layout_width="@dimen/starryMargin30"
            android:layout_height="@dimen/starryMargin40"
            android:padding="@dimen/starryMargin5"
            android:src="@mipmap/starry_home_comapny_icon"
            android:visibility="gone"/>

        <com.czur.czurwma.widget.MediumBoldTextView
            android:id="@+id/user_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/account_title"
            android:text=""
            android:maxLines="1"
            android:ellipsize="end"
            android:maxEms="10"
            android:textSize="@dimen/starryTopTitleSize" />
    </LinearLayout>

    <ImageView
        android:layout_width="@dimen/starryMargin35"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:src="@mipmap/starry_user_add"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/user_more_btn"
        android:layout_width="@dimen/starryMargin40"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:src="@mipmap/starry_user_more3_btn"
        android:visibility="gone" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_top_bar_detail_btn"
        android:textColor="@color/starry_top_btn_blue"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="gone"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_top_bar_done_btn"
        android:textColor="@color/starry_top_btn_blue"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="gone"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:text="@string/starry_top_bar_delete_btn"
        android:textColor="@color/starry_delete_red"
        android:textSize="@dimen/starryTopBtnSize"
        android:visibility="gone"/>

    <ImageView
        android:layout_width="@dimen/starryMargin35"
        android:layout_height="@dimen/starryMargin35"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin8"
        android:src="@mipmap/starry_company_contact_list_detail"
        android:visibility="gone"/>

</RelativeLayout>

