<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center"
    android:clickable="false"
    >

    <LinearLayout
        android:id="@+id/starry_custom_toast_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/btn_rec_5_bg_with_gray_38"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/starryMargin30"
        android:paddingTop="@dimen/dp10"
        android:paddingEnd="@dimen/starryMargin30"
        android:paddingBottom="@dimen/dp10"
        tools:ignore="UselessParent">


        <ImageView
            android:id="@+id/image"
            android:layout_width="@dimen/starryMargin10"
            android:layout_height="@dimen/starryMargin10"
            android:src="@drawable/circle_with_blue" />

        <TextView
            android:id="@+id/message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_5"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:ellipsize="middle"
            android:maxLines="1"
            android:gravity="center"
            android:layout_gravity="center"
            tools:text="这是一条来电提醒消息这是一条来电提醒消息这是一条来电提醒消息" />

    </LinearLayout>
</RelativeLayout>