<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_10_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/starryMargin20"
            >

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/eshare_input_title"
                android:textColor="@color/black_22"
                android:textSize="@dimen/starryFontSize16" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="14sp"
                android:visibility="gone"/>

            <EditText
                android:id="@+id/eshare_pincode"
                android:layout_width="match_parent"
                android:layout_height="@dimen/starryMargin40"
                android:layout_below="@+id/title"
                android:layout_marginTop="@dimen/starryMargin20"
                android:background="@drawable/eshare_input_bg"
                android:maxLength="15"
                android:padding="@dimen/starryMargin8"
                android:singleLine="true"
                android:textAlignment="center"
                android:textCursorDrawable="@drawable/edittext_cursor_blue"
                android:textSize="@dimen/starryFontSize16" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"/>

        <!-- button -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin40"
            android:orientation="horizontal"
            android:layout_margin="@dimen/starryMargin20"
            >

            <TextView
                android:id="@+id/button_no"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/starry_common_dialog_no2"
                tools:text = "取消"
                android:background="@drawable/eshare_input_bg"
                android:textColor="@color/TextGray"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/button_yes"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@drawable/eshare_input_ok_bg"
                android:text="@string/starry_common_dialog_ok2"
                tools:text = "确定"
                android:layout_marginStart="@dimen/starryMargin20"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>
</RelativeLayout>