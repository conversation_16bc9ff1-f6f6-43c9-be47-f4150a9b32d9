<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/eshare_paint_switch"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <com.czur.czurwma.eshare.view.SwitchView
                android:id="@+id/eshare_paint_switch"
                android:layout_width="50dp"
                android:layout_height="26dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:switchMinWidth="@dimen/starryMargin20"
                android:layout_marginRight="@dimen/starryMargin20"
                android:checked="true" />

        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>

