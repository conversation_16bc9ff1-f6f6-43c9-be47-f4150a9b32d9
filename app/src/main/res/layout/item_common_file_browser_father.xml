<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/icon_iv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/eshare_ic_add_pen"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="初始值"
        app:layout_constraintStart_toEndOf="@id/icon_iv"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>