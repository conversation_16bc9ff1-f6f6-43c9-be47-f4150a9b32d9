<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/eshare_common_bg">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_cl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_top_bar_height"
        app:layout_constraintTop_toTopOf="parent">


    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:id="@+id/user_back_btn"
        android:layout_width="@dimen/starryMargin30"
        android:layout_height="@dimen/starryMargin40"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/starryMargin10"
        android:padding="@dimen/starryMargin10"
        android:src="@mipmap/eshare_top_back"
        app:layout_constraintBottom_toBottomOf="@+id/title_cl"
        app:layout_constraintStart_toStartOf="@+id/title_cl"
        app:layout_constraintTop_toTopOf="@+id/title_cl" />


    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/title_cl"
        app:layout_constraintEnd_toEndOf="@+id/title_cl"
        app:layout_constraintStart_toStartOf="@+id/title_cl"
        app:layout_constraintTop_toTopOf="@+id/title_cl">

        <ImageView
            android:layout_width="@dimen/starryMargin10"
            android:layout_height="@dimen/starryMargin10"
            android:layout_margin="@dimen/starryMargin5"
            android:src="@drawable/eshare_circle_with_blue" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/eshare_connect_start"
            android:textColor="@color/eshare_text_color"
            android:textSize="@dimen/starryFontSize12" />
    </LinearLayout>


    <TextView
        android:id="@+id/eshare_device_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="5dp"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:text="StarryHub"
        android:textColor="@color/eshare_text_color"
        android:textSize="24sp"
        app:layout_constraintEnd_toStartOf="@+id/eshare_disconnect_cl"
        app:layout_constraintStart_toEndOf="@+id/eshare_disconnect_cl_front"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout2" />

    <!--    下面有一个相同布局  为了让控件左右都缩进一样的距离-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eshare_disconnect_cl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="27dp"
        android:padding="5dp"
        app:layout_constraintBottom_toBottomOf="@+id/eshare_device_name_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/linearLayout2">

        <ImageView
            android:id="@+id/user_more_btn"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@mipmap/eshare_disconnect"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/duankai_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/eshare_disconnect"
            android:textColor="@color/eshare_text_color"
            android:textSize="@dimen/starryFontSize12"
            app:layout_constraintEnd_toEndOf="@+id/user_more_btn"
            app:layout_constraintStart_toStartOf="@+id/user_more_btn"
            app:layout_constraintTop_toBottomOf="@+id/user_more_btn" />
    </androidx.constraintlayout.widget.ConstraintLayout>


<!--    这个布局为了让控件左右都缩进一样的距离-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/eshare_disconnect_cl_front"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="27dp"
        android:clickable="false"
        android:focusable="false"
        android:padding="5dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/eshare_device_name_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/linearLayout2">

        <ImageView
            android:id="@+id/user_more_btn_front"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@mipmap/eshare_disconnect"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/duankai_tv_front"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:clickable="false"
            android:focusable="false"
            android:text="@string/eshare_disconnect"
            android:textColor="@color/eshare_text_color"
            android:textSize="@dimen/starryFontSize12"
            app:layout_constraintEnd_toEndOf="@+id/user_more_btn_front"
            app:layout_constraintStart_toStartOf="@+id/user_more_btn_front"
            app:layout_constraintTop_toBottomOf="@+id/user_more_btn_front" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

