<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Android 16 适配测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_run_tests"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="运行测试"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_generate_report"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="生成报告"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_test_reflection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="反射测试"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_test_intent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Intent测试"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:padding="12dp">

        <TextView
            android:id="@+id/tv_test_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击上方按钮开始测试..."
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:textColor="@android:color/black" />

    </ScrollView>

</LinearLayout>
