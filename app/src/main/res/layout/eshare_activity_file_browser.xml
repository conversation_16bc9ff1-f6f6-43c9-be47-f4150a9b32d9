<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/logo_background"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    android:id="@+id/file_browser_bg_rl">


    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_top_bar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/root_file_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/eshare_layout_top_bar_rl"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/file_browser_folder_status_iv"
        android:layout_width="70dp"
        android:layout_height="60dp"
        android:src="@mipmap/ic_empty_folder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.43"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/file_browser_folder_status_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/browser_folder_locked_tips"
        android:textColor="@color/gray_39"
        android:textSize="15sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/file_browser_folder_status_iv" />

</androidx.constraintlayout.widget.ConstraintLayout>