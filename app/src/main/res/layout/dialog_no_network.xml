<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:layout_width="96dp"
            android:layout_height="65dp"
            android:src="@mipmap/no_network" />

        <com.czur.czurwma.widget.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_network"
            android:textColor="@color/gray_d8d8d8"
            android:textSize="14sp" />


        <com.czur.czurwma.widget.MediumBoldTextView
            android:id="@+id/tv_click_return"
            android:layout_width="90dp"
            android:layout_height="30dp"
            android:layout_marginTop="34dp"
            android:background="@drawable/btn_rec_5_bg_blue"
            android:gravity="center"
            android:text="@string/auramate_return"
            android:textColor="@color/white" />

    </LinearLayout>


</RelativeLayout>