<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="25dp"
    android:background="@color/transparent">

    <TextView
        android:id="@+id/file_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="middle"
        android:lines="1"
        android:text="集体照1423.jpg"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintHorizontal_bias="0 "
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/end_cl"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="wrap" />



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/end_cl"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:paddingStart="10dp"
        app:layout_constraintEnd_toEndOf="parent">
        <TextView
            android:id="@+id/fail_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/eshare_transmit_file_fail"
            android:layout_marginEnd="6dp"
            android:textColor="@color/eshare_red_fa5c5c"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/error_fl"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/done_iv"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@mipmap/ic_completed"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/error_fl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/delete_iv"
            app:layout_constraintTop_toTopOf="parent" >

            <ImageView
                android:id="@+id/error_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="5dp"
                android:src="@mipmap/ic_error_file"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/delete_iv"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/resume_tv"
                android:layout_width="70dp"
                android:layout_height="20dp"
                android:layout_marginEnd="5dp"
                android:background="@drawable/eshare_circle_color_white_10dp"
                android:gravity="center"
                android:text="@string/transmit_resume_download_btn"
                android:textColor="@color/eshare_common_bg"
                android:textSize="9sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/delete_iv"
                app:layout_constraintTop_toTopOf="parent" />

        </FrameLayout>


<!--        android:layout_width="12dp"-->
<!--        android:layout_height="14dp"-->
        <ImageView
            android:id="@+id/delete_iv"
            android:layout_width="22dp"
            android:layout_height="24dp"
            android:paddingVertical="5dp"
            android:paddingHorizontal="5dp"
            android:src="@mipmap/ic_transmit_delete"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.czurwma.widget.TransmitFileProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="50dp"
            android:layout_height="6dp"
            android:layout_marginEnd="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/delete_iv"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>