<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/white"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/white">

            <ImageView
                android:id="@+id/user_back_btn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/login_back_icon" />

            <TextView
                android:id="@+id/user_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black_24"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/commit_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="5dp"
                android:padding="10dp"
                android:text="@string/commit"
                android:textColor="@color/gray_c4"
                android:textSize="15sp" />
        </RelativeLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/user_mail_ll"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:bl_stroke_color="@color/gray_e1"
        app:bl_stroke_width="0.5dp"
        tools:ignore="MissingPrefix">

        <EditText
            android:id="@+id/user_mail_edt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="10dp"
            android:singleLine="true"
            android:layout_marginRight="17dp"
            android:background="@null"
            android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_@."
            android:hint="@string/input_mail_feedback"
            android:textColor="@color/black_24"
            android:textColorHint="@color/gray_c4"
            android:textSize="15sp" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/user_feedback_ll"
        android:layout_width="match_parent"
        android:layout_height="318dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:bl_stroke_color="@color/gray_e1"
        app:bl_stroke_width="0.5dp"
        tools:ignore="MissingPrefix">
`
        <EditText
            android:id="@+id/user_feedback_edt"
            android:layout_width="match_parent"
            android:layout_height="295dp"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="17dp"
            android:background="@null"
            android:gravity="top"
            android:hint="@string/input_feedback"
            android:textColor="@color/black_24"
            android:textColorHint="@color/gray_c4"
            android:textSize="15sp" />

    </LinearLayout>


</LinearLayout>