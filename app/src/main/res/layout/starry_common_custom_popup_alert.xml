<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp">

            <com.czur.czurwma.widget.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="14sp" />

            <com.czur.czurwma.widget.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_22"
                android:textSize="14sp" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e5"/>

        <!-- button -->
        <TextView
            android:id="@+id/positive_button"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/starry_common_dialog_ok"
            tools:text = "确定"
            android:textStyle="bold"
            android:textColor="@color/blue_positive_button"
            android:textSize="14sp" />

    </LinearLayout>
</RelativeLayout>