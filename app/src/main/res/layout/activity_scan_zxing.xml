<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/eshare_common_bg"
    android:fitsSystemWindows="true"
    >

    <cn.bingoogolapple.qrcode.zxing.ZXingView
        android:id="@+id/zxingview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:qrcv_animTime="1000"
        app:qrcv_barcodeRectHeight="120dp"
        app:qrcv_borderColor="@color/whiteOpaque50"
        app:qrcv_borderSize="1dp"
        app:qrcv_cornerColor="@color/eshare_common_bg"
        app:qrcv_cornerDisplayType="center"
        app:qrcv_cornerLength="20dp"
        app:qrcv_cornerSize="3dp"
        app:qrcv_customGridScanLineDrawable="@drawable/scan_bg"
        app:qrcv_isAutoZoom="true"
        app:qrcv_isBarcode="false"
        app:qrcv_isOnlyDecodeScanBoxArea="false"
        app:qrcv_isScanLineReverse="true"
        app:qrcv_isShowDefaultGridScanLineDrawable="true"
        app:qrcv_isShowDefaultScanLineDrawable="true"
        app:qrcv_isShowLocationPoint="true"
        app:qrcv_isShowTipBackground="true"
        app:qrcv_isShowTipTextAsSingleLine="false"
        app:qrcv_isTipTextBelowRect="false"
        app:qrcv_maskColor="@color/blackOpaque40"
        app:qrcv_qrCodeTipText="@string/eshare_scan_text"
        app:qrcv_rectWidth="200dp"
        app:qrcv_scanLineColor="@color/eshare_common_bg"
        app:qrcv_scanLineMargin="0dp"
        app:qrcv_scanLineSize="2dp"
        app:qrcv_tipBackgroundColor="@color/transparent"
        app:qrcv_tipTextColor="@color/gray_93"
        app:qrcv_tipTextMargin="25dp"
        app:qrcv_tipTextSize="15sp"
        app:qrcv_toolbarHeight="44dp"
        app:qrcv_topOffset="150dp"
        app:qrcv_verticalBias="-1" />

    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_top_bar"
        />

</RelativeLayout>