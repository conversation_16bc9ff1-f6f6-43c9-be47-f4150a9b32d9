<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/eshare_search_ll"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:layout_below="@+id/eshare_layout_top_bar_rl"
    android:layout_height="44dp"
    android:layout_marginStart="@dimen/starryMargin20"
    android:layout_marginEnd="@dimen/starryMargin20"
    android:background="@color/eshare_common_bg"
    android:padding="2dp"
    android:gravity="center_horizontal"
    >

    <TextView
        android:id="@+id/eshare_pincode"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_weight="1"
        android:background="@drawable/eshare_btn_bg"
        android:gravity="center"
        android:imeOptions="actionGo"
        android:maxLength="55"
        android:paddingStart="@dimen/starryMargin10"
        android:singleLine="true"
        android:text="@string/eshare_input_address"
        android:textColor="@color/eshare_text_color"
        android:textColorHint="@color/eshare_text_gray"
        android:textCursorDrawable="@drawable/eshare_cursor_color_white"
        android:textSize="@dimen/starryFontSize16" />

    <ImageView
        android:id="@+id/eshare_connect"
        android:layout_width="@dimen/starryMargin40"
        android:layout_height="@dimen/starryMargin40"
        android:padding="@dimen/starryMargin10"
        android:background="@drawable/eshare_btn_bg"
        android:src="@mipmap/eshare_arrow"
        android:layout_marginStart="@dimen/starryMargin5" />

    <ImageView
        android:id="@+id/eshare_scan"
        android:layout_width="@dimen/starryMargin40"
        android:layout_height="@dimen/starryMargin40"
        android:padding="@dimen/starryMargin10"
        android:background="@drawable/eshare_btn_bg"
        android:src="@mipmap/eshare_scan"
        android:layout_marginStart="@dimen/starryMargin5" />

</LinearLayout>